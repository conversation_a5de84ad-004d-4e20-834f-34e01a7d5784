/**
 * 角色常量
 */
export default {
  // 超级管理员角色
  ADMIN: 'admin',
  
  // 管理员角色
  MANAGER: 'manager',
  
  // 财务角色
  FINANCE: 'finance',
  
  /**
   * 判断是否为管理类角色（admin、manager、finance）
   * 
   * @param {String} roleKey 角色标识
   * @returns {Boolean} 是否为管理类角色
   */
  isManagementRole(roleKey) {
    return roleKey === this.ADMIN || roleKey === this.MANAGER || roleKey === this.FINANCE;
  },
  
  /**
   * 判断用户是否拥有管理类角色（admin、manager、finance）
   * 
   * @param {Array} roles 角色列表
   * @returns {Boolean} 是否拥有管理类角色
   */
  hasManagementRole(roles) {
    if (!roles || roles.length === 0) {
      return false;
    }
    return roles.some(role => this.isManagementRole(role));
  }
}
