<template>
  <div class="app-container">
    <!-- 筛选条件 -->
    <el-card shadow="never" class="filter-container">
      <el-form :model="queryForm" ref="queryRef" :inline="true" label-width="80px">
        <el-form-item label="日期范围" prop="dateRange">
          <el-date-picker
            v-model="queryForm.dateRange"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            format="YYYY-MM-DD"
            value-format="YYYY-MM-DD"
            style="width: 240px;"
          />
        </el-form-item>
        <el-form-item label="门店" prop="storeId">
          <el-select v-model="queryForm.storeId" placeholder="请选择门店" clearable style="width: 150px;">
            <el-option
              v-for="store in storeOptions"
              :key="store.id"
              :label="store.name"
              :value="store.id"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="渠道" prop="channelId" v-if="isAdmin">
          <el-select v-model="queryForm.channelId" placeholder="请选择渠道" clearable style="width: 150px;">
            <el-option
              v-for="channel in channelOptions"
              :key="channel.id"
              :label="channel.name"
              :value="channel.id"
            />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" :loading="loading" @click="handleSearch">搜索</el-button>
          <el-button @click="resetQuery">重置</el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <!-- 渠道信息 -->
    <el-card shadow="never" class="channel-info" v-if="channelName">
      <div class="channel-name">当前渠道：{{ channelName }}</div>
    </el-card>

    <!-- 数据展示区域 -->
    <div class="data-container">

      <!-- 产品销售明细表格 -->
      <el-card shadow="never" class="table-card full-width">
        <template #header>
          <div class="card-header">
            <span>产品销售明细</span>
          </div>
        </template>
        <div class="table-container">
          <el-table
            :data="productDetailsList"
            v-loading="tableLoading"
            height="400"
            style="width: 100%"
          >
            <el-table-column label="商品图片" align="center">
              <template #default="scope">
                <image-preview :src="scope.row.productImage" :width="50" :height="50"/>
              </template>
            </el-table-column>
            <el-table-column label="货号" prop="productNumber" show-overflow-tooltip />
            <el-table-column label="商品名称" prop="productName" show-overflow-tooltip />
            <el-table-column label="品牌" prop="brand" show-overflow-tooltip />
            <el-table-column label="渠道" prop="channelName" show-overflow-tooltip />
            <el-table-column label="门店" prop="storeName" show-overflow-tooltip />
            <el-table-column label="销售数量" align="center">
              <template #default="scope">
                <span>{{ scope.row.totalQuantity }} 件</span>
              </template>
            </el-table-column>
            <el-table-column label="销售时间" align="center">
              <template #default="scope">
                <span>{{ formatDateTime(scope.row.saleTime) }}</span>
              </template>
            </el-table-column>
          </el-table>

          <!-- 分页 -->
          <el-pagination
            v-show="productDetailsTotal > 0"
            :current-page="productDetailsQuery.pageNum"
            :page-size="productDetailsQuery.pageSize"
            :page-sizes="[10, 20, 50, 100]"
            :total="productDetailsTotal"
            background
            layout="total, sizes, prev, pager, next, jumper"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
            style="margin-top: 20px; text-align: center;"
          />
        </div>
      </el-card>

      <!-- 时间销售数据 -->
      <el-card shadow="never" class="chart-card">
        <template #header>
          <div class="card-header">
            <span>渠道销售趋势（数量）</span>
            <div class="time-unit-selector">
              <el-radio-group v-model="timeUnit" size="small" @change="handleTimeUnitChange">
                <el-radio-button label="day">日</el-radio-button>
                <el-radio-button label="month">月</el-radio-button>
                <el-radio-button label="year">年</el-radio-button>
              </el-radio-group>
            </div>
          </div>
        </template>
        <div class="chart-container">
          <div ref="timeSalesChart" class="chart"></div>
        </div>
      </el-card>

      <!-- 商品销售情况 -->
      <el-card shadow="never" class="chart-card">
        <template #header>
          <div class="card-header">
            <span>渠道商品销售排行（数量）</span>
          </div>
        </template>
        <div class="chart-container">
          <div ref="productSalesChart" class="chart"></div>
        </div>
      </el-card>

      <!-- 商品库存统计 -->
      <el-card shadow="never" class="chart-card">
        <template #header>
          <div class="card-header">
            <span>渠道商品库存排行</span>
          </div>
        </template>
        <div class="chart-container">
          <div ref="productInventoryChart" class="chart"></div>
        </div>
      </el-card>
      
      <!-- 门店库存分布 -->
      <el-card shadow="never" class="chart-card">
        <template #header>
          <div class="card-header">
            <span>渠道商品库存门店分布</span>
          </div>
        </template>
        <div class="chart-container">
          <div ref="storeInventoryChart" class="chart"></div>
        </div>
      </el-card>

    </div>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, onBeforeUnmount, getCurrentInstance } from 'vue';
import { useRouter } from 'vue-router';
import { ElMessage } from 'element-plus';
import * as echarts from 'echarts';
import { getChannelStatistics, getChannelProductDetails } from '@/api/analysis/channel';
import useUserStore from '@/store/modules/user';

const router = useRouter();
const { proxy } = getCurrentInstance();
const userStore = useUserStore();

// 判断是否为管理员
const isAdmin = ref(userStore.roles.includes('admin') || userStore.roles.includes('manager'));

// 查询表单
const queryForm = ref({
  dateRange: [],
  storeId: null,
  channelId: null
});

// 选项数据
const channelOptions = ref([]);
const storeOptions = ref([]);
const channelName = ref('');

// 时间维度
const timeUnit = ref('month');

// 加载状态
const loading = ref(false);

// 产品明细相关数据
const tableLoading = ref(false);
const productDetailsList = ref([]);
const productDetailsTotal = ref(0);
const productDetailsQuery = ref({
  pageNum: 1,
  pageSize: 20
});

// 图表引用
const storeInventoryChart = ref(null);
const timeSalesChart = ref(null);
const productSalesChart = ref(null);
const productInventoryChart = ref(null);

// 图表实例
let storeInventoryChartInstance = null;
let timeSalesChartInstance = null;
let productSalesChartInstance = null;
let productInventoryChartInstance = null;

// 初始化图表
function initCharts() {
  // 初始化门店库存分布图表
  storeInventoryChartInstance = echarts.init(storeInventoryChart.value);

  // 初始化时间销售数据图表
  timeSalesChartInstance = echarts.init(timeSalesChart.value);

  // 初始化商品销售情况图表
  productSalesChartInstance = echarts.init(productSalesChart.value);

  // 初始化商品库存统计图表
  productInventoryChartInstance = echarts.init(productInventoryChart.value);

  // 窗口大小变化时，重新调整图表大小
  window.addEventListener('resize', handleResize);
}

// 处理窗口大小变化
function handleResize() {
  storeInventoryChartInstance && storeInventoryChartInstance.resize();
  timeSalesChartInstance && timeSalesChartInstance.resize();
  productSalesChartInstance && productSalesChartInstance.resize();
  productInventoryChartInstance && productInventoryChartInstance.resize();
}

// 组件卸载前清除事件监听
onBeforeUnmount(() => {
  window.removeEventListener('resize', handleResize);
});

// 加载数据
function loadData() {
  loading.value = true;

  const params = {};

  // 渠道参数
  if (queryForm.value.channelId) {
    params.channelId = queryForm.value.channelId;
  }

  // 时间维度参数
  if (timeUnit.value) {
    params.timeUnit = timeUnit.value;
  }

  // 日期范围参数（仅影响销售相关数据）
  if (queryForm.value.dateRange && queryForm.value.dateRange.length === 2) {
    params.beginTime = queryForm.value.dateRange[0];
    params.endTime = queryForm.value.dateRange[1];
  }

  // 门店参数（仅影响销售相关数据）
  if (queryForm.value.storeId) {
    params.storeId = queryForm.value.storeId;
  }

  getChannelStatistics(params).then(response => {
    if (response.code === 200) {
      const data = response.data;

      // 设置渠道名称
      channelName.value = data.channelName || '';

      // 设置渠道选项（仅管理员可见）
      if (isAdmin.value && data.channelOptions) {
        channelOptions.value = data.channelOptions;
      }

      // 设置门店选项
      if (data.storeOptions) {
        storeOptions.value = data.storeOptions;
      }

      // 渲染门店库存分布图表（不受日期和门店筛选影响）
      renderStoreInventoryChart(data.storeInventoryDistribution || []);

      // 渲染时间销售数据图表（受筛选条件影响）
      renderTimeSalesChart(data.timeSalesData || []);

      // 渲染商品销售情况图表（受筛选条件影响）
      renderProductSalesChart(data.productSalesData || []);

      // 渲染商品库存统计图表（不受日期和门店筛选影响）
      renderProductInventoryChart(data.productInventoryData || []);
    } else {
      ElMessage.error(response.msg || '加载数据失败');
    }
  }).catch(error => {
    console.error('加载数据失败:', error);
    ElMessage.error('加载数据失败');
  }).finally(() => {
    loading.value = false;
  });
}

// 渲染门店库存分布图表
function renderStoreInventoryChart(data) {
  const storeNames = data.map(item => item.storeName);
  const quantities = data.map(item => item.quantity);

  const option = {
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'shadow'
      }
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      containLabel: true
    },
    xAxis: {
      type: 'category',
      data: storeNames,
      axisLabel: {
        interval: 0,
        rotate: 30
      }
    },
    yAxis: {
      type: 'value',
      name: '库存数量',
      axisLabel: {
        formatter: '{value} 件'
      }
    },
    series: [
      {
        name: '库存数量',
        type: 'bar',
        data: quantities,
        itemStyle: {
          color: '#409EFF'
        }
      }
    ]
  };

  storeInventoryChartInstance.setOption(option);
}

// 渲染时间销售数据图表
function renderTimeSalesChart(data) {
  // 获取后端返回的时间单位
  if (data.length > 0 && data[0].timeLabel) {
    // 使用后端返回的时间标签和销售数量
    const timeLabels = data.map(item => item.timeLabel);
    const salesQuantities = data.map(item => item.salesQuantity);

    let xAxisName = '';
    let rotateAngle = 30;

    // 根据时间单位设置X轴名称和旋转角度
    switch (timeUnit.value) {
      case 'day':
        xAxisName = '日期';
        rotateAngle = 45;
        break;
      case 'month':
        xAxisName = '月份';
        rotateAngle = 30;
        break;
      case 'year':
        xAxisName = '年份';
        rotateAngle = 0;
        break;
      default:
        xAxisName = '月份';
        rotateAngle = 30;
    }

    const option = {
      tooltip: {
        trigger: 'axis',
        axisPointer: {
          type: 'line'
        }
      },
      grid: {
        left: '3%',
        right: '4%',
        bottom: '3%',
        containLabel: true
      },
      xAxis: {
        type: 'category',
        data: timeLabels,
        name: xAxisName,
        axisLabel: {
          interval: 0,
          rotate: rotateAngle
        }
      },
      yAxis: {
        type: 'value',
        name: '销售数量',
        axisLabel: {
          formatter: '{value} 件'
        }
      },
      series: [
        {
          name: '销售数量',
          type: 'line',
          data: salesQuantities,
          smooth: true,
          itemStyle: {
            color: '#67C23A'
          },
          areaStyle: {
            color: {
              type: 'linear',
              x: 0,
              y: 0,
              x2: 0,
              y2: 1,
              colorStops: [
                {
                  offset: 0,
                  color: 'rgba(103, 194, 58, 0.5)'
                },
                {
                  offset: 1,
                  color: 'rgba(103, 194, 58, 0.1)'
                }
              ]
            }
          }
        }
      ]
    };

    timeSalesChartInstance.setOption(option);
  } else {
    // 兼容旧版数据格式
    const months = data.map(item => item.month);
    const salesQuantities = data.map(item => item.salesQuantity);

    const option = {
      tooltip: {
        trigger: 'axis',
        axisPointer: {
          type: 'line'
        }
      },
      grid: {
        left: '3%',
        right: '4%',
        bottom: '3%',
        containLabel: true
      },
      xAxis: {
        type: 'category',
        data: months,
        name: '月份',
        axisLabel: {
          interval: 0,
          rotate: 30
        }
      },
      yAxis: {
        type: 'value',
        name: '销售数量',
        axisLabel: {
          formatter: '{value} 件'
        }
      },
      series: [
        {
          name: '销售数量',
          type: 'line',
          data: salesQuantities,
          smooth: true,
          itemStyle: {
            color: '#67C23A'
          },
          areaStyle: {
            color: {
              type: 'linear',
              x: 0,
              y: 0,
              x2: 0,
              y2: 1,
              colorStops: [
                {
                  offset: 0,
                  color: 'rgba(103, 194, 58, 0.5)'
                },
                {
                  offset: 1,
                  color: 'rgba(103, 194, 58, 0.1)'
                }
              ]
            }
          }
        }
      ]
    };

    timeSalesChartInstance.setOption(option);
  }
}

// 渲染商品销售情况图表
function renderProductSalesChart(data) {
  const productNames = data.map(item => item.productName);
  const salesQuantities = data.map(item => item.salesQuantity);

  const option = {
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'shadow'
      },
      formatter: function(params) {
        const dataIndex = params[0].dataIndex;
        const item = data[dataIndex];

        let html = `<div style="text-align: left;">`;

        // 添加商品图片
        if (item.productImage) {
          html += `<img src="${import.meta.env.VITE_APP_BASE_API}${item.productImage}" style="width: 50px; height: 50px; object-fit: cover; margin-right: 10px; vertical-align: middle;" />`;
        }

        html += `<div style="display: inline-block; vertical-align: middle;">`;
        html += `<div style="font-weight: bold;">${item.productName}</div>`;

        // 添加货号
        if (item.productNumber) {
          html += `<div style="color: #666; font-size: 12px;">货号: ${item.productNumber}</div>`;
        }

        html += `<div>销售数量: ${params[0].value} 件</div>`;
        html += `</div></div>`;

        return html;
      }
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      containLabel: true
    },
    xAxis: {
      type: 'value',
      name: '销售数量',
      axisLabel: {
        formatter: '{value} 件'
      }
    },
    yAxis: {
      type: 'category',
      data: productNames,
      axisLabel: {
        interval: 0
      }
    },
    series: [
      {
        name: '销售数量',
        type: 'bar',
        data: salesQuantities,
        itemStyle: {
          color: '#67C23A'
        }
      }
    ]
  };

  productSalesChartInstance.setOption(option);
}

// 渲染商品库存统计图表
function renderProductInventoryChart(data) {
  const productNames = data.map(item => item.productName);
  const inventoryQuantities = data.map(item => item.inventoryQuantity);

  const option = {
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'shadow'
      },
      formatter: function(params) {
        const dataIndex = params[0].dataIndex;
        const item = data[dataIndex];

        let html = `<div style="text-align: left;">`;

        // 添加商品图片
        if (item.productImage) {
          html += `<img src="${import.meta.env.VITE_APP_BASE_API}${item.productImage}" style="width: 50px; height: 50px; object-fit: cover; margin-right: 10px; vertical-align: middle;" />`;
        }

        html += `<div style="display: inline-block; vertical-align: middle;">`;
        html += `<div style="font-weight: bold;">${item.productName}</div>`;

        // 添加货号
        if (item.productNumber) {
          html += `<div style="color: #666; font-size: 12px;">货号: ${item.productNumber}</div>`;
        }

        html += `<div>库存数量: ${params[0].value} 件</div>`;
        html += `</div></div>`;

        return html;
      }
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      containLabel: true
    },
    xAxis: {
      type: 'value',
      name: '库存数量',
      axisLabel: {
        formatter: '{value} 件'
      }
    },
    yAxis: {
      type: 'category',
      data: productNames,
      axisLabel: {
        interval: 0
      }
    },
    series: [
      {
        name: '库存数量',
        type: 'bar',
        data: inventoryQuantities,
        itemStyle: {
          color: '#E6A23C'
        }
      }
    ]
  };

  productInventoryChartInstance.setOption(option);
}

// 处理搜索
function handleSearch() {
  // 重新加载所有数据
  loadData();
  loadProductDetails();
}

// 重置查询条件
function resetQuery() {
  queryForm.value = {
    dateRange: [],
    storeId: null,
    channelId: null
  };

  // 重新加载数据
  handleSearch();
}

// 处理时间维度变化
function handleTimeUnitChange() {
  // 只需要重新初始化时间销售数据图表
  if (timeSalesChartInstance) {
    timeSalesChartInstance.dispose();
    timeSalesChartInstance = echarts.init(timeSalesChart.value);
  }

  // 重新加载数据
  loadData();
}

// 刷新数据
function refreshData() {
  // 重新初始化所有图表
  if (storeInventoryChartInstance) {
    storeInventoryChartInstance.dispose();
    storeInventoryChartInstance = echarts.init(storeInventoryChart.value);
  }
  if (timeSalesChartInstance) {
    timeSalesChartInstance.dispose();
    timeSalesChartInstance = echarts.init(timeSalesChart.value);
  }
  if (productSalesChartInstance) {
    productSalesChartInstance.dispose();
    productSalesChartInstance = echarts.init(productSalesChart.value);
  }
  if (productInventoryChartInstance) {
    productInventoryChartInstance.dispose();
    productInventoryChartInstance = echarts.init(productInventoryChart.value);
  }

  // 重新加载数据
  loadData();
  loadProductDetails();
}

// 产品明细相关方法

function handleSizeChange(val) {
  productDetailsQuery.value.pageSize = val;
  productDetailsQuery.value.pageNum = 1;
  loadProductDetails();
}

function handleCurrentChange(val) {
  productDetailsQuery.value.pageNum = val;
  loadProductDetails();
}

function loadProductDetails() {
  tableLoading.value = true;

  const params = {
    pageNum: productDetailsQuery.value.pageNum,
    pageSize: productDetailsQuery.value.pageSize
  };

  // 渠道参数
  if (queryForm.value.channelId) {
    params.channelId = queryForm.value.channelId;
  }

  // 门店参数
  if (queryForm.value.storeId) {
    params.storeId = queryForm.value.storeId;
  }

  // 日期范围参数
  if (queryForm.value.dateRange && queryForm.value.dateRange.length === 2) {
    params.beginTime = queryForm.value.dateRange[0];
    params.endTime = queryForm.value.dateRange[1];
  } else {
    // 默认查询今天的数据
    const today = new Date().toISOString().split('T')[0];
    params.beginTime = today;
    params.endTime = today;
  }

  getChannelProductDetails(params).then(response => {
    if (response.code === 200) {
      // 后端返回的是 TableDataInfo 格式
      productDetailsList.value = response.rows || [];
      productDetailsTotal.value = response.total || 0;
    } else {
      ElMessage.error(response.msg || '加载产品明细失败');
    }
  }).catch(error => {
    console.error('加载产品明细失败:', error);
    ElMessage.error('加载产品明细失败');
  }).finally(() => {
    tableLoading.value = false;
  });
}

function formatDateTime(dateTime) {
  if (!dateTime) return '';
  const date = new Date(dateTime);
  return date.toLocaleString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit'
  });
}

// 初始化查询条件
function initQueryForm() {
  const today = new Date().toISOString().split('T')[0];
  queryForm.value.dateRange = [today, today];
}

// 组件挂载时初始化图表和加载数据
onMounted(() => {
  initCharts();
  initQueryForm();
  loadData();
  loadProductDetails();
});
</script>

<style scoped>
.filter-container {
  margin-bottom: 20px;
}

.filter-item {
  display: flex;
  align-items: center;
  gap: 15px;
}

.channel-info {
  margin-bottom: 20px;
}

.channel-name {
  font-size: 16px;
  font-weight: bold;
}

.data-container {
  display: grid;
  grid-template-columns: 1fr;
  gap: 20px;
}

.chart-card, .table-card {
  margin-bottom: 0;
}

.table-card.full-width {
  grid-column: 1 / -1; /* 占满整行 */
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.header-controls {
  display: flex;
  align-items: center;
}

.table-container {
  width: 100%;
}

.no-image {
  display: inline-block;
  width: 50px;
  height: 50px;
  line-height: 50px;
  text-align: center;
  background-color: #f5f7fa;
  color: #909399;
  font-size: 12px;
  border-radius: 4px;
}

.time-unit-selector {
  margin-left: 15px;
}

.chart-container {
  height: 400px;
  width: 100%;
}

.chart {
  height: 100%;
  width: 100%;
}

@media (min-width: 1200px) {
  .data-container {
    grid-template-columns: 1fr 1fr;
  }

  .chart-card:first-child {
    grid-column: 1 / 3;
  }
}
</style>