<template>
  <div class="app-container">
    <el-card class="box-card">
      <template #header>
        <div class="card-header">
          <span><el-icon><DataAnalysis /></el-icon> 销售数据分析</span>
          <div class="filter-container">
            <div style="min-width: 350px;">
              <el-date-picker
                v-model="dateRange"
                type="daterange"
                range-separator="至"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
                value-format="YYYY-MM-DD"
                style="width: 100%;"
                @change="handleDateRangeChange"
              />
            </div>
            <div style="min-width: 150px;">
              <el-select v-model="storeId" placeholder="选择门店" clearable style="width: 100%;" @change="handleStoreChange">
                <el-option v-for="item in storeOptions" :key="item.value" :label="item.label" :value="item.value" />
              </el-select>
            </div>
            <div style="min-width: 150px;">
              <el-select v-model="channelId" placeholder="选择渠道" clearable style="width: 100%;" @change="handleChannelChange">
                <el-option v-for="item in channelOptions" :key="item.value" :label="item.label" :value="item.value" />
              </el-select>
            </div>
            <el-button type="primary" :loading="loading" @click="refreshData">{{ loading ? '数据加载中...' : '刷新数据' }}</el-button>
            <el-button type="danger" :loading="stockWarningLoading" @click="handleStockWarning">
              库存超时预警{{ stockWarningCount > 0 ? `（${stockWarningCount}）` : '' }}
            </el-button>
          </div>
        </div>
      </template>

      <!-- 数据概览卡片 -->
      <div class="data-overview">
        <el-row :gutter="20">
          <el-col :span="4">
            <el-card shadow="hover" class="data-card">
              <div class="data-card-content">
                <div class="data-card-value">{{ formatCurrency(statistics.totalSales) }}</div>
                <div class="data-card-title">总销售额</div>
                <el-icon class="data-card-icon"><Money /></el-icon>
              </div>
            </el-card>
          </el-col>
          <el-col :span="4">
            <el-card shadow="hover" class="data-card">
              <div class="data-card-content">
                <div class="data-card-value">{{ statistics.totalOrders }}</div>
                <div class="data-card-title">总订单数</div>
                <el-icon class="data-card-icon"><ShoppingCart /></el-icon>
              </div>
            </el-card>
          </el-col>
          <el-col :span="4">
            <el-card shadow="hover" class="data-card">
              <div class="data-card-content">
                <div class="data-card-value">{{ statistics.totalQuantity }}</div>
                <div class="data-card-title">总销售商品数量</div>
                <el-icon class="data-card-icon"><Goods /></el-icon>
              </div>
            </el-card>
          </el-col>
          <el-col :span="4">
            <el-card shadow="hover" class="data-card">
              <div class="data-card-content">
                <div class="data-card-value">{{ formatCurrency(statistics.totalProfit) }}</div>
                <div class="data-card-title">总利润</div>
                <el-icon class="data-card-icon"><TrendCharts /></el-icon>
              </div>
            </el-card>
          </el-col>
          <el-col :span="4">
            <el-card shadow="hover" class="data-card">
              <div class="data-card-content">
                <div class="data-card-value">{{ formatCurrency(statistics.averageOrderValue) }}</div>
                <div class="data-card-title">平均客单价</div>
                <el-icon class="data-card-icon"><PriceTag /></el-icon>
              </div>
            </el-card>
          </el-col>
          <el-col :span="4">
            <el-card shadow="hover" class="data-card">
              <div class="data-card-content">
                <div class="data-card-value">{{ statistics.totalQuantity > 0 ? (statistics.totalSales / statistics.totalQuantity).toFixed(2) : '0.00' }}</div>
                <div class="data-card-title">平均单品价格</div>
                <el-icon class="data-card-icon"><Discount /></el-icon>
              </div>
            </el-card>
          </el-col>
        </el-row>
      </div>

      <!-- 销售趋势图表 -->
      <el-row :gutter="20" class="chart-row">
        <el-col :span="24">
          <el-card shadow="hover" class="chart-card">
            <template #header>
              <div class="chart-header">
                <span>销售趋势分析</span>
                <el-radio-group v-model="trendTimeUnit" size="small" @change="handleTrendTimeUnitChange">
                  <el-radio-button label="day">日</el-radio-button>
                  <el-radio-button label="month">月</el-radio-button>
                  <el-radio-button label="year">年</el-radio-button>
                </el-radio-group>
              </div>
            </template>
            <div ref="saleTrendChart" class="chart-container"></div>
          </el-card>
        </el-col>
      </el-row>

      <!-- 销售分布和排行图表 -->
      <el-row :gutter="20" class="chart-row">
        <el-col :span="12">
          <el-card shadow="hover" class="chart-card">
            <template #header>
              <div class="chart-header">
                <span>销售渠道分布</span>
              </div>
            </template>
            <div ref="channelDistributionChart" class="chart-container"></div>
          </el-card>
        </el-col>
        <el-col :span="12">
          <el-card shadow="hover" class="chart-card">
            <template #header>
              <div class="chart-header">
                <span>门店销售排行</span>
              </div>
            </template>
            <div ref="storeRankingChart" class="chart-container"></div>
          </el-card>
        </el-col>
      </el-row>

      <!-- 商品排行和客户分析图表 -->
      <el-row :gutter="20" class="chart-row">
        <el-col :span="12">
          <el-card shadow="hover" class="chart-card">
            <template #header>
              <div class="chart-header">
                <span>商品销售排行</span>
              </div>
            </template>
            <div ref="productRankingChart" class="chart-container"></div>
          </el-card>
        </el-col>
        <el-col :span="12">
          <el-card shadow="hover" class="chart-card">
            <template #header>
              <div class="chart-header">
                <span>客户性别分布</span>
              </div>
            </template>
            <div ref="customerGenderChart" class="chart-container"></div>
          </el-card>
        </el-col>
      </el-row>

      <!-- 利润分析图表 -->
      <el-row :gutter="20" class="chart-row">
        <el-col :span="24">
          <el-card shadow="hover" class="chart-card">
            <template #header>
              <div class="chart-header">
                <span>利润分析</span>
                <el-radio-group v-model="profitTimeUnit" size="small" @change="handleProfitTimeUnitChange">
                  <el-radio-button label="day">日</el-radio-button>
                  <el-radio-button label="month">月</el-radio-button>
                  <el-radio-button label="year">年</el-radio-button>
                </el-radio-group>
              </div>
            </template>
            <div ref="profitAnalysisChart" class="chart-container"></div>
          </el-card>
        </el-col>
      </el-row>

      <!-- 库存分析图表 -->
      <el-row :gutter="20" class="chart-row">
        <el-col :span="12">
          <el-card shadow="hover" class="chart-card">
            <template #header>
              <div class="chart-header">
                <span>库存分布</span>
              </div>
            </template>
            <div ref="inventoryDistributionChart" class="chart-container"></div>
          </el-card>
        </el-col>
        <el-col :span="12">
          <el-card shadow="hover" class="chart-card">
            <template #header>
              <div class="chart-header">
                <span>库存渠道分布</span>
              </div>
            </template>
            <div ref="inventoryTypeChart" class="chart-container"></div>
          </el-card>
        </el-col>
      </el-row>
    </el-card>

    <!-- 库存超时预警弹窗 -->
    <el-dialog title="库存超时预警" v-model="stockWarningDialogVisible" width="1200px" append-to-body destroy-on-close>
      <el-table v-loading="stockWarningTableLoading" :data="stockWarningList" style="width: 100%">
        <el-table-column label="商品图片" width="80">
          <template #default="scope">
            <image-preview :src="scope.row.productImage" :width="50" :height="50"/>
          </template>
        </el-table-column>
        <el-table-column label="商品名称" prop="productName" show-overflow-tooltip />
        <el-table-column label="货号" prop="productNumber" />
        <el-table-column label="品牌" prop="brand" />
        <el-table-column label="门店" prop="storeName" />
        <el-table-column label="柜台" prop="counterName" />
        <el-table-column label="渠道" prop="channelName" />
        <el-table-column label="库存数量" prop="quantity" align="center">
          <template #default="scope">
            <el-tag type="warning">{{ scope.row.quantity }}</el-tag>
          </template>
        </el-table-column>
        <el-table-column label="入库时间" prop="createTime" width="180">
          <template #default="scope">
            <span>{{ parseTime(scope.row.createTime) }}</span>
          </template>
        </el-table-column>
        <el-table-column label="库存天数" prop="stockDays" align="center">
          <template #default="scope">
            <el-tag type="danger">{{ scope.row.stockDays }}天</el-tag>
          </template>
        </el-table-column>
        <el-table-column label="操作" align="center" width="100">
          <template #default="scope">
            <el-button link type="primary" icon="View" @click="handleViewStockDetail(scope.row)">查看</el-button>
          </template>
        </el-table-column>
      </el-table>

      <pagination
        v-show="stockWarningTotal > 0"
        :total="stockWarningTotal"
        :page="stockWarningQueryParams.pageNum"
        :limit="stockWarningQueryParams.pageSize"
        @update:page="stockWarningQueryParams.pageNum = $event"
        @update:limit="stockWarningQueryParams.pageSize = $event"
        @pagination="getStockWarningList"
      />
    </el-dialog>

    <!-- 库存详情弹窗 -->
    <el-dialog title="库存详情" v-model="stockDetailDialogVisible" width="800px" append-to-body destroy-on-close>
      <el-descriptions :column="2" border v-if="stockDetail">
        <el-descriptions-item label="商品图片" :span="2">
          <image-preview :src="stockDetail.productImage" :width="100" :height="100"/>
        </el-descriptions-item>
        <el-descriptions-item label="商品名称">{{ stockDetail.productName }}</el-descriptions-item>
        <el-descriptions-item label="货号">{{ stockDetail.productNumber }}</el-descriptions-item>
        <el-descriptions-item label="品牌">{{ stockDetail.brand }}</el-descriptions-item>
        <el-descriptions-item label="门店">{{ stockDetail.storeName }}</el-descriptions-item>
        <el-descriptions-item label="柜台">{{ stockDetail.counterName }}</el-descriptions-item>
        <el-descriptions-item label="渠道">{{ stockDetail.channelName }}</el-descriptions-item>
        <el-descriptions-item label="库存数量">
          <el-tag type="warning">{{ stockDetail.quantity }}</el-tag>
        </el-descriptions-item>
        <el-descriptions-item label="进货价">{{ stockDetail.purchasePrice }} 元</el-descriptions-item>
        <el-descriptions-item label="零售价">{{ stockDetail.retailPrice }} 元</el-descriptions-item>
        <el-descriptions-item label="批次号">{{ stockDetail.batchNumber }}</el-descriptions-item>
        <el-descriptions-item label="入库时间">{{ parseTime(stockDetail.createTime) }}</el-descriptions-item>
        <el-descriptions-item label="库存天数">
          <el-tag type="danger">{{ stockDetail.stockDays }}天</el-tag>
        </el-descriptions-item>
        <el-descriptions-item label="备注" :span="2">{{ stockDetail.remark || '无' }}</el-descriptions-item>
      </el-descriptions>
    </el-dialog>
  </div>
</template>

<script setup name="SaleAnalysis">
import { ref, reactive, onMounted, onUnmounted, getCurrentInstance } from 'vue';
import { DataAnalysis, Money, ShoppingCart, TrendCharts, PriceTag, Goods, Discount } from '@element-plus/icons-vue';
import * as echarts from 'echarts';
import {
  getSaleStatistics,
  getSaleTrend,
  getSaleChannelDistribution,
  getSaleStoreRanking,
  getSaleProductRanking,
  getCustomerGenderDistribution,
  getProfitAnalysis,
  getInventoryAnalysis
} from '@/api/analysis/sale';
import { listStore } from "@/api/retail/store";
import { listChannel } from "@/api/retail/channel";
import { listStoreStock } from "@/api/retail/storestock";
import ImagePreview from '@/components/ImagePreview/index.vue';
import Pagination from '@/components/Pagination/index.vue';
import { parseTime } from '@/utils/ruoyi';

const { proxy } = getCurrentInstance();

// 加载状态
const loading = ref(false);

// 图表引用
const saleTrendChart = ref(null);
const channelDistributionChart = ref(null);
const storeRankingChart = ref(null);
const productRankingChart = ref(null);
const customerGenderChart = ref(null);
const profitAnalysisChart = ref(null);
const inventoryDistributionChart = ref(null);
const inventoryTypeChart = ref(null);

// 图表实例
let saleTrendChartInstance = null;
let channelDistributionChartInstance = null;
let storeRankingChartInstance = null;
let productRankingChartInstance = null;
let customerGenderChartInstance = null;
let profitAnalysisChartInstance = null;
let inventoryDistributionChartInstance = null;
let inventoryTypeChartInstance = null;

// 筛选条件
const dateRange = ref([]);
const storeId = ref('');
const channelId = ref('');
const trendTimeUnit = ref('month');
const profitTimeUnit = ref('month');

// 数据
const statistics = reactive({
  totalSales: 0,
  totalOrders: 0,
  totalQuantity: 0,
  totalProfit: 0,
  averageOrderValue: 0
});

// 门店选项
const storeOptions = ref([]);

// 渠道选项
const channelOptions = ref([]);

// 库存超时预警相关
const stockWarningLoading = ref(false);
const stockWarningCount = ref(0);
const stockWarningDialogVisible = ref(false);
const stockWarningTableLoading = ref(false);
const stockWarningList = ref([]);
const stockWarningTotal = ref(0);
const stockWarningQueryParams = ref({
  pageNum: 1,
  pageSize: 10
});

// 库存详情相关
const stockDetailDialogVisible = ref(false);
const stockDetail = ref(null);

// 获取门店列表
function getStoreList() {
  listStore().then(response => {
    if (response.code === 200) {
      storeOptions.value = response.rows.map(item => {
        return {
          value: item.id.toString(),
          label: item.storeName
        };
      });
    }
  });
}

// 获取渠道列表
function getChannelList() {
  listChannel().then(response => {
    if (response.code === 200) {
      channelOptions.value = response.rows.map(item => {
        return {
          value: item.id.toString(),
          label: item.channelName
        };
      });
    }
  });
}

// 初始化图表
function initCharts() {
  // 初始化销售趋势图表
  saleTrendChartInstance = echarts.init(saleTrendChart.value);

  // 初始化销售渠道分布图表
  channelDistributionChartInstance = echarts.init(channelDistributionChart.value);

  // 初始化门店销售排行图表
  storeRankingChartInstance = echarts.init(storeRankingChart.value);

  // 初始化商品销售排行图表
  productRankingChartInstance = echarts.init(productRankingChart.value);

  // 初始化客户性别分布图表
  customerGenderChartInstance = echarts.init(customerGenderChart.value);

  // 初始化利润分析图表
  profitAnalysisChartInstance = echarts.init(profitAnalysisChart.value);

  // 初始化库存分布图表
  inventoryDistributionChartInstance = echarts.init(inventoryDistributionChart.value);

  // 初始化库存类型分布图表
  inventoryTypeChartInstance = echarts.init(inventoryTypeChart.value);

  // 加载数据
  loadData();

  // 监听窗口大小变化，调整图表大小
  window.addEventListener('resize', handleResize);
}

// 处理窗口大小变化
function handleResize() {
  saleTrendChartInstance && saleTrendChartInstance.resize();
  channelDistributionChartInstance && channelDistributionChartInstance.resize();
  storeRankingChartInstance && storeRankingChartInstance.resize();
  productRankingChartInstance && productRankingChartInstance.resize();
  customerGenderChartInstance && customerGenderChartInstance.resize();
  profitAnalysisChartInstance && profitAnalysisChartInstance.resize();
  inventoryDistributionChartInstance && inventoryDistributionChartInstance.resize();
  inventoryTypeChartInstance && inventoryTypeChartInstance.resize();
}

// 加载数据
function loadData() {
  loading.value = true;

  Promise.all([
    loadSaleStatistics(),
    loadSaleTrend(),
    loadChannelDistribution(),
    loadStoreRanking(),
    loadProductRanking(),
    loadCustomerGenderDistribution(),
    loadProfitAnalysis(),
    loadInventoryAnalysis()
  ]).finally(() => {
    loading.value = false;
  });
}

// 加载销售统计数据
function loadSaleStatistics() {
  const params = getQueryParams();
  return getSaleStatistics(params).then(response => {
    if (response.code === 200) {
      Object.assign(statistics, response.data);
    }
    return response;
  });
}

// 加载销售趋势数据
function loadSaleTrend() {
  // 如果选择了日期范围，将时间单位设置为天
  if (dateRange.value && dateRange.value.length === 2) {
    trendTimeUnit.value = 'day';
  }

  const params = {
    ...getQueryParams(),
    timeUnit: trendTimeUnit.value
  };

  console.log('销售趋势参数:', params);

  return getSaleTrend(params).then(response => {
    if (response.code === 200) {
      renderSaleTrendChart(response.data);
    }
    return response;
  });
}

// 渲染销售趋势图表
function renderSaleTrendChart(data) {
  const option = {
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'shadow'
      }
    },
    legend: {
      data: ['销售额', '订单数', '销售商品数量']
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      containLabel: true
    },
    xAxis: {
      type: 'category',
      data: data.dateList
    },
    yAxis: [
      {
        type: 'value',
        name: '销售额',
        axisLabel: {
          formatter: '{value} 元'
        }
      },
      {
        type: 'value',
        name: '订单数/商品数量',
        axisLabel: {
          formatter: '{value}'
        }
      }
    ],
    series: [
      {
        name: '销售额',
        type: 'bar',
        data: data.salesData
      },
      {
        name: '订单数',
        type: 'line',
        yAxisIndex: 1,
        data: data.orderData
      },
      {
        name: '销售商品数量',
        type: 'line',
        yAxisIndex: 1,
        data: data.quantityData,
        itemStyle: {
          color: '#67C23A'
        }
      }
    ]
  };

  saleTrendChartInstance.setOption(option);
}

// 加载销售渠道分布数据
function loadChannelDistribution() {
  const params = getQueryParams();
  return getSaleChannelDistribution(params).then(response => {
    if (response.code === 200) {
      renderChannelDistributionChart(response.data);
    }
    return response;
  });
}

// 渲染销售渠道分布图表
function renderChannelDistributionChart(data) {
  const option = {
    tooltip: {
      trigger: 'item',
      formatter: '{a} <br/>{b}: {c} ({d}%)'
    },
    legend: {
      orient: 'vertical',
      left: 10,
      data: data.map(item => item.name)
    },
    series: [
      {
        name: '销售渠道',
        type: 'pie',
        radius: ['50%', '70%'],
        avoidLabelOverlap: false,
        label: {
          show: false,
          position: 'center'
        },
        emphasis: {
          label: {
            show: true,
            fontSize: '16',
            fontWeight: 'bold'
          }
        },
        labelLine: {
          show: false
        },
        data: data
      }
    ]
  };

  channelDistributionChartInstance.setOption(option);
}

// 加载门店销售排行数据
function loadStoreRanking() {
  const params = getQueryParams();
  return getSaleStoreRanking(params).then(response => {
    if (response.code === 200) {
      renderStoreRankingChart(response.data);
    }
    return response;
  });
}

// 渲染门店销售排行图表
function renderStoreRankingChart(data) {
  const storeNames = data.map(item => item.name);
  const storeSales = data.map(item => item.sales);

  const option = {
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'shadow'
      }
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      containLabel: true
    },
    xAxis: {
      type: 'value',
      axisLabel: {
        formatter: '{value} 元'
      }
    },
    yAxis: {
      type: 'category',
      data: storeNames
    },
    series: [
      {
        name: '销售额',
        type: 'bar',
        data: storeSales
      }
    ]
  };

  storeRankingChartInstance.setOption(option);
}

// 加载商品销售排行数据
function loadProductRanking() {
  const params = getQueryParams();
  return getSaleProductRanking(params).then(response => {
    if (response.code === 200) {
      renderProductRankingChart(response.data);
    }
    return response;
  });
}

// 渲染商品销售排行图表
function renderProductRankingChart(data) {
  const productNames = data.map(item => item.name);
  const productSales = data.map(item => item.sales);
  const productQuantities = data.map(item => item.quantity);

  const option = {
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'shadow'
      },
      formatter: function(params) {
        const salesItem = params[0];
        const quantityItem = params[1];
        const dataIndex = salesItem.dataIndex;
        const item = data[dataIndex];

        let html = `<div style="text-align: left;">`;

        // 添加商品图片
        if (item.productImage) {
          html += `<img src="${import.meta.env.VITE_APP_BASE_API}${item.productImage}" style="width: 50px; height: 50px; object-fit: cover; margin-right: 10px; vertical-align: middle;" />`;
        }

        html += `<div style="display: inline-block; vertical-align: middle;">`;
        html += `<div style="font-weight: bold;">${item.name}</div>`;

        // 添加货号
        if (item.productNumber) {
          html += `<div style="color: #666; font-size: 12px;">货号: ${item.productNumber}</div>`;
        }

        html += `<div>${salesItem.seriesName}: ${formatCurrency(salesItem.value)}</div>`;
        html += `<div>${quantityItem.seriesName}: ${quantityItem.value}</div>`;
        html += `</div></div>`;

        return html;
      }
    },
    legend: {
      data: ['销售额', '销售数量']
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      containLabel: true
    },
    xAxis: [
      {
        type: 'value',
        name: '销售额',
        axisLabel: {
          formatter: '{value} 元'
        },
        position: 'bottom'
      },
      {
        type: 'value',
        name: '销售数量',
        axisLabel: {
          formatter: '{value}'
        },
        position: 'top'
      }
    ],
    yAxis: {
      type: 'category',
      data: productNames
    },
    series: [
      {
        name: '销售额',
        type: 'bar',
        data: productSales
      },
      {
        name: '销售数量',
        type: 'bar',
        xAxisIndex: 1,
        data: productQuantities,
        itemStyle: {
          color: '#67C23A'
        }
      }
    ]
  };

  productRankingChartInstance.setOption(option);
}

// 加载客户性别分布数据
function loadCustomerGenderDistribution() {
  const params = getQueryParams();
  return getCustomerGenderDistribution(params).then(response => {
    if (response.code === 200) {
      renderCustomerGenderChart(response.data);
    }
    return response;
  });
}

// 渲染客户性别分布图表
function renderCustomerGenderChart(data) {
  const option = {
    tooltip: {
      trigger: 'item',
      formatter: '{a} <br/>{b}: {c} ({d}%)'
    },
    legend: {
      orient: 'vertical',
      left: 10,
      data: data.map(item => item.name)
    },
    series: [
      {
        name: '客户性别',
        type: 'pie',
        radius: '50%',
        data: data,
        emphasis: {
          itemStyle: {
            shadowBlur: 10,
            shadowOffsetX: 0,
            shadowColor: 'rgba(0, 0, 0, 0.5)'
          }
        }
      }
    ]
  };

  customerGenderChartInstance.setOption(option);
}

// 加载利润分析数据
function loadProfitAnalysis() {
  // 如果选择了日期范围，将时间单位设置为天
  if (dateRange.value && dateRange.value.length === 2) {
    profitTimeUnit.value = 'day';
  }

  const params = {
    ...getQueryParams(),
    timeUnit: profitTimeUnit.value
  };

  console.log('利润分析参数:', params);

  return getProfitAnalysis(params).then(response => {
    if (response.code === 200) {
      renderProfitAnalysisChart(response.data);
    }
    return response;
  });
}

// 渲染利润分析图表
function renderProfitAnalysisChart(data) {
  const option = {
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'cross',
        crossStyle: {
          color: '#999'
        }
      }
    },
    legend: {
      data: ['销售额', '成本', '利润']
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      containLabel: true
    },
    xAxis: {
      type: 'category',
      data: data.dateList,
      axisPointer: {
        type: 'shadow'
      }
    },
    yAxis: {
      type: 'value',
      axisLabel: {
        formatter: '{value} 元'
      }
    },
    series: [
      {
        name: '销售额',
        type: 'bar',
        data: data.salesData
      },
      {
        name: '成本',
        type: 'bar',
        data: data.costData
      },
      {
        name: '利润',
        type: 'line',
        data: data.profitData
      }
    ]
  };

  profitAnalysisChartInstance.setOption(option);
}

// 加载库存分析数据
function loadInventoryAnalysis() {
  const params = getQueryParams();
  return getInventoryAnalysis(params).then(response => {
    if (response.code === 200) {
      renderInventoryDistributionChart(response.data.inventoryDistribution);
      renderInventoryTypeChart(response.data.inventoryChannelDistribution);
    }
    return response;
  });
}

// 渲染库存分布图表
function renderInventoryDistributionChart(data) {
  const storeNames = data.map(item => item.name);
  const inventoryQuantities = data.map(item => item.value);

  const option = {
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'shadow'
      }
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      containLabel: true
    },
    xAxis: {
      type: 'category',
      data: storeNames
    },
    yAxis: {
      type: 'value',
      axisLabel: {
        formatter: '{value} 件'
      }
    },
    series: [
      {
        name: '库存数量',
        type: 'bar',
        data: inventoryQuantities
      }
    ]
  };

  inventoryDistributionChartInstance.setOption(option);
}

// 渲染库存渠道分布图表
function renderInventoryTypeChart(data) {
  // 确保数据中使用的是数量而不是价值
  const formattedData = data.map(item => ({
    name: item.name,
    value: item.value // 使用数量作为值
  }));

  const option = {
    tooltip: {
      trigger: 'item',
      formatter: '{a} <br/>{b}: {c}件 ({d}%)'
    },
    legend: {
      orient: 'vertical',
      left: 10,
      data: formattedData.map(item => item.name)
    },
    series: [
      {
        name: '库存渠道',
        type: 'pie',
        radius: '50%',
        data: formattedData,
        emphasis: {
          itemStyle: {
            shadowBlur: 10,
            shadowOffsetX: 0,
            shadowColor: 'rgba(0, 0, 0, 0.5)'
          }
        }
      }
    ]
  };

  inventoryTypeChartInstance.setOption(option);
}

// 获取查询参数
function getQueryParams() {
  const params = {};

  if (dateRange.value && dateRange.value.length === 2) {
    params.startDate = dateRange.value[0];
    params.endDate = dateRange.value[1];
  }

  // 只有当storeId有值时才添加到参数中
  if (storeId.value) {
    params.storeId = storeId.value;
  }

  // 只有当channelId有值时才添加到参数中
  if (channelId.value) {
    params.channelId = channelId.value;
  }

  console.log('查询参数:', params);
  return params;
}

// 处理日期范围变化
function handleDateRangeChange() {
  console.log('日期范围变化:', dateRange.value);

  // 如果选择了日期范围，将时间单位设置为天
  if (dateRange.value && dateRange.value.length === 2) {
    trendTimeUnit.value = 'day';
    profitTimeUnit.value = 'day';
  } else {
    trendTimeUnit.value = 'month';
    profitTimeUnit.value = 'month';
  }

  // 重新初始化所有图表
  reinitializeCharts();

  // 强制重新加载所有数据
  refreshData();
}

// 重新初始化所有图表
function reinitializeCharts() {
  // 销毁所有图表实例
  if (saleTrendChartInstance) {
    saleTrendChartInstance.dispose();
  }
  if (channelDistributionChartInstance) {
    channelDistributionChartInstance.dispose();
  }
  if (storeRankingChartInstance) {
    storeRankingChartInstance.dispose();
  }
  if (productRankingChartInstance) {
    productRankingChartInstance.dispose();
  }
  if (customerGenderChartInstance) {
    customerGenderChartInstance.dispose();
  }
  if (profitAnalysisChartInstance) {
    profitAnalysisChartInstance.dispose();
  }
  if (inventoryDistributionChartInstance) {
    inventoryDistributionChartInstance.dispose();
  }
  if (inventoryTypeChartInstance) {
    inventoryTypeChartInstance.dispose();
  }

  // 重新初始化所有图表
  saleTrendChartInstance = echarts.init(saleTrendChart.value);
  channelDistributionChartInstance = echarts.init(channelDistributionChart.value);
  storeRankingChartInstance = echarts.init(storeRankingChart.value);
  productRankingChartInstance = echarts.init(productRankingChart.value);
  customerGenderChartInstance = echarts.init(customerGenderChart.value);
  profitAnalysisChartInstance = echarts.init(profitAnalysisChart.value);
  inventoryDistributionChartInstance = echarts.init(inventoryDistributionChart.value);
  inventoryTypeChartInstance = echarts.init(inventoryTypeChart.value);
}

// 处理门店变化
function handleStoreChange() {
  console.log('门店变化:', storeId.value);

  // 重新初始化所有图表
  reinitializeCharts();

  // 强制重新加载所有数据
  refreshData();
}

// 处理渠道变化
function handleChannelChange() {
  console.log('渠道变化:', channelId.value);

  // 重新初始化所有图表
  reinitializeCharts();

  // 强制重新加载所有数据
  refreshData();
}

// 处理趋势时间单位变化
function handleTrendTimeUnitChange() {
  loadSaleTrend();
}

// 处理利润时间单位变化
function handleProfitTimeUnitChange() {
  loadProfitAnalysis();
}

// 刷新数据
function refreshData() {
  loading.value = true;

  // 清除之前的数据，确保图表重新渲染
  Object.assign(statistics, {
    totalSales: 0,
    totalOrders: 0,
    totalQuantity: 0,
    totalProfit: 0,
    averageOrderValue: 0
  });

  // 重新初始化所有图表
  reinitializeCharts();

  // 使用setTimeout确保DOM更新后再加载数据
  setTimeout(() => {
    // 使用Promise.all等待所有数据加载完成
    Promise.all([
      loadSaleStatistics(),
      loadSaleTrend(),
      loadChannelDistribution(),
      loadStoreRanking(),
      loadProductRanking(),
      loadCustomerGenderDistribution(),
      loadProfitAnalysis(),
      loadInventoryAnalysis()
    ]).then(() => {
      console.log('所有数据加载完成');
      proxy.$message.success('数据刷新成功');
    }).catch(error => {
      console.error('数据加载失败:', error);
      proxy.$message.error('数据刷新失败');
    }).finally(() => {
      loading.value = false;
    });
  }, 100);
}

// 格式化货币
function formatCurrency(value) {
  if (!value) return '0.00';
  return new Intl.NumberFormat('zh-CN', { style: 'currency', currency: 'CNY' }).format(value);
}

// 初始化默认日期范围
function initDefaultDateRange() {
  // 设置默认日期范围为最近30天
  const endDate = new Date();
  const startDate = new Date();
  startDate.setDate(endDate.getDate() - 30);

  dateRange.value = [
    startDate.toISOString().split('T')[0],
    endDate.toISOString().split('T')[0]
  ];

  console.log('初始化默认日期范围:', dateRange.value);
}

// 库存超时预警相关函数
function handleStockWarning() {
  stockWarningDialogVisible.value = true;
  getStockWarningList();
}

// 获取库存超时预警数量
function getStockWarningCount() {
  stockWarningLoading.value = true;

  // 计算180天前的日期
  const warningDate = new Date();
  warningDate.setDate(warningDate.getDate() - 180);
  const warningDateStr = warningDate.getFullYear() + '-' +
    String(warningDate.getMonth() + 1).padStart(2, '0') + '-' +
    String(warningDate.getDate()).padStart(2, '0');

  const params = {
    pageNum: 1,
    pageSize: 1,
    minQuantity: 1, // 数量大于0
    maxCreateTime: warningDateStr // 入库时间早于180天前
  };

  listStoreStock(params).then(response => {
    if (response.code === 200) {
      stockWarningCount.value = response.total || 0;
    }
  }).finally(() => {
    stockWarningLoading.value = false;
  });
}

// 获取库存超时预警列表
function getStockWarningList() {
  stockWarningTableLoading.value = true;

  // 计算180天前的日期
  const warningDate = new Date();
  warningDate.setDate(warningDate.getDate() - 180); // 入库时间早于180天前
  const warningDateStr = warningDate.getFullYear() + '-' +
    String(warningDate.getMonth() + 1).padStart(2, '0') + '-' +
    String(warningDate.getDate()).padStart(2, '0');

  const params = {
    ...stockWarningQueryParams.value,
    minQuantity: 1, // 数量大于0
    maxCreateTime: warningDateStr // 入库时间早于180天前
  };

  listStoreStock(params).then(response => {
    if (response.code === 200) {
      // 计算库存天数
      const currentDate = new Date();
      stockWarningList.value = response.rows.map(item => {
        const createDate = new Date(item.createTime);
        const diffTime = currentDate - createDate;
        const stockDays = Math.floor(diffTime / (1000 * 60 * 60 * 24));
        return {
          ...item,
          stockDays
        };
      });
      stockWarningTotal.value = response.total;
    }
  }).finally(() => {
    stockWarningTableLoading.value = false;
  });
}

// 查看库存详情
function handleViewStockDetail(row) {
  stockDetail.value = row;
  stockDetailDialogVisible.value = true;
}

// 组件挂载时初始化图表和获取数据
onMounted(() => {
  // 获取门店和渠道数据
  getStoreList();
  getChannelList();
  // 获取库存超时预警数量
  getStockWarningCount();
  // 初始化默认日期范围
  initDefaultDateRange();
  // 初始化图表
  initCharts();
});

// 组件卸载时销毁图表实例
onUnmounted(() => {
  window.removeEventListener('resize', handleResize);
  saleTrendChartInstance && saleTrendChartInstance.dispose();
  channelDistributionChartInstance && channelDistributionChartInstance.dispose();
  storeRankingChartInstance && storeRankingChartInstance.dispose();
  productRankingChartInstance && productRankingChartInstance.dispose();
  customerGenderChartInstance && customerGenderChartInstance.dispose();
  profitAnalysisChartInstance && profitAnalysisChartInstance.dispose();
  inventoryDistributionChartInstance && inventoryDistributionChartInstance.dispose();
  inventoryTypeChartInstance && inventoryTypeChartInstance.dispose();
});
</script>

<style scoped>
.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.filter-container {
  display: flex;
  gap: 10px;
}

.data-overview {
  margin-bottom: 20px;
}

.data-card {
  height: 120px;
  transition: all 0.3s;
}

.data-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.data-card-content {
  position: relative;
  padding: 10px;
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: center;
}

.data-card-value {
  font-size: 24px;
  font-weight: bold;
  color: #409EFF;
  margin-bottom: 5px;
}

.data-card-title {
  font-size: 14px;
  color: #606266;
}

.data-card-icon {
  position: absolute;
  right: 20px;
  top: 20px;
  font-size: 30px;
  color: #ebeef5;
}

.chart-row {
  margin-bottom: 20px;
}

.chart-card {
  height: 400px;
}

.chart-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.chart-container {
  height: 330px;
}
</style>