<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryRef" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="姓名" prop="memberName">
        <el-input
          v-model="queryParams.memberName"
          placeholder="请输入会员姓名"
          clearable
          style="width: 200px"
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item label="手机号" prop="phoneNumber">
        <el-input
          v-model="queryParams.phoneNumber"
          placeholder="请输入手机号码"
          clearable
          style="width: 200px"
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item label="性别" prop="gender">
        <el-select v-model="queryParams.gender" placeholder="请选择性别" clearable style="width: 200px">
          <el-option
            v-for="dict in sys_user_sex"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="等级" prop="memberLevel">
        <el-select v-model="queryParams.memberLevel" placeholder="请选择等级" clearable style="width: 200px">
          <el-option
            v-for="level in memberLevelOptions"
            :key="level.levelId"
            :label="level.levelName"
            :value="level.levelId"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="注册日期">
        <el-date-picker
          v-model="dateRange"
          style="width: 240px"
          value-format="YYYY-MM-DD"
          type="daterange"
          range-separator="-"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
        ></el-date-picker>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
        <el-button icon="Refresh" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="Plus"
          @click="handleAdd"
          v-hasPermi="['member:member:add']"
        >新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="Edit"
          :disabled="single"
          @click="handleUpdate"
          v-hasPermi="['member:member:edit']"
        >修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="Delete"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['member:member:remove']"
        >删除</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="Download"
          @click="handleExport"
          v-hasPermi="['member:member:export']"
        >导出</el-button>
      </el-col>
      <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="memberList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="会员ID" align="center" prop="memberId" />
      <el-table-column label="会员姓名" align="center" prop="memberName" />
      <el-table-column label="手机号码" align="center" prop="phoneNumber" />
      <el-table-column label="性别" align="center" prop="gender">
        <template #default="scope">
          <dict-tag :options="sys_user_sex" :value="scope.row.gender" />
        </template>
      </el-table-column>
      <el-table-column label="生日" align="center" prop="birthday" width="100">
        <template #default="scope">
          <span>{{ parseTime(scope.row.birthday, '{y}-{m}-{d}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="累计消费" align="center" prop="totalAmount">
        <template #default="scope">
          {{ scope.row.totalAmount }} 元
        </template>
      </el-table-column>
      <el-table-column label="会员等级" align="center" prop="memberLevel">
        <template #default="scope">
          <el-tag v-if="getMemberLevelTag(scope.row.memberLevel)" :type="getMemberLevelTag(scope.row.memberLevel)">
            {{ getMemberLevelName(scope.row.memberLevel) }}
          </el-tag>
          <el-tag v-else>
            {{ getMemberLevelName(scope.row.memberLevel) }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column label="注册时间" align="center" prop="createTime" width="180">
        <template #default="scope">
          <span>{{ parseTime(scope.row.createTime) }}</span>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template #default="scope">
          <el-button link type="primary" icon="View" @click="handleDetail(scope.row)" v-hasPermi="['member:member:query']">查看</el-button>
          <el-button link type="primary" icon="Edit" @click="handleUpdate(scope.row)" v-hasPermi="['member:member:edit']">修改</el-button>
          <el-button link type="primary" icon="Delete" @click="handleDelete(scope.row)" v-hasPermi="['member:member:remove']">删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total>0"
      :total="total"
      :page="queryParams.pageNum"
      :limit="queryParams.pageSize"
      @update:page="queryParams.pageNum = $event"
      @update:limit="queryParams.pageSize = $event"
      @pagination="getList"
    />

    <!-- 添加或修改会员对话框 -->
    <el-dialog :title="title" v-model="open" width="500px" append-to-body>
      <el-form ref="memberRef" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="会员姓名" prop="memberName">
          <el-input v-model="form.memberName" placeholder="请输入会员姓名" />
        </el-form-item>
        <el-form-item label="手机号码" prop="phoneNumber">
          <el-input v-model="form.phoneNumber" placeholder="请输入手机号码" />
        </el-form-item>
        <el-form-item label="性别" prop="gender">
          <el-select v-model="form.gender" placeholder="请选择性别">
            <el-option
              v-for="dict in sys_user_sex"
              :key="dict.value"
              :label="dict.label"
              :value="parseInt(dict.value)"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="生日" prop="birthday">
          <el-date-picker
            v-model="form.birthday"
            type="date"
            placeholder="选择生日"
            style="width: 100%"
            value-format="YYYY-MM-DD"
          />
        </el-form-item>
        <el-form-item label="累计消费" prop="totalAmount">
          <el-input-number v-model="form.totalAmount" :precision="2" :step="100" :min="0" controls-position="right" style="width: 100%" disabled />
          <div class="el-form-item-msg">累计消费金额根据销售订单自动计算，不可手动修改</div>
        </el-form-item>
        <el-form-item label="会员等级" prop="memberLevel">
          <el-select v-model="form.memberLevel" placeholder="请选择会员等级" disabled>
            <el-option
              v-for="level in memberLevelOptions"
              :key="level.levelId"
              :label="level.levelName"
              :value="level.levelId"
            ></el-option>
          </el-select>
          <div class="el-form-item-msg">会员等级根据累计消费金额自动计算</div>
        </el-form-item>
        <el-form-item label="备注" prop="remark">
          <el-input v-model="form.remark" type="textarea" placeholder="请输入备注" />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 会员详情对话框 -->
    <el-dialog title="会员详情" v-model="detailOpen" width="700px" append-to-body>
      <el-descriptions :column="2" border>
        <el-descriptions-item label="会员姓名">{{ detail.memberName }}</el-descriptions-item>
        <el-descriptions-item label="手机号码">{{ detail.phoneNumber }}</el-descriptions-item>
        <el-descriptions-item label="性别">
          <dict-tag :options="sys_user_sex" :value="detail.gender" />
        </el-descriptions-item>
        <el-descriptions-item label="生日">{{ parseTime(detail.birthday, '{y}-{m}-{d}') }}</el-descriptions-item>
        <el-descriptions-item label="累计消费">{{ detail.totalAmount }} 元</el-descriptions-item>
        <el-descriptions-item label="会员等级">
          <el-tag v-if="getMemberLevelTag(detail.memberLevel)" :type="getMemberLevelTag(detail.memberLevel)">
            {{ getMemberLevelName(detail.memberLevel) }}
          </el-tag>
          <el-tag v-else>
            {{ getMemberLevelName(detail.memberLevel) }}
          </el-tag>
        </el-descriptions-item>
        <el-descriptions-item label="注册时间">{{ parseTime(detail.createTime) }}</el-descriptions-item>
        <el-descriptions-item label="最后更新时间">{{ parseTime(detail.updateTime) }}</el-descriptions-item>
        <el-descriptions-item label="备注" :span="2">{{ detail.remark }}</el-descriptions-item>
      </el-descriptions>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="detailOpen = false">关 闭</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="Member">
import { ref, reactive, toRefs, onMounted, getCurrentInstance, watch, computed } from 'vue';
import { listMember, getMember, delMember, addMember, updateMember, exportMember, listAllMemberLevel } from "@/api/member/index";

const { proxy } = getCurrentInstance();
const { sys_user_sex } = proxy.useDict("sys_user_sex");

const memberList = ref([]);
const memberLevelOptions = ref([]);
const open = ref(false);
const detailOpen = ref(false);
const loading = ref(true);
const showSearch = ref(true);
const ids = ref([]);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);
const title = ref("");
const dateRange = ref([]);
const detail = ref({});

// 会员等级配置（默认值，将从后端获取）
const memberLevelConfig = ref([]);

const data = reactive({
  form: {
    memberId: undefined,
    memberName: undefined,
    phoneNumber: undefined,
    gender: undefined,
    birthday: undefined,
    totalAmount: 0,
    memberLevel: 1,
    remark: undefined
  },
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    memberName: undefined,
    phoneNumber: undefined,
    gender: undefined,
    memberLevel: undefined
  },
  rules: {
    memberName: [
      { required: true, message: "会员姓名不能为空", trigger: "blur" },
      { min: 2, max: 20, message: "会员姓名长度必须介于 2 和 20 之间", trigger: "blur" }
    ],
    phoneNumber: [
      { required: true, message: "手机号码不能为空", trigger: "blur" },
      { pattern: /^1[3|4|5|6|7|8|9][0-9]\d{8}$/, message: "请输入正确的手机号码", trigger: "blur" }
    ],
    gender: [
      { required: true, message: "性别不能为空", trigger: "change" }
    ],
    birthday: [
      { required: false, message: "生日不能为空", trigger: "blur" }
    ],
    totalAmount: [
      { required: true, message: "累计消费金额不能为空", trigger: "blur" }
    ]
  }
});

const { queryParams, form, rules } = toRefs(data);

/** 查询会员列表 */
function getList() {
  loading.value = true;
  listMember(proxy.addDateRange(queryParams.value, dateRange.value)).then(response => {
    memberList.value = response.rows;
    total.value = response.total;
    loading.value = false;
  });
}

/** 查询会员等级列表 */
function getMemberLevelList() {
  listAllMemberLevel().then(response => {
    memberLevelConfig.value = response.data;
    memberLevelOptions.value = response.data;
  });
}

/** 取消按钮 */
function cancel() {
  open.value = false;
  reset();
}

/** 表单重置 */
function reset() {
  form.value = {
    memberId: undefined,
    memberName: undefined,
    phoneNumber: undefined,
    gender: undefined,
    birthday: undefined,
    totalAmount: 0,
    memberLevel: 1,
    remark: undefined
  };
  proxy.resetForm("memberRef");
}

/** 搜索按钮操作 */
function handleQuery() {
  queryParams.value.pageNum = 1;
  getList();
}

/** 重置按钮操作 */
function resetQuery() {
  dateRange.value = [];
  proxy.resetForm("queryRef");
  handleQuery();
}

/** 多选框选中数据 */
function handleSelectionChange(selection) {
  ids.value = selection.map(item => item.memberId);
  single.value = selection.length !== 1;
  multiple.value = !selection.length;
}

/** 新增按钮操作 */
function handleAdd() {
  reset();
  open.value = true;
  title.value = "添加会员";
}

/** 修改按钮操作 */
function handleUpdate(row) {
  reset();
  const _memberId = row.memberId || ids.value;
  getMember(_memberId).then(response => {
    form.value = response.data;
    open.value = true;
    title.value = "修改会员";
  });
}

/** 查看详情按钮操作 */
function handleDetail(row) {
  detail.value = {};
  const _memberId = row.memberId || ids.value;
  getMember(_memberId).then(response => {
    detail.value = response.data;
    detailOpen.value = true;
  });
}

/** 提交按钮 */
function submitForm() {
  proxy.$refs["memberRef"].validate(valid => {
    if (valid) {
      // 根据累计消费金额计算会员等级
      form.value.memberLevel = calculateMemberLevel(form.value.totalAmount);

      if (form.value.memberId != undefined) {
        updateMember(form.value).then(response => {
          proxy.$modal.msgSuccess("修改成功");
          open.value = false;
          getList();
        });
      } else {
        addMember(form.value).then(response => {
          proxy.$modal.msgSuccess("新增成功");
          open.value = false;
          getList();
        });
      }
    }
  });
}

/** 删除按钮操作 */
function handleDelete(row) {
  const _memberIds = row.memberId || ids.value;
  proxy.$modal.confirm('是否确认删除会员编号为"' + _memberIds + '"的数据项?').then(function() {
    return delMember(_memberIds);
  }).then(() => {
    getList();
    proxy.$modal.msgSuccess("删除成功");
  }).catch(() => {});
}

/** 导出按钮操作 */
function handleExport() {
  proxy.download('member/export', {
    ...queryParams.value
  }, `member_${new Date().getTime()}.xlsx`);
}

/** 根据累计消费金额计算会员等级 */
function calculateMemberLevel(amount) {
  for (const level of memberLevelConfig.value) {
    if (amount >= level.minAmount && amount <= level.maxAmount) {
      return level.levelId;
    }
  }
  return 1; // 默认为普通会员
}

/** 获取会员等级名称 */
function getMemberLevelName(levelId) {
  const level = memberLevelConfig.value.find(item => item.levelId === parseInt(levelId));
  return level ? level.levelName : '普通会员';
}

/** 获取会员等级标签类型 */
function getMemberLevelTag(levelId) {
  const level = memberLevelConfig.value.find(item => item.levelId === parseInt(levelId));
  return level ? level.tagType : '';
}

// 监听累计消费金额变化，自动计算会员等级
watch(
  () => form.value.totalAmount,
  (newValue) => {
    if (newValue !== undefined) {
      form.value.memberLevel = calculateMemberLevel(newValue);
    }
  }
);

onMounted(() => {
  getList();
  getMemberLevelList();
});
</script>

<style scoped>
.el-form-item-msg {
  font-size: 12px;
  color: #999;
  margin-top: 5px;
}
</style>