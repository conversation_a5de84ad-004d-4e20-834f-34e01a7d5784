<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryRef" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="名称" prop="levelName">
        <el-input
          v-model="queryParams.levelName"
          placeholder="请输入等级名称"
          clearable
          style="width: 200px"
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item label="状态" prop="status">
        <el-select v-model="queryParams.status" placeholder="请选择状态" clearable style="width: 200px">
          <el-option label="正常" :value="1" />
          <el-option label="停用" :value="0" />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
        <el-button icon="Refresh" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="Plus"
          @click="handleAdd"
          v-hasPermi="['member:level:add']"
        >新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="Edit"
          :disabled="single"
          @click="handleUpdate"
          v-hasPermi="['member:level:edit']"
        >修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="Delete"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['member:level:remove']"
        >删除</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="Download"
          @click="handleExport"
          v-hasPermi="['member:level:export']"
        >导出</el-button>
      </el-col>
      <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="levelList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="等级ID" align="center" prop="levelId" />
      <el-table-column label="等级名称" align="center" prop="levelName" />
      <el-table-column label="最小消费金额" align="center" prop="minAmount">
        <template #default="scope">
          {{ scope.row.minAmount }} 元
        </template>
      </el-table-column>
      <el-table-column label="最大消费金额" align="center" prop="maxAmount">
        <template #default="scope">
          {{ scope.row.maxAmount === 999999.99 ? '无上限' : scope.row.maxAmount + ' 元' }}
        </template>
      </el-table-column>
      <el-table-column label="折扣率" align="center" prop="discountRate">
        <template #default="scope">
          {{ (scope.row.discountRate * 100).toFixed(0) }}%
        </template>
      </el-table-column>
      <el-table-column label="等级标签" align="center" prop="tagType">
        <template #default="scope">
          <el-tag v-if="scope.row.tagType" :type="scope.row.tagType">
            {{ scope.row.levelName }}
          </el-tag>
          <el-tag v-else>
            {{ scope.row.levelName }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column label="状态" align="center" prop="status">
        <template #default="scope">
          <el-tag type="success" v-if="scope.row.status === 1">正常</el-tag>
          <el-tag type="danger" v-else-if="scope.row.status === 0">停用</el-tag>
        </template>
      </el-table-column>
      <el-table-column label="创建时间" align="center" prop="createTime" width="180">
        <template #default="scope">
          <span>{{ parseTime(scope.row.createTime) }}</span>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template #default="scope">
          <el-button link type="primary" icon="Edit" @click="handleUpdate(scope.row)" v-hasPermi="['member:level:edit']">修改</el-button>
          <el-button link type="primary" icon="Delete" @click="handleDelete(scope.row)" v-hasPermi="['member:level:remove']">删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total>0"
      :total="total"
      :page="queryParams.pageNum"
      :limit="queryParams.pageSize"
      @update:page="queryParams.pageNum = $event"
      @update:limit="queryParams.pageSize = $event"
      @pagination="getList"
    />

    <!-- 添加或修改会员等级对话框 -->
    <el-dialog :title="title" v-model="open" width="500px" append-to-body>
      <el-form ref="levelRef" :model="form" :rules="rules" label-width="100px">
        <el-form-item label="等级名称" prop="levelName">
          <el-input v-model="form.levelName" placeholder="请输入等级名称" />
        </el-form-item>
        <el-form-item label="最小消费金额" prop="minAmount">
          <el-input-number v-model="form.minAmount" :precision="2" :step="100" :min="0" controls-position="right" style="width: 100%" />
        </el-form-item>
        <el-form-item label="最大消费金额" prop="maxAmount">
          <el-input-number v-model="form.maxAmount" :precision="2" :step="1000" :min="0" controls-position="right" style="width: 100%" />
        </el-form-item>
        <el-form-item label="折扣率" prop="discountRate">
          <el-slider
            v-model="discountRatePercent"
            :min="50"
            :max="100"
            :step="1"
            :format-tooltip="formatDiscountTooltip"
            style="width: 100%"
          />
        </el-form-item>
        <el-form-item label="标签类型" prop="tagType">
          <el-select v-model="form.tagType" placeholder="请选择标签类型" style="width: 100%">
            <el-option label="默认" value="" />
            <el-option label="信息 (灰色)" value="info" />
            <el-option label="成功 (绿色)" value="success" />
            <el-option label="警告 (黄色)" value="warning" />
            <el-option label="危险 (红色)" value="danger" />
          </el-select>
          <div class="tag-preview">
            预览:
            <el-tag v-if="form.tagType" :type="form.tagType">{{ form.levelName || '会员等级' }}</el-tag>
            <el-tag v-else>{{ form.levelName || '会员等级' }}</el-tag>
          </div>
        </el-form-item>
        <el-form-item label="状态" prop="status">
          <el-radio-group v-model="form.status">
            <el-radio :label="1">正常</el-radio>
            <el-radio :label="0">停用</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="备注" prop="remark">
          <el-input v-model="form.remark" type="textarea" placeholder="请输入备注" />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="MemberLevel">
import { ref, reactive, toRefs, onMounted, getCurrentInstance, computed, watch } from 'vue';
import { listMemberLevel, getMemberLevel, delMemberLevel, addMemberLevel, updateMemberLevel, exportMemberLevel } from "@/api/member/index";

const { proxy } = getCurrentInstance();

const levelList = ref([]);
const open = ref(false);
const loading = ref(true);
const showSearch = ref(true);
const ids = ref([]);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);
const title = ref("");

// 折扣率百分比值（用于滑块显示）
const discountRatePercent = computed({
  get: () => {
    return form.value.discountRate ? form.value.discountRate * 100 : 100;
  },
  set: (val) => {
    form.value.discountRate = val / 100;
  }
});

// 格式化折扣率提示
const formatDiscountTooltip = (val) => {
  return val + '%';
};

const data = reactive({
  form: {
    levelId: undefined,
    levelName: undefined,
    minAmount: 0,
    maxAmount: 0,
    discountRate: 1,
    tagType: '',
    status: 1,
    remark: undefined
  },
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    levelName: undefined,
    status: undefined
  },
  rules: {
    levelName: [
      { required: true, message: "等级名称不能为空", trigger: "blur" },
      { min: 2, max: 20, message: "等级名称长度必须介于 2 和 20 之间", trigger: "blur" }
    ],
    minAmount: [
      { required: true, message: "最小消费金额不能为空", trigger: "blur" }
    ],
    maxAmount: [
      { required: true, message: "最大消费金额不能为空", trigger: "blur" }
    ],
    discountRate: [
      { required: true, message: "折扣率不能为空", trigger: "blur" }
    ],
    status: [
      { required: true, message: "状态不能为空", trigger: "change" }
    ]
  }
});

const { queryParams, form, rules } = toRefs(data);

/** 查询会员等级列表 */
function getList() {
  loading.value = true;
  listMemberLevel(queryParams.value).then(response => {
    levelList.value = response.rows;
    total.value = response.total;
    loading.value = false;
  });
}

/** 取消按钮 */
function cancel() {
  open.value = false;
  reset();
}

/** 表单重置 */
function reset() {
  form.value = {
    levelId: undefined,
    levelName: undefined,
    minAmount: 0,
    maxAmount: 0,
    discountRate: 1,
    tagType: '',
    status: 1,
    remark: undefined
  };
  proxy.resetForm("levelRef");
}

/** 搜索按钮操作 */
function handleQuery() {
  queryParams.value.pageNum = 1;
  getList();
}

/** 重置按钮操作 */
function resetQuery() {
  proxy.resetForm("queryRef");
  handleQuery();
}

/** 多选框选中数据 */
function handleSelectionChange(selection) {
  ids.value = selection.map(item => item.levelId);
  single.value = selection.length !== 1;
  multiple.value = !selection.length;
}

/** 新增按钮操作 */
function handleAdd() {
  reset();
  open.value = true;
  title.value = "添加会员等级";
}

/** 修改按钮操作 */
function handleUpdate(row) {
  reset();
  const _levelId = row.levelId || ids.value;
  getMemberLevel(_levelId).then(response => {
    form.value = response.data;
    open.value = true;
    title.value = "修改会员等级";
  });
}

/** 提交按钮 */
function submitForm() {
  proxy.$refs["levelRef"].validate(valid => {
    if (valid) {
      if (form.value.minAmount >= form.value.maxAmount) {
        proxy.$modal.msgError("最小消费金额必须小于最大消费金额");
        return;
      }

      if (form.value.levelId != undefined) {
        updateMemberLevel(form.value).then(response => {
          proxy.$modal.msgSuccess("修改成功");
          open.value = false;
          getList();
        });
      } else {
        addMemberLevel(form.value).then(response => {
          proxy.$modal.msgSuccess("新增成功");
          open.value = false;
          getList();
        });
      }
    }
  });
}

/** 删除按钮操作 */
function handleDelete(row) {
  const _levelIds = row.levelId || ids.value;
  proxy.$modal.confirm('是否确认删除会员等级编号为"' + _levelIds + '"的数据项?').then(function() {
    return delMemberLevel(_levelIds);
  }).then(() => {
    getList();
    proxy.$modal.msgSuccess("删除成功");
  }).catch(() => {});
}

/** 导出按钮操作 */
function handleExport() {
  proxy.download('member/level/export', {
    ...queryParams.value
  }, `member_level_${new Date().getTime()}.xlsx`);
}

// 监听等级名称变化，更新预览
watch(
  () => form.value.levelName,
  (newValue) => {
    if (!newValue) {
      form.value.levelName = '';
    }
  }
);

onMounted(() => {
  getList();
});
</script>

<style scoped>
.tag-preview {
  margin-top: 8px;
  font-size: 12px;
  color: #606266;
}
</style>
