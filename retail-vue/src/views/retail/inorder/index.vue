<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryRef" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="入库单号" prop="orderNumber">
        <el-input
          v-model="queryParams.orderNumber"
          placeholder="请输入入库单号"
          clearable
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item label="渠道名称" prop="channelName">
        <el-input
          v-model="queryParams.channelName"
          placeholder="请输入渠道名称"
          clearable
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item label="期货状态" prop="isFutures">
        <el-select v-model="queryParams.isFutures" placeholder="请选择期货状态" clearable style="width: 200px">
          <el-option :key="true" label="是" :value="true" />
          <el-option :key="false" label="否" :value="false" />
        </el-select>
      </el-form-item>
      <el-form-item label="入库日期" prop="createTime">
        <el-date-picker
          v-model="dateRange"
          value-format="YYYY-MM-DD"
          type="daterange"
          range-separator="-"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
        ></el-date-picker>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
        <el-button icon="Refresh" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="Plus"
          @click="handleAdd"
          v-hasPermi="['retail:inorder:add']"
        >新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="Download"
          @click="handleExport"
          v-hasPermi="['retail:inorder:export']"
        >导出</el-button>
      </el-col>
      <right-toolbar :model-value="showSearch" @update:model-value="showSearch = $event" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="inOrderList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="入库单ID" align="center" prop="id" />
      <el-table-column label="入库单号" align="center" prop="orderNumber" />
      <el-table-column label="渠道名称" align="center" prop="channelName" />
      <el-table-column label="是否期货" align="center" prop="isFutures">
        <template #default="scope">
          <el-tag :type="scope.row.isFutures ? 'warning' : 'success'">{{ scope.row.isFutures ? '是' : '否' }}</el-tag>
        </template>
      </el-table-column>
      <el-table-column label="期货物流" align="center" prop="futuresLogistics" />
      <el-table-column label="创建日期" align="center" prop="createTime" width="180">
        <template #default="scope">
          <span>{{ parseTime(scope.row.createTime) }}</span>
        </template>
      </el-table-column>
      <el-table-column label="更新日期" align="center" prop="updateTime" width="180">
        <template #default="scope">
          <span>{{ parseTime(scope.row.updateTime) }}</span>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template #default="scope">
          <el-button link type="primary" icon="View" @click="handleDetail(scope.row)" v-hasPermi="['retail:inorder:query']">查看</el-button>
          <el-button link type="primary" icon="Edit" @click="handleUpdate(scope.row)" v-hasPermi="['retail:inorder:edit']">修改</el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total>0"
      :total="total"
      :page="queryParams.pageNum"
      @update:page="queryParams.pageNum = $event"
      :limit="queryParams.pageSize"
      @update:limit="queryParams.pageSize = $event"
      @pagination="getList"
    />

    <!-- 添加或修改入库单对话框 -->
    <el-dialog :title="title" v-model="open" width="1100px" append-to-body :close-on-click-modal="false">
      <el-form ref="inOrderRef" :model="form" :rules="rules" label-width="100px">
        <el-row>
          <el-col :span="12">
            <el-form-item label="渠道" prop="channelId">
              <el-select
                v-model="form.channelId"
                placeholder="请选择渠道"
                filterable
                clearable
                :disabled="isEditMode"
              >
                <el-option
                  v-for="dict in channelOptions"
                  :key="dict.id"
                  :label="dict.channelName"
                  :value="dict.id"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="入库单号" prop="orderNumber">
              <el-input v-model="form.orderNumber" placeholder="系统自动生成" disabled />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="是否期货" prop="isFutures">
              <el-radio-group v-model="form.isFutures">
                <el-radio :value="true" :label="true">是</el-radio>
                <el-radio :value="false" :label="false">否</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="期货物流" prop="futuresLogistics" v-if="form.isFutures">
              <el-input v-model="form.futuresLogistics" placeholder="请输入期货物流" />
            </el-form-item>
          </el-col>
        </el-row>

        <!-- 入库明细表格 -->
        <el-divider content-position="center">入库明细</el-divider>
        <el-row>
          <el-col :span="24">
            <el-button type="primary" @click="handleAddDetail" icon="Plus" :disabled="isEditMode">添加明细</el-button>
            <div v-if="isEditMode" style="margin-top: 10px; color: #f56c6c; font-size: 12px;">
              <el-icon><InfoFilled /></el-icon>
              修改模式下不允许编辑入库明细
            </div>
            <el-table :data="form.detailList" style="width: 100%; margin-top: 10px;" height="400" :scrollbar-always-on="true">
              <el-table-column label="序号" type="index" width="60" />
              <el-table-column label="商品图" width="80" v-if="false">
                <template #default="scope">
                  <image-preview :src="scope.row.productImage" :width="50" :height="50"/>
                </template>
              </el-table-column>
              <el-table-column label="商品" prop="productName" width="220">
                <template #default="scope">
                  <el-select
                    v-model="scope.row.productId"
                    placeholder="搜索商品"
                    filterable
                    remote
                    :remote-method="(query) => remoteProductSearch(query, scope.$index)"
                    :loading="productSearchLoading"
                    @change="handleProductChange(scope.$index)"
                    :disabled="isEditMode"
                  >
                    <el-option
                      v-for="item in filteredProductOptions"
                      :key="item.id"
                      :label="item.productNumber"
                      :value="item.id"
                    >
                      <div style="display: flex; align-items: center;">
                        <image-preview :src="item.productImage" :width="30" :height="30"/>
                        <span>{{ item.productNumber }} - {{ item.productName || item.brand }}</span>
                      </div>
                    </el-option>
                  </el-select>
                </template>
              </el-table-column>
              <el-table-column label="门店" prop="storeId" width="200">
                <template #default="scope">
                  <el-select
                    v-model="scope.row.storeId"
                    placeholder="选择门店"
                    filterable
                    @change="handleStoreChange(scope.$index)"
                    :disabled="isEditMode"
                  >
                    <el-option
                      v-for="item in storeOptions"
                      :key="item.id"
                      :label="item.storeName"
                      :value="item.id"
                    />
                  </el-select>
                </template>
              </el-table-column>
              <el-table-column label="柜台" prop="counterId" width="200">
                <template #default="scope">
                  <el-select
                    v-model="scope.row.counterId"
                    placeholder="选择柜台"
                    filterable
                    :disabled="isEditMode"
                  >
                    <el-option
                      v-for="item in getCountersByStore(scope.row.storeId)"
                      :key="item.id"
                      :label="item.counterName"
                      :value="item.id"
                    />
                  </el-select>
                </template>
              </el-table-column>
              <el-table-column label="数量" prop="quantity" width="140">
                <template #default="scope">
                  <el-input-number v-model="scope.row.quantity" :min="1" controls-position="right" :disabled="isEditMode" />
                </template>
              </el-table-column>
              <el-table-column label="进货价" prop="purchasePrice" width="140">
                <template #default="scope">
                  <el-input-number v-model="scope.row.purchasePrice" :min="0" :precision="2" controls-position="right" :disabled="isEditMode" />
                </template>
              </el-table-column>
              <el-table-column label="操作" width="100" v-if="!isEditMode">
                <template #default="scope">
                  <el-button type="danger" icon="Delete" @click="handleDeleteDetail(scope.$index)">删除</el-button>
                </template>
              </el-table-column>

            </el-table>
          </el-col>
        </el-row>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 入库单详情对话框 -->
    <el-dialog title="入库单详情" v-model="detailOpen" width="900px" append-to-body :close-on-click-modal="false">
      <el-descriptions :column="2" border>
        <el-descriptions-item label="入库单号">{{ detail.orderNumber }}</el-descriptions-item>
        <el-descriptions-item label="渠道名称">{{ detail.channelName }}</el-descriptions-item>
        <el-descriptions-item label="是否期货">{{ detail.isFutures ? '是' : '否' }}</el-descriptions-item>
        <el-descriptions-item label="期货物流">{{ detail.futuresLogistics }}</el-descriptions-item>
        <el-descriptions-item label="创建日期" :span="2">{{ parseTime(detail.createTime) }}</el-descriptions-item>
      </el-descriptions>

      <el-divider content-position="center">入库明细</el-divider>
      <el-table :data="detail.detailList">
        <el-table-column label="序号" type="index" width="50" />
        <el-table-column label="商品图" width="80">
          <template #default="scope">
            <image-preview :src="scope.row.productImage" :width="50" :height="50"/>
          </template>
        </el-table-column>
        <el-table-column label="商品" prop="productName" show-overflow-tooltip />
        <el-table-column label="货号" prop="productNumber" show-overflow-tooltip />
        <el-table-column label="门店" prop="storeName" />
        <el-table-column label="柜台" prop="counterName" />
        <el-table-column label="数量" prop="quantity" />
        <el-table-column label="进货价" prop="purchasePrice">
          <template #default="scope">
            {{ scope.row.purchasePrice }} 元
          </template>
        </el-table-column>
        <el-table-column label="批次号" prop="batchNumber" show-overflow-tooltip />
      </el-table>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="detailOpen = false">关 闭</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="InventoryInOrder">
import { ref, reactive, toRefs, onMounted, getCurrentInstance, computed } from 'vue';
import { InfoFilled } from '@element-plus/icons-vue';
import { listInOrder, getInOrder, delInOrder, addInOrder, saveInOrderWithDetails, updateInOrder } from "@/api/retail/inventory";
import { listAllProducts } from "@/api/retail/products";
import { listAllChannels } from "@/api/retail/channel";
import { listAllStores } from "@/api/retail/store";
import { listCounterByStore } from "@/api/retail/counter";

const { proxy } = getCurrentInstance();

const inOrderList = ref([]);
const open = ref(false);
const detailOpen = ref(false);
const loading = ref(true);
const showSearch = ref(true);
const ids = ref([]);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);
const title = ref("");
const dateRange = ref([]);
const detail = ref({});

// 渠道选项
const channelOptions = ref([]);

// 门店选项
const storeOptions = ref([]);

// 柜台选项 - 使用Map存储不同门店的柜台数据
const counterOptions = ref([]);
const countersByStore = ref(new Map());

// 商品选项
const productOptions = ref([]);

// 过滤后的商品选项
const filteredProductOptions = ref([]);

// 商品搜索加载状态
const productSearchLoading = ref(false);

// 判断是否为修改模式
const isEditMode = computed(() => {
  return form.value.id != null && form.value.id > 0;
});

const data = reactive({
  form: {
    id: null,
    orderNumber: null,
    channelId: null,
    isFutures: false,
    futuresLogistics: null,
    detailList: []
  },
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    orderNumber: null,
    channelName: null,
    isFutures: null
  },
  rules: {
    channelId: [{ required: true, message: "渠道不能为空", trigger: "blur" }],
    isFutures: [{ required: true, message: "请选择是否期货", trigger: "change" }],
  }
});

const { queryParams, form, rules } = toRefs(data);

/** 查询入库单列表 */
function getList() {
  loading.value = true;
  listInOrder(proxy.addDateRange(queryParams.value, dateRange.value)).then(response => {
    inOrderList.value = response.rows;
    total.value = response.total;
    loading.value = false;
  });
}

/** 查询渠道列表 */
function getChannelList() {
  listAllChannels().then(response => {
    channelOptions.value = response.data;
  });
}

/** 查询门店列表 */
function getStoreList() {
  listAllStores().then(response => {
    storeOptions.value = response.data;
    // 预加载所有门店的柜台数据
    preloadAllCounters();
  });
}

/** 预加载所有门店的柜台数据 */
function preloadAllCounters() {
  storeOptions.value.forEach(store => {
    if (store.id) {
      listCounterByStore(store.id).then(response => {
        const counters = response.data;
        // 缓存数据
        countersByStore.value.set(store.id, counters);
        // 合并到全局柜台选项中
        counters.forEach(counter => {
          const existingIndex = counterOptions.value.findIndex(c => c.id === counter.id);
          if (existingIndex === -1) {
            counterOptions.value.push(counter);
          }
        });
      });
    }
  });
}

/** 根据门店ID获取柜台列表（数据已预加载，此函数保留用于兼容性） */
function getCounterList(storeId) {
  // 数据已在页面初始化时预加载，无需重复请求
  // 此函数保留是为了兼容现有的调用，但实际上不执行任何操作
}

/** 根据门店ID获取对应的柜台列表 */
function getCountersByStore(storeId) {
  if (!storeId) {
    return [];
  }
  return countersByStore.value.get(storeId) || [];
}

/** 查询商品列表 */
function getProductList() {
  listAllProducts().then(response => {
    productOptions.value = response.data;
    filteredProductOptions.value = response.data;
  });
}

/** 远程搜索商品 */
function remoteProductSearch(query, index) {
  if (query) {
    productSearchLoading.value = true;

    // 延迟执行，避免频繁请求
    setTimeout(() => {
      filteredProductOptions.value = productOptions.value.filter(item => {
        // 搜索商品编号、名称或品牌
        return (
          (item.productNumber && item.productNumber.toLowerCase().includes(query.toLowerCase())) ||
          (item.productName && item.productName.toLowerCase().includes(query.toLowerCase())) ||
          (item.brand && item.brand.toLowerCase().includes(query.toLowerCase()))
        );
      });
      productSearchLoading.value = false;
    }, 200);
  } else {
    filteredProductOptions.value = productOptions.value;
  }
}

// 商品选择变更处理
function handleProductChange(index) {
  const selectedProduct = productOptions.value.find(item => item.id === form.value.detailList[index].productId);
  if (selectedProduct) {
    form.value.detailList[index].productName = selectedProduct.productName || selectedProduct.productNumber;
    form.value.detailList[index].productImage = selectedProduct.productImage;
  }
}

// 门店选择变更处理
function handleStoreChange(index) {
  const storeId = form.value.detailList[index].storeId;
  form.value.detailList[index].counterId = null; // 清空已选柜台
  getCounterList(storeId);
}

// 取消按钮
function cancel() {
  open.value = false;
  reset();
}

// 表单重置
function reset() {
  form.value = {
    id: null,
    orderNumber: null,
    channelId: null,
    isFutures: false,
    futuresLogistics: null,
    detailList: []
  };
  proxy.resetForm("inOrderRef");
}

/** 搜索按钮操作 */
function handleQuery() {
  queryParams.value.pageNum = 1;
  getList();
}

/** 重置按钮操作 */
function resetQuery() {
  dateRange.value = [];
  proxy.resetForm("queryRef");
  handleQuery();
}

// 多选框选中数据
function handleSelectionChange(selection) {
  ids.value = selection.map(item => item.id);
  single.value = selection.length != 1;
  multiple.value = !selection.length;
}

/** 新增按钮操作 */
function handleAdd() {
  reset();
  open.value = true;
  title.value = "添加入库单";
  // 不需要重复请求，数据已在页面加载时获取
}

/** 修改按钮操作 */
function handleUpdate(row) {
  reset();
  const loadingInstance = proxy.$loading({
    lock: true,
    text: '正在加载入库单详情...',
    background: 'rgba(0, 0, 0, 0.7)'
  });

  const _id = row.id || ids.value;
  getInOrder(_id).then(response => {
    form.value = response.data;
    if (!form.value.detailList) {
      form.value.detailList = [];
    }
    open.value = true;
    title.value = "修改入库单";

    // 不需要重复请求基础数据，只需要加载相关的柜台信息
    // 如果有门店信息，获取对应的柜台信息
    if (form.value.detailList && form.value.detailList.length > 0) {
      const storeIds = [...new Set(form.value.detailList.map(item => item.storeId))];
      storeIds.forEach(storeId => {
        if (storeId) {
          getCounterList(storeId);
        }
      });
    }
  }).catch(error => {
    console.error("获取入库单详情失败:", error);
    proxy.$modal.msgError("获取入库单详情失败: " + (error.message || "未知错误"));
  }).finally(() => {
    loadingInstance.close();
  });
}

/** 查看详情按钮操作 */
function handleDetail(row) {
  detail.value = {};
  const _id = row.id || ids.value;
  getInOrder(_id).then(response => {
    detail.value = response.data;
    detailOpen.value = true;
  });
}

/** 添加明细行 */
function handleAddDetail() {
  form.value.detailList.push({
    productId: null,
    productName: null,
    productImage: null,
    storeId: null,
    counterId: null,
    quantity: 1,
    purchasePrice: 0.00
  });
}

/** 删除明细行 */
function handleDeleteDetail(index) {
  form.value.detailList.splice(index, 1);
}

/** 提交按钮 */
function submitForm() {
  proxy.$refs["inOrderRef"].validate(valid => {
    if (valid) {
      if (form.value.detailList.length === 0) {
        proxy.$modal.msgError("请添加至少一条入库明细");
        return;
      }

      if (form.value.id != null) {
        updateInOrder(form.value).then(response => {
          proxy.$modal.msgSuccess("修改成功");
          open.value = false;
          getList();
        }).catch(error => {
          console.error("修改入库单失败:", error);
          proxy.$modal.msgError("修改失败: " + (error.message || "未知错误"));
        });
      } else {
        saveInOrderWithDetails(form.value).then(response => {
          proxy.$modal.msgSuccess("新增成功");
          open.value = false;
          getList();
        }).catch(error => {
          console.error("新增入库单失败:", error);
          proxy.$modal.msgError("新增失败: " + (error.message || "未知错误"));
        });
      }
    }
  });
}

/** 删除按钮操作 */
function handleDelete(row) {
  const _ids = row.id || ids.value;
  proxy.$modal.confirm('是否确认删除入库单编号为"' + _ids + '"的数据项？').then(function() {
    return delInOrder(_ids);
  }).then(() => {
    getList();
    proxy.$modal.msgSuccess("删除成功");
  }).catch(() => {});
}

/** 导出按钮操作 */
function handleExport() {
  proxy.download('retail/inorder/export', {
    ...queryParams.value
  }, `inorder_${new Date().getTime()}.xlsx`)
}

onMounted(() => {
  getList();
  getChannelList();
  getStoreList();
  getProductList();
});
</script>