<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryRef" :inline="true" v-show="showSearch" label-width="68px" @submit.prevent>
      <el-form-item label="货号" prop="productNumber">
        <el-input
          v-model="queryParams.productNumber"
          placeholder="请输入商品货号"
          clearable
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item label="名称" prop="productName">
        <el-input
          v-model="queryParams.productName"
          placeholder="请输入商品名称"
          clearable
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item label="品牌" prop="brand">
        <el-input
          v-model="queryParams.brand"
          placeholder="请输入品牌"
          clearable
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item label="品类" prop="category">
        <el-input
          v-model="queryParams.category"
          placeholder="请输入品类"
          clearable
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item label="门店" prop="storeName">
        <el-input
          v-model="queryParams.storeName"
          placeholder="请输入门店名称"
          clearable
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
        <el-button icon="Refresh" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="Download"
          @click="handleExport"
          v-hasPermi="['retail:storestock:export']"
        >导出</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="Sell"
          @click="handleBatchSale"
          v-hasPermi="['retail:storestock:sale']"
        >批量售出</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="Calendar"
          @click="handleBatchReserve"
          v-hasPermi="['retail:storestock:reserve']"
        >批量预订</el-button>
      </el-col>
      <right-toolbar :model-value="showSearch" @update:model-value="showSearch = $event" @queryTable="getList"></right-toolbar>
    </el-row>

    <!-- 商品合并的库存列表 -->
    <el-table v-loading="loading" :data="mergedStockList">
      <el-table-column label="商品图" align="center" width="80">
        <template #default="scope">
          <image-preview :src="scope.row.productImage" :width="50" :height="50"/>
        </template>
      </el-table-column>
      <el-table-column label="商品货号" align="center" prop="productNumber" />
      <el-table-column label="商品名称" align="center" prop="productName" />
      <el-table-column label="品牌" align="center" prop="brand" />
      <el-table-column label="品类" align="center" prop="category" />
      <el-table-column label="总库存量" align="center" prop="totalQuantity" />
      <el-table-column label="门店数" align="center" prop="storeCount" />
      <el-table-column label="期货状态" align="center">
        <template #default="scope">
          <el-tag :type="scope.row.hasFutures ? 'warning' : 'success'">
            {{ scope.row.hasFutures ? '含期货' : '现货' }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width" width="220">
        <template #default="scope">
          <el-button link type="primary" icon="View" @click="handleProductDetail(scope.row)" v-hasPermi="['retail:storestock:query']">查看</el-button>
          <el-button
            v-if="scope.row.totalQuantity > 0"
            link
            type="success"
            icon="Sell"
            @click="handleSaleProduct(scope.row)"
            v-hasPermi="['retail:storestock:sale']"
          >售出</el-button>
          <el-button
            v-if="!scope.row.hasNonFuturesStock"
            link
            type="warning"
            icon="Calendar"
            @click="handleReserveProduct(scope.row)"
            v-hasPermi="['retail:storestock:reserve']"
          >预订</el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total>0"
      :total="total"
      :page="queryParams.pageNum"
      @update:page="queryParams.pageNum = $event"
      :limit="queryParams.pageSize"
      @update:limit="queryParams.pageSize = $event"
      @pagination="getList"
    />

    <!-- 产品库存详情对话框 -->
    <el-dialog title="库存明细" v-model="productDetailOpen" width="900px" append-to-body>
      <div v-if="currentProduct" class="mb-3">
        <el-descriptions :column="4" border>
          <el-descriptions-item label="商品货号" :span="1">{{ currentProduct.productNumber }}</el-descriptions-item>
          <el-descriptions-item label="商品名称" :span="1">{{ currentProduct.productName }}</el-descriptions-item>
          <el-descriptions-item label="品牌" :span="1">{{ currentProduct.brand }}</el-descriptions-item>
          <el-descriptions-item label="品类" :span="1">{{ currentProduct.category }}</el-descriptions-item>
        </el-descriptions>
      </div>

      <el-table v-loading="detailLoading" :data="stockDetailsList">
        <el-table-column label="库存ID" align="center" prop="id" width="80" />
        <el-table-column label="门店" align="center" prop="storeName" />
        <el-table-column label="柜台" align="center" prop="counterName" />
        <el-table-column label="渠道" align="center" prop="channelName" />
        <el-table-column label="批次号" align="center" prop="batchNumber" />
        <el-table-column label="期货状态" align="center" width="80">
          <template #default="scope">
            <el-tag :type="scope.row.isFutures ? 'warning' : 'success'">{{ scope.row.isFutures ? '是' : '否' }}</el-tag>
          </template>
        </el-table-column>
        <el-table-column label="库存数量" align="center" prop="quantity" width="80" />
        <el-table-column label="入库时间" align="center" width="150">
          <template #default="scope">
            <span>{{ parseTime(scope.row.createTime) }}</span>
          </template>
        </el-table-column>
        <el-table-column label="操作" align="center" width="140">
          <template #default="scope">
            <el-button link type="primary" icon="View" @click="handleDetail(scope.row)" v-hasPermi="['retail:storestock:query']">查看</el-button>
            <el-button
              v-if="!scope.row.isFutures && scope.row.quantity > 0"
              link
              type="success"
              icon="Sell"
              @click="handleSaleStockItem(scope.row)"
              v-hasPermi="['retail:storestock:sale']"
            >售出</el-button>
          </template>
        </el-table-column>
      </el-table>

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="productDetailOpen = false">关 闭</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 库存详情对话框 -->
    <el-dialog title="库存详情" v-model="detailOpen" width="700px" append-to-body>
      <el-tabs type="border-card">
        <el-tab-pane label="基本信息">
          <el-descriptions :column="2" border>
            <el-descriptions-item label="商品图" :span="2">
              <image-preview :src="detail.productImage" :width="100" :height="100"/>
            </el-descriptions-item>
            <el-descriptions-item label="商品货号">{{ detail.productNumber }}</el-descriptions-item>
            <el-descriptions-item label="商品名称">{{ detail.productName }}</el-descriptions-item>
            <el-descriptions-item label="门店名称">{{ detail.storeName }}</el-descriptions-item>
            <el-descriptions-item label="柜台名称">{{ detail.counterName }}</el-descriptions-item>
            <el-descriptions-item label="渠道名称">{{ detail.channelName }}</el-descriptions-item>
            <el-descriptions-item label="批次号">{{ detail.batchNumber }}</el-descriptions-item>
            <el-descriptions-item label="期货状态">
              <el-tag :type="detail.isFutures ? 'warning' : 'success'">{{ detail.isFutures ? '是' : '否' }}</el-tag>
            </el-descriptions-item>
            <el-descriptions-item label="库存数量">{{ detail.quantity }}</el-descriptions-item>
            <el-descriptions-item label="入库时间" :span="2">{{ parseTime(detail.createTime) }}</el-descriptions-item>
          </el-descriptions>
        </el-tab-pane>
        <el-tab-pane label="商品详情">
          <el-descriptions :column="2" border>
            <el-descriptions-item label="品牌" :span="2">{{ detail.brand }}</el-descriptions-item>
            <el-descriptions-item label="品类">{{ detail.category }}</el-descriptions-item>
            <el-descriptions-item label="规格">{{ detail.specification }}</el-descriptions-item>
            <el-descriptions-item label="颜色">{{ detail.color }}</el-descriptions-item>
            <el-descriptions-item label="材质">{{ detail.material }}</el-descriptions-item>
            <el-descriptions-item label="原产地">{{ detail.origin }}</el-descriptions-item>
            <el-descriptions-item label="执行标准">{{ detail.standard }}</el-descriptions-item>
            <el-descriptions-item label="安全类别">{{ detail.safetyCategory }}</el-descriptions-item>
            <el-descriptions-item label="产品等级">{{ detail.productGrade }}</el-descriptions-item>
            <el-descriptions-item label="国内参考价" :span="2">{{ detail.referencePrice }} 元</el-descriptions-item>
            <el-descriptions-item label="门店零售价" :span="2">{{ detail.retailPrice }} 元</el-descriptions-item>
          </el-descriptions>
        </el-tab-pane>
      </el-tabs>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="detailOpen = false">关 闭</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 商品售出对话框 -->
    <el-dialog title="商品售出" v-model="saleDialogOpen" width="500px" append-to-body>
      <div v-if="saleItem" class="mb-3">
        <el-descriptions :column="1" border>
          <el-descriptions-item label="商品图">
            <image-preview :src="saleItem.productImage" :width="80" :height="80"/>
          </el-descriptions-item>
          <el-descriptions-item label="商品货号">{{ saleItem.productNumber }}</el-descriptions-item>
          <el-descriptions-item label="商品名称">{{ saleItem.productName }}</el-descriptions-item>
          <el-descriptions-item label="品牌">{{ saleItem.brand }}</el-descriptions-item>
          <el-descriptions-item label="品类">{{ saleItem.category }}</el-descriptions-item>
          <el-descriptions-item label="建议零售价">{{ saleItem.retailPrice }} 元</el-descriptions-item>
        </el-descriptions>
      </div>

      <el-form :model="saleForm" ref="saleFormRef" :rules="saleRules" label-width="100px" @submit.prevent>
        <el-form-item label="实际售价" prop="salePrice">
          <el-input-number v-model="saleForm.salePrice" :min="1" :precision="2" :step="10" style="width: 100%" @change="calculateSingleDiscount"></el-input-number>
          <div v-if="saleItem && saleItem.retailPrice && saleForm.salePrice" style="margin-top: 5px; font-size: 12px; color: #666;">
            零售价: ¥{{ saleItem.retailPrice }} |
            <span :style="{ color: singleDiscountRate < 1 ? '#f56c6c' : '#67c23a' }">
              {{ singleDiscountRate < 1 ? `${(singleDiscountRate * 10).toFixed(1)}折` : '无折扣' }}
            </span>
          </div>
        </el-form-item>
        <el-form-item label="支付方式" prop="payType">
          <el-select v-model="saleForm.payType" placeholder="请选择支付方式" style="width: 100%">
            <el-option
              v-for="item in retail_pay_type"
              :key="item.value"
              :label="item.label"
              :value="Number(item.value)"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="会员查询" prop="customerPhone">
          <div class="member-search-container">
            <el-input v-model="saleForm.customerPhone" placeholder="输入手机号查询会员（必选）" style="width: 250px; margin-right: 10px;" />
            <el-button @click="searchMember(saleForm.customerPhone)" :loading="memberSearchLoading">
              <el-icon><Search /></el-icon> 搜索
            </el-button>
          </div>
        </el-form-item>
        <!-- 移除了新增会员表单项，改为在底部显示 -->
        <el-form-item v-if="foundMember" label="会员信息" class="member-info">
          <div class="member-card compact">
            <div class="member-header">
              <div class="member-header-left">
                <el-tag :type="getMemberLevelTag(foundMember.memberLevel)">
                  {{ getMemberLevelName(foundMember.memberLevel) }}
                </el-tag>
                <span class="member-name">{{ foundMember.memberName }}</span>
              </div>
              <span class="member-id">ID: {{ foundMember.memberId }}</span>
            </div>
            <div class="member-body">
              <div class="member-row">
                <div class="member-item">
                  <span class="label">手机号:</span>
                  <span class="value">{{ foundMember.phoneNumber }}</span>
                </div>
                <div class="member-item">
                  <span class="label">累计消费:</span>
                  <span class="value">{{ foundMember.totalAmount }} 元</span>
                </div>
              </div>
            </div>
          </div>
          <input type="hidden" v-model="saleForm.memberId">
        </el-form-item>
        <el-form-item label="实际售出时间" prop="actualSaleTime">
          <el-date-picker
            v-model="saleForm.actualSaleTime"
            type="datetime"
            placeholder="选择实际售出时间"
            format="YYYY-MM-DD HH:mm:ss"
            value-format="YYYY-MM-DD HH:mm:ss"
            style="width: 100%"
          />
          <div class="form-help-text">默认为当前时间，可根据实际情况修改</div>
        </el-form-item>
        <el-form-item label="备注" prop="remark">
          <el-input v-model="saleForm.remark" type="textarea" :rows="3" placeholder="请输入备注信息（选填）"></el-input>
        </el-form-item>
      </el-form>

      <template #footer>
        <div class="dialog-footer">
          <el-button v-if="!foundMember" type="primary" @click="openNewMemberDialog('single')" plain>
            <el-icon><Plus /></el-icon> 新增会员
          </el-button>
          <div style="flex-grow: 1;"></div>
          <el-button @click="saleDialogOpen = false">取 消</el-button>
          <el-button type="primary" @click="submitSale" :loading="submitLoading">确 定</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 批量售出对话框 -->
    <el-dialog title="批量商品售出" v-model="batchSaleDialogOpen" width="900px" append-to-body>
      <el-tabs type="border-card">
        <el-tab-pane label="添加商品">
          <el-form :model="productSearchForm" ref="productSearchFormRef" :rules="productSearchRules" inline class="mb-3" @submit.prevent>
            <el-form-item label="商品货号" prop="productNumber">
              <el-input v-model="productSearchForm.productNumber" placeholder="请输入商品货号" clearable @keyup.enter="handleSearchProduct" />
            </el-form-item>
            <el-form-item>
              <el-button type="primary" @click="handleSearchProduct" :loading="searchLoading">查找商品</el-button>
              <el-button @click="resetProductSearch">重置</el-button>
            </el-form-item>
          </el-form>

          <div v-if="searchedProduct" class="searched-product">
            <el-card shadow="hover">
              <div class="search-result-card">
                <div class="product-image">
                  <image-preview :src="searchedProduct.productImage" :width="100" :height="100"/>
                </div>
                <div class="product-info">
                  <div class="info-row"><span class="label">商品货号:</span> {{ searchedProduct.productNumber }}</div>
                  <div class="info-row"><span class="label">商品名称:</span> {{ searchedProduct.productName }}</div>
                  <div class="info-row"><span class="label">品牌:</span> {{ searchedProduct.brand }}</div>
                  <div class="info-row"><span class="label">品类:</span> {{ searchedProduct.category }}</div>
                </div>
                <div class="product-action">
                  <el-button
                    type="primary"
                    @click="handleViewStockDetails(searchedProduct)">
                    查看库存明细
                  </el-button>
                </div>
              </div>
            </el-card>
          </div>

          <!-- 库存明细列表 -->
          <div v-if="showStockDetails" class="stock-details-list">
            <div class="stock-details-header">
              <h4>库存明细</h4>
            </div>
            <el-table :data="stockDetailsList" max-height="300px" style="margin-bottom: 20px; width: 100%;">
              <el-table-column label="批次号" align="center" prop="batchNumber" min-width="120" />
              <el-table-column label="柜台" align="center" prop="counterName" min-width="100" />
              <el-table-column label="渠道" align="center" prop="channelName" min-width="100" />
              <el-table-column label="库存数量" align="center" prop="quantity" min-width="100" />
              <el-table-column label="期货" align="center" min-width="80">
                <template #default="scope">
                  {{ scope.row.isFutures ? '是' : '否' }}
                </template>
              </el-table-column>
              <el-table-column label="数量" align="center" min-width="120">
                <template #default="scope">
                  <el-input-number
                    v-model="scope.row.saleQuantity"
                    :min="1"
                    :max="scope.row.quantity"
                    size="small"
                    :disabled="scope.row.quantity <= 0 || scope.row.isFutures"
                  ></el-input-number>
                </template>
              </el-table-column>
              <el-table-column label="操作" align="center" min-width="120">
                <template #default="scope">
                  <el-button
                    link
                    type="primary"
                    @click="handleAddStockToSelected(scope.row)"
                    :disabled="scope.row.quantity <= 0 || scope.row.isFutures">
                    添加
                  </el-button>
                </template>
              </el-table-column>
            </el-table>
          </div>
        </el-tab-pane>

        <el-tab-pane label="已选商品">
          <div class="selected-products-header">
            <h4>已选商品 ({{ selectedProducts.length }})</h4>
            <el-button type="danger" plain size="small" @click="clearSelectedProducts" :disabled="selectedProducts.length === 0">
              清空已选商品
            </el-button>
          </div>

          <el-table :data="selectedProducts" max-height="400px" style="margin-bottom: 20px;">
            <el-table-column label="商品图" align="center" width="80">
              <template #default="scope">
                <image-preview :src="scope.row.productImage" :width="50" :height="50"/>
              </template>
            </el-table-column>
            <el-table-column label="商品货号" align="center" prop="productNumber" width="120" />
            <el-table-column label="商品名称" align="center" prop="productName" min-width="120" />
            <el-table-column label="品牌" align="center" prop="brand" width="100" />
            <el-table-column label="柜台" align="center" prop="counterName" width="100" />
            <el-table-column label="渠道" align="center" prop="channelName" width="100" />
            <el-table-column label="零售价" align="center" width="100">
              <template #default="scope">
                ¥{{ (scope.row.retailPrice || 0).toFixed(2) }}
              </template>
            </el-table-column>
            <el-table-column label="数量" align="center" width="120">
              <template #default="scope">
                <el-input-number
                  v-model="scope.row.saleQuantity"
                  :min="1"
                  :max="scope.row.quantity"
                  size="small"
                  @change="updateTotalAmount"
                ></el-input-number>
              </template>
            </el-table-column>
            <el-table-column label="分配价格" align="center" width="100">
              <template #default="scope">
                <span v-if="batchSaleForm.totalSalePrice && selectedProducts.length > 0" style="color: #409eff;">
                  ¥{{ getAllocatedPrice(scope.$index).toFixed(2) }}
                </span>
                <span v-else>-</span>
              </template>
            </el-table-column>
            <el-table-column label="操作" align="center" width="80">
              <template #default="scope">
                <el-button link type="danger" icon="Delete" @click="handleRemoveSelected(scope.$index)">移除</el-button>
              </template>
            </el-table-column>
          </el-table>

          <div class="batch-sale-summary">
            <div class="summary-item">
              <span class="summary-label">商品总数:</span>
              <span class="summary-value">{{ totalQuantity }} 件</span>
            </div>
            <div class="summary-item">
              <span class="summary-label">商品总价:</span>
              <span class="summary-value">{{ calculateTotalRetailPrice() }} 元</span>
            </div>
          </div>
        </el-tab-pane>
      </el-tabs>

      <el-divider>客户信息</el-divider>

      <el-form :model="batchSaleForm" ref="batchSaleFormRef" :rules="batchSaleRules" label-width="100px" @submit.prevent>
        <el-form-item label="实际售价" prop="totalSalePrice">
          <el-input-number v-model="batchSaleForm.totalSalePrice" :min="1" :precision="2" :step="10" style="width: 100%" @change="calculateBatchDiscount"></el-input-number>
          <div class="form-help-text">
            默认为所有商品零售价总和，可根据实际情况修改
            <span v-if="selectedProducts.length > 0 && batchSaleForm.totalSalePrice" style="margin-left: 10px;">
              | 零售价总计: ¥{{ calculateTotalRetailPrice() }} |
              <span :style="{ color: batchDiscountRate < 1 ? '#f56c6c' : '#67c23a' }">
                {{ batchDiscountRate < 1 ? `${(batchDiscountRate * 10).toFixed(1)}折` : '无折扣' }}
              </span>
            </span>
          </div>
        </el-form-item>
        <el-form-item label="支付方式" prop="payType">
          <el-select v-model="batchSaleForm.payType" placeholder="请选择支付方式" style="width: 100%">
            <el-option
              v-for="item in retail_pay_type"
              :key="item.value"
              :label="item.label"
              :value="Number(item.value)"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="会员查询" prop="customerPhone">
          <div class="member-search-container">
            <el-input v-model="batchSaleForm.customerPhone" placeholder="输入手机号查询会员（必选）" style="width: 250px; margin-right: 10px;" />
            <el-button @click="searchMember(batchSaleForm.customerPhone, 'batch')" :loading="memberSearchLoading">
              <el-icon><Search /></el-icon> 搜索
            </el-button>
          </div>
        </el-form-item>
        <!-- 移除了新增会员表单项，改为在底部显示 -->
        <el-form-item v-if="foundMember && searchMode === 'batch'" label="会员信息" class="member-info">
          <div class="member-card compact">
            <div class="member-header">
              <div class="member-header-left">
                <el-tag :type="getMemberLevelTag(foundMember.memberLevel)">
                  {{ getMemberLevelName(foundMember.memberLevel) }}
                </el-tag>
                <span class="member-name">{{ foundMember.memberName }}</span>
              </div>
              <span class="member-id">ID: {{ foundMember.memberId }}</span>
            </div>
            <div class="member-body">
              <div class="member-row">
                <div class="member-item">
                  <span class="label">手机号:</span>
                  <span class="value">{{ foundMember.phoneNumber }}</span>
                </div>
                <div class="member-item">
                  <span class="label">累计消费:</span>
                  <span class="value">{{ foundMember.totalAmount }} 元</span>
                </div>
              </div>
            </div>
          </div>
          <input type="hidden" v-model="batchSaleForm.memberId">
        </el-form-item>
        <el-form-item label="实际售出时间" prop="actualSaleTime">
          <el-date-picker
            v-model="batchSaleForm.actualSaleTime"
            type="datetime"
            placeholder="选择实际售出时间"
            format="YYYY-MM-DD HH:mm:ss"
            value-format="YYYY-MM-DD HH:mm:ss"
            style="width: 100%"
          />
          <div class="form-help-text">默认为当前时间，可根据实际情况修改</div>
        </el-form-item>
        <el-form-item label="备注" prop="remark">
          <el-input v-model="batchSaleForm.remark" type="textarea" :rows="3" placeholder="请输入备注信息（选填）"></el-input>
        </el-form-item>
      </el-form>

      <template #footer>
        <div class="dialog-footer">
          <el-button v-if="!foundMember || searchMode !== 'batch'" type="primary" @click="openNewMemberDialog('batch')" plain>
            <el-icon><Plus /></el-icon> 新增会员
          </el-button>
          <div style="flex-grow: 1;"></div>
          <el-button @click="batchSaleDialogOpen = false">取 消</el-button>
          <el-button type="primary" @click="submitBatchSale" :loading="submitLoading" :disabled="selectedProducts.length === 0">确 定</el-button>
        </div>
      </template>
    </el-dialog>
    <!-- 商品预订对话框 -->
    <el-dialog title="商品预订" v-model="reserveDialogOpen" width="500px" append-to-body>
      <div v-if="reserveItem" class="mb-3">
        <el-descriptions :column="1" border>
          <el-descriptions-item label="商品图">
            <image-preview :src="reserveItem.productImage" :width="80" :height="80"/>
          </el-descriptions-item>
          <el-descriptions-item label="商品货号">{{ reserveItem.productNumber }}</el-descriptions-item>
          <el-descriptions-item label="商品名称">{{ reserveItem.productName }}</el-descriptions-item>
          <el-descriptions-item label="品牌">{{ reserveItem.brand }}</el-descriptions-item>
          <el-descriptions-item label="品类">{{ reserveItem.category }}</el-descriptions-item>
          <el-descriptions-item label="建议零售价">{{ reserveItem.retailPrice }} 元</el-descriptions-item>
        </el-descriptions>
      </div>

      <el-form :model="reserveForm" ref="reserveFormRef" :rules="reserveRules" label-width="100px" @submit.prevent>
        <el-form-item label="实际售价" prop="salePrice">
          <el-input-number v-model="reserveForm.salePrice" :min="1" :precision="2" :step="10" style="width: 100%" @change="calculateReserveDiscount"></el-input-number>
          <div v-if="reserveItem && reserveItem.retailPrice && reserveForm.salePrice" style="margin-top: 5px; font-size: 12px; color: #666;">
            零售价: ¥{{ reserveItem.retailPrice }} |
            <span :style="{ color: reserveDiscountRate < 1 ? '#f56c6c' : '#67c23a' }">
              {{ reserveDiscountRate < 1 ? `${(reserveDiscountRate * 10).toFixed(1)}折` : '无折扣' }}
            </span>
          </div>
        </el-form-item>
        <el-form-item label="支付方式" prop="payType">
          <el-select v-model="reserveForm.payType" placeholder="请选择支付方式" style="width: 100%">
            <el-option
              v-for="item in retail_pay_type"
              :key="item.value"
              :label="item.label"
              :value="Number(item.value)"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="预订类型" prop="depositType">
          <el-radio-group v-model="reserveForm.depositType">
            <el-radio :value="0">定金</el-radio>
            <el-radio :value="1">全款</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="定金金额" prop="depositAmount" v-if="reserveForm.depositType === 0">
          <el-input-number v-model="reserveForm.depositAmount" :min="1" :max="reserveForm.salePrice" :precision="2" :step="10" style="width: 100%"></el-input-number>
          <div class="form-help-text">定金金额不能超过商品售价</div>
        </el-form-item>
        <el-form-item label="会员查询" prop="customerPhone">
          <div class="member-search-container">
            <el-input v-model="reserveForm.customerPhone" placeholder="输入手机号查询会员（必选）" style="width: 250px; margin-right: 10px;" />
            <el-button @click="searchMember(reserveForm.customerPhone, 'reserve')" :loading="memberSearchLoading">
              <el-icon><Search /></el-icon> 搜索
            </el-button>
          </div>
        </el-form-item>
        <el-form-item v-if="foundMember && searchMode === 'reserve'" label="会员信息" class="member-info">
          <div class="member-card compact">
            <div class="member-header">
              <div class="member-header-left">
                <el-tag :type="getMemberLevelTag(foundMember.memberLevel)">
                  {{ getMemberLevelName(foundMember.memberLevel) }}
                </el-tag>
                <span class="member-name">{{ foundMember.memberName }}</span>
              </div>
              <span class="member-id">ID: {{ foundMember.memberId }}</span>
            </div>
            <div class="member-body">
              <div class="member-row">
                <div class="member-item">
                  <span class="label">手机号:</span>
                  <span class="value">{{ foundMember.phoneNumber }}</span>
                </div>
                <div class="member-item">
                  <span class="label">累计消费:</span>
                  <span class="value">{{ foundMember.totalAmount }} 元</span>
                </div>
              </div>
            </div>
          </div>
          <input type="hidden" v-model="reserveForm.memberId">
        </el-form-item>
        <el-form-item label="实际售出时间" prop="actualSaleTime">
          <el-date-picker
            v-model="reserveForm.actualSaleTime"
            type="datetime"
            placeholder="选择实际售出时间"
            format="YYYY-MM-DD HH:mm:ss"
            value-format="YYYY-MM-DD HH:mm:ss"
            style="width: 100%"
          />
          <div class="form-help-text">默认为当前时间，可根据实际情况修改</div>
        </el-form-item>
        <el-form-item label="备注" prop="remark">
          <el-input v-model="reserveForm.remark" type="textarea" :rows="3" placeholder="请输入备注信息（选填）"></el-input>
        </el-form-item>
      </el-form>

      <template #footer>
        <div class="dialog-footer">
          <el-button v-if="!foundMember || searchMode !== 'reserve'" type="primary" @click="openNewMemberDialog('reserve')" plain>
            <el-icon><Plus /></el-icon> 新增会员
          </el-button>
          <div style="flex-grow: 1;"></div>
          <el-button @click="reserveDialogOpen = false">取 消</el-button>
          <el-button type="primary" @click="submitReserve" :loading="submitLoading">确 定</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 批量预订对话框 -->
    <el-dialog title="批量商品预订" v-model="batchReserveDialogOpen" width="900px" append-to-body>
      <el-tabs type="border-card">
        <el-tab-pane label="添加商品">
          <el-form :model="productSearchForm" ref="productSearchFormRef" :rules="productSearchRules" inline class="mb-3" @submit.prevent>
            <el-form-item label="商品货号" prop="productNumber">
              <el-input v-model="productSearchForm.productNumber" placeholder="请输入商品货号" clearable @keyup.enter="handleSearchProduct" />
            </el-form-item>
            <el-form-item>
              <el-button type="primary" @click="handleSearchProduct" :loading="searchLoading">查找商品</el-button>
              <el-button @click="resetProductSearch">重置</el-button>
            </el-form-item>
          </el-form>

          <div v-if="searchedProduct" class="searched-product">
            <el-card shadow="hover">
              <div class="search-result-card">
                <div class="product-image">
                  <image-preview :src="searchedProduct.productImage" :width="100" :height="100"/>
                </div>
                <div class="product-info">
                  <div class="info-row"><span class="label">商品货号:</span> {{ searchedProduct.productNumber }}</div>
                  <div class="info-row"><span class="label">商品名称:</span> {{ searchedProduct.productName }}</div>
                  <div class="info-row"><span class="label">品牌:</span> {{ searchedProduct.brand }}</div>
                  <div class="info-row"><span class="label">品类:</span> {{ searchedProduct.category }}</div>
                </div>
                <div class="product-action">
                  <el-button
                    type="primary"
                    @click="handleAddProductToReserve(searchedProduct)">
                    添加到预订
                  </el-button>
                </div>
              </div>
            </el-card>
          </div>
        </el-tab-pane>

        <el-tab-pane label="已选商品">
          <div class="selected-products-header">
            <h4>已选商品 ({{ selectedProducts.length }})</h4>
            <el-button type="danger" plain size="small" @click="clearSelectedProducts" :disabled="selectedProducts.length === 0">
              清空已选商品
            </el-button>
          </div>

          <el-table :data="selectedProducts" max-height="400px" style="margin-bottom: 20px;">
            <el-table-column label="商品图" align="center" width="80">
              <template #default="scope">
                <image-preview :src="scope.row.productImage" :width="50" :height="50"/>
              </template>
            </el-table-column>
            <el-table-column label="商品货号" align="center" prop="productNumber" width="120" />
            <el-table-column label="商品名称" align="center" prop="productName" min-width="120" />
            <el-table-column label="品牌" align="center" prop="brand" width="100" />
            <el-table-column label="零售价" align="center" width="100">
              <template #default="scope">
                ¥{{ (scope.row.retailPrice || 0).toFixed(2) }}
              </template>
            </el-table-column>
            <el-table-column label="数量" align="center" width="120">
              <template #default="scope">
                <el-input-number
                  v-model="scope.row.saleQuantity"
                  :min="1"
                  size="small"
                  @change="updateTotalAmount"
                ></el-input-number>
              </template>
            </el-table-column>
            <el-table-column label="分配价格" align="center" width="100">
              <template #default="scope">
                <span v-if="batchReserveForm.totalSalePrice && selectedProducts.length > 0" style="color: #409eff;">
                  ¥{{ getReserveAllocatedPrice(scope.$index).toFixed(2) }}
                </span>
                <span v-else>-</span>
              </template>
            </el-table-column>
            <el-table-column label="操作" align="center" width="80">
              <template #default="scope">
                <el-button link type="danger" icon="Delete" @click="handleRemoveSelected(scope.$index)">移除</el-button>
              </template>
            </el-table-column>
          </el-table>

          <div class="batch-sale-summary">
            <div class="summary-item">
              <span class="summary-label">商品总数:</span>
              <span class="summary-value">{{ totalQuantity }} 件</span>
            </div>
            <div class="summary-item">
              <span class="summary-label">商品总价:</span>
              <span class="summary-value">{{ calculateTotalRetailPrice() }} 元</span>
            </div>
          </div>
        </el-tab-pane>
      </el-tabs>

      <el-divider>预订信息</el-divider>

      <el-form :model="batchReserveForm" ref="batchReserveFormRef" :rules="batchReserveRules" label-width="100px" @submit.prevent>
        <el-form-item label="实际售价" prop="totalSalePrice">
          <el-input-number v-model="batchReserveForm.totalSalePrice" :min="1" :precision="2" :step="10" style="width: 100%" @change="calculateBatchReserveDiscount"></el-input-number>
          <div class="form-help-text">
            默认为所有商品零售价总和，可根据实际情况修改
            <span v-if="selectedProducts.length > 0 && batchReserveForm.totalSalePrice" style="margin-left: 10px;">
              | 零售价总计: ¥{{ calculateTotalRetailPrice() }} |
              <span :style="{ color: batchReserveDiscountRate < 1 ? '#f56c6c' : '#67c23a' }">
                {{ batchReserveDiscountRate < 1 ? `${(batchReserveDiscountRate * 10).toFixed(1)}折` : '无折扣' }}
              </span>
            </span>
          </div>
        </el-form-item>
        <el-form-item label="支付方式" prop="payType">
          <el-select v-model="batchReserveForm.payType" placeholder="请选择支付方式" style="width: 100%">
            <el-option
              v-for="item in retail_pay_type"
              :key="item.value"
              :label="item.label"
              :value="Number(item.value)"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="预订类型" prop="depositType">
          <el-radio-group v-model="batchReserveForm.depositType">
            <el-radio :value="0">定金</el-radio>
            <el-radio :value="1">全款</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="定金金额" prop="depositAmount" v-if="batchReserveForm.depositType === 0">
          <el-input-number v-model="batchReserveForm.depositAmount" :min="1" :max="batchReserveForm.totalSalePrice" :precision="2" :step="10" style="width: 100%"></el-input-number>
          <div class="form-help-text">定金金额不能超过商品总售价</div>
        </el-form-item>
        <el-form-item label="会员查询" prop="customerPhone">
          <div class="member-search-container">
            <el-input v-model="batchReserveForm.customerPhone" placeholder="输入手机号查询会员（必选）" style="width: 250px; margin-right: 10px;" />
            <el-button @click="searchMember(batchReserveForm.customerPhone, 'batchReserve')" :loading="memberSearchLoading">
              <el-icon><Search /></el-icon> 搜索
            </el-button>
          </div>
        </el-form-item>
        <el-form-item v-if="foundMember && searchMode === 'batchReserve'" label="会员信息" class="member-info">
          <div class="member-card compact">
            <div class="member-header">
              <div class="member-header-left">
                <el-tag :type="getMemberLevelTag(foundMember.memberLevel)">
                  {{ getMemberLevelName(foundMember.memberLevel) }}
                </el-tag>
                <span class="member-name">{{ foundMember.memberName }}</span>
              </div>
              <span class="member-id">ID: {{ foundMember.memberId }}</span>
            </div>
            <div class="member-body">
              <div class="member-row">
                <div class="member-item">
                  <span class="label">手机号:</span>
                  <span class="value">{{ foundMember.phoneNumber }}</span>
                </div>
                <div class="member-item">
                  <span class="label">累计消费:</span>
                  <span class="value">{{ foundMember.totalAmount }} 元</span>
                </div>
              </div>
            </div>
          </div>
          <input type="hidden" v-model="batchReserveForm.memberId">
        </el-form-item>
        <el-form-item label="实际售出时间" prop="actualSaleTime">
          <el-date-picker
            v-model="batchReserveForm.actualSaleTime"
            type="datetime"
            placeholder="选择实际售出时间"
            format="YYYY-MM-DD HH:mm:ss"
            value-format="YYYY-MM-DD HH:mm:ss"
            style="width: 100%"
          />
          <div class="form-help-text">默认为当前时间，可根据实际情况修改</div>
        </el-form-item>
        <el-form-item label="备注" prop="remark">
          <el-input v-model="batchReserveForm.remark" type="textarea" :rows="3" placeholder="请输入备注信息（选填）"></el-input>
        </el-form-item>
      </el-form>

      <template #footer>
        <div class="dialog-footer">
          <el-button v-if="!foundMember || searchMode !== 'batchReserve'" type="primary" @click="openNewMemberDialog('batchReserve')" plain>
            <el-icon><Plus /></el-icon> 新增会员
          </el-button>
          <div style="flex-grow: 1;"></div>
          <el-button @click="batchReserveDialogOpen = false">取 消</el-button>
          <el-button type="primary" @click="submitBatchReserve" :loading="submitLoading" :disabled="selectedProducts.length === 0">确 定</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 新增会员对话框 -->
    <el-dialog v-model="newMemberDialogOpen" title="新增会员" width="500px" append-to-body>
      <el-form ref="newMemberFormRef" :model="newMemberForm" :rules="newMemberRules" label-width="100px">
        <el-form-item label="会员姓名" prop="memberName">
          <el-input v-model="newMemberForm.memberName" placeholder="请输入会员姓名" />
        </el-form-item>
        <el-form-item label="手机号码" prop="phoneNumber">
          <el-input v-model="newMemberForm.phoneNumber" placeholder="请输入手机号码" />
        </el-form-item>
        <el-form-item label="性别" prop="gender">
          <el-radio-group v-model="newMemberForm.gender">
            <el-radio :value="0">女</el-radio>
            <el-radio :value="1">男</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="出生日期" prop="birthday">
          <el-date-picker
            v-model="newMemberForm.birthday"
            type="date"
            placeholder="请选择出生日期"
            value-format="YYYY-MM-DD"
          />
        </el-form-item>
        <el-form-item label="备注" prop="remark">
          <el-input v-model="newMemberForm.remark" type="textarea" placeholder="请输入备注" />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="newMemberDialogOpen = false">取 消</el-button>
          <el-button type="primary" @click="submitNewMember" :loading="newMemberSubmitLoading">确 定</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="StoreStock">
import { ref, reactive, toRefs, getCurrentInstance, onMounted } from 'vue';
import { listStoreStock, getStoreStock, listMergedStoreStock, getProductStockDetails, getEarliestNonFuturesStock, createOutOrder, createBatchOutOrder } from "@/api/retail/storestock";
import { reserveProduct, batchReserveProduct } from "@/api/retail/reserve";
import { getUserStore } from "@/api/retail/store";
import { searchMemberByPhone, addMember, listAllMemberLevel } from "@/api/member/index";
import { ElMessage } from 'element-plus';

// 引入字典数据
const { proxy } = getCurrentInstance();
const { retail_pay_type } = proxy.useDict("retail_pay_type");

const mergedStockList = ref([]);
const stockDetailsList = ref([]);
const detailOpen = ref(false);
const productDetailOpen = ref(false);
const saleDialogOpen = ref(false);
const batchSaleDialogOpen = ref(false); // 批量售出对话框
const reserveDialogOpen = ref(false); // 商品预订对话框
const batchReserveDialogOpen = ref(false); // 批量预订对话框
const reserveItem = ref(null); // 当前预订的商品
const loading = ref(true);
const detailLoading = ref(false);
const submitLoading = ref(false);
const searchLoading = ref(false); // 商品搜索加载状态
const memberSearchLoading = ref(false); // 会员搜索加载状态
const foundMember = ref(null); // 搜索到的会员信息
const searchMode = ref('single'); // 搜索模式：single 或 batch
const newMemberDialogOpen = ref(false); // 新增会员对话框
const newMemberSubmitLoading = ref(false); // 新增会员提交加载状态
const currentSaleMode = ref('single'); // 当前售出模式：single 或 batch
const showSearch = ref(true);
const showStockDetails = ref(false); // 控制是否显示库存明细
const total = ref(0);
const detail = ref({});
const currentProduct = ref(null);
const saleItem = ref(null);
const selectedProducts = ref([]); // 已选择的商品列表
const searchedProduct = ref(null); // 搜索到的商品
const currentUserStoreId = ref(null); // 当前用户所属门店ID
const totalQuantity = ref(0); // 已选商品总数量
const memberLevelConfig = ref([]); // 会员等级配置
const singleDiscountRate = ref(1); // 单个商品折扣率
const batchDiscountRate = ref(1); // 批量商品折扣率
const reserveDiscountRate = ref(1); // 单个预订商品折扣率
const batchReserveDiscountRate = ref(1); // 批量预订商品折扣率

// 商品搜索表单
const productSearchForm = ref({
  productNumber: ''
});

// 商品搜索表单校验规则
const productSearchRules = {
  productNumber: [
    { required: true, message: '请输入商品货号', trigger: 'blur' }
  ]
};

// 售出表单
const saleForm = ref({
  stockId: null,
  productId: null,
  salePrice: 0,
  payType: 1, // 支付方式
  customerPhone: '', // 保留用于会员搜索
  memberId: null, // 会员ID
  actualSaleTime: '', // 实际售出时间
  remark: ''
});

// 表单校验规则
const saleRules = {
  salePrice: [
    { required: true, message: '请输入售价', trigger: 'blur' },
    { type: 'number', min: 1, message: '售价必须大于0', trigger: 'blur' }
  ],
  payType: [
    { required: true, message: '请选择支付方式', trigger: 'change' }
  ],
  customerPhone: [
    { required: false, message: '请输入会员手机号', trigger: 'blur' },
    { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号码', trigger: 'blur' }
  ],
  memberId: [
    { required: true, message: '请选择会员', trigger: 'change' }
  ]
};

const data = reactive({
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    productNumber: null,
    productName: null,
    brand: null,
    category: null,
    storeName: null
  }
});

const { queryParams } = toRefs(data);

// 获取会员等级配置
function getMemberLevelConfig() {
  listAllMemberLevel().then(response => {
    if (response.code === 200 && response.data) {
      memberLevelConfig.value = response.data;
    }
  }).catch(() => {
  });
}

// 页面挂载时获取当前用户的门店ID
onMounted(() => {
  // 获取会员等级配置
  getMemberLevelConfig();

  // 根据实际API调整获取当前用户门店的方法
  getUserStore().then(response => {
    if (response.code === 200 && response.data) {
      currentUserStoreId.value = response.data.id;
    }
    getList(); // 获取列表数据
  }).catch(() => {
    getList(); // 如果获取门店ID失败，也继续获取列表
  });
});

/** 查询合并后的门店库存列表 */
function getList() {
  loading.value = true;

  // 如果有门店ID，则按门店查询
  if (currentUserStoreId.value) {
    queryParams.value.storeId = currentUserStoreId.value;
  }

  listMergedStoreStock(queryParams.value).then(response => {
    mergedStockList.value = response.rows;
    total.value = response.total;
    loading.value = false;
  });
}

/** 搜索按钮操作 */
function handleQuery() {
  queryParams.value.pageNum = 1;
  getList();
}

/** 重置按钮操作 */
function resetQuery() {
  proxy.resetForm("queryRef");
  // 保留门店ID查询条件
  if (currentUserStoreId.value) {
    queryParams.value.storeId = currentUserStoreId.value;
  }
  handleQuery();
}

/** 查看商品详细库存 */
function handleProductDetail(row) {
  currentProduct.value = row;
  detailLoading.value = true;
  stockDetailsList.value = [];

  const params = {
    storeName: queryParams.value.storeName
  };

  // 如果有门店ID，则按门店查询
  if (currentUserStoreId.value) {
    params.storeId = currentUserStoreId.value;
  }

  getProductStockDetails(row.productId, params).then(response => {
    stockDetailsList.value = response.data || [];
    productDetailOpen.value = true;
    detailLoading.value = false;
  }).catch(() => {
    detailLoading.value = false;
  });
}

/** 查看单个库存详情按钮操作 */
function handleDetail(row) {
  detail.value = {};
  getStoreStock(row.id).then(response => {
    detail.value = response.data;
    detailOpen.value = true;
  });
}

/** 导出按钮操作 */
function handleExport() {
  proxy.download('retail/storestock/export', {
    ...queryParams.value
  }, `storestock_${new Date().getTime()}.xlsx`)
}

/** 商品售出操作（从合并列表） */
function handleSaleProduct(row) {
  // 获取该产品最早入库的非期货库存，并传入当前门店ID
  getEarliestNonFuturesStock(row.productId, currentUserStoreId.value).then(response => {
    if (response.code === 200 && response.data) {
      saleItem.value = response.data;
      prepareSaleForm(response.data);
      saleDialogOpen.value = true;
    } else {
      ElMessage.warning('该商品没有可售出的库存，所有库存都是期货或数量为0');
    }
  });
}

/** 商品售出操作（从详细列表） */
function handleSaleStockItem(row) {
  if (row.isFutures || row.quantity <= 0) {
    ElMessage.warning('该库存不可售出');
    return;
  }

  // 验证是否属于当前用户的门店
  if (currentUserStoreId.value && row.storeId !== currentUserStoreId.value) {
    ElMessage.warning('您只能操作自己门店的库存');
    return;
  }

  saleItem.value = row;
  prepareSaleForm(row);
  saleDialogOpen.value = true;
}

/** 准备售出表单 */
function prepareSaleForm(item) {
  // 重置会员信息
  foundMember.value = null;

  // 获取当前时间作为默认实际售出时间
  const now = new Date();
  const currentTime = now.getFullYear() + '-' +
    String(now.getMonth() + 1).padStart(2, '0') + '-' +
    String(now.getDate()).padStart(2, '0') + ' ' +
    String(now.getHours()).padStart(2, '0') + ':' +
    String(now.getMinutes()).padStart(2, '0') + ':' +
    String(now.getSeconds()).padStart(2, '0');

  saleForm.value = {
    stockId: item.id,
    productId: item.productId,
    salePrice: item.retailPrice || 0,
    payType: 1, // 默认值，根据实际情况调整
    customerPhone: '',
    memberId: null,
    actualSaleTime: currentTime,
    remark: ''
  };

  // 计算初始折扣
  calculateSingleDiscount();
}

/** 计算单个商品折扣率 */
function calculateSingleDiscount() {
  if (saleItem.value && saleItem.value.retailPrice && saleForm.value.salePrice) {
    singleDiscountRate.value = saleForm.value.salePrice / saleItem.value.retailPrice;
  } else {
    singleDiscountRate.value = 1;
  }
}

/** 计算批量商品折扣率 */
function calculateBatchDiscount() {
  const totalRetailPrice = calculateTotalRetailPrice();
  if (totalRetailPrice > 0 && batchSaleForm.value.totalSalePrice) {
    batchDiscountRate.value = batchSaleForm.value.totalSalePrice / totalRetailPrice;
  } else {
    batchDiscountRate.value = 1;
  }
}

/** 计算单个预订商品折扣率 */
function calculateReserveDiscount() {
  if (reserveItem.value && reserveItem.value.retailPrice && reserveForm.value.salePrice) {
    reserveDiscountRate.value = reserveForm.value.salePrice / reserveItem.value.retailPrice;
  } else {
    reserveDiscountRate.value = 1;
  }
}

/** 计算批量预订商品折扣率 */
function calculateBatchReserveDiscount() {
  const totalRetailPrice = calculateTotalRetailPrice();
  if (totalRetailPrice > 0 && batchReserveForm.value.totalSalePrice) {
    batchReserveDiscountRate.value = batchReserveForm.value.totalSalePrice / totalRetailPrice;
  } else {
    batchReserveDiscountRate.value = 1;
  }
}

/** 提交售出 */
function submitSale() {
  proxy.$refs["saleFormRef"].validate(valid => {
    if (valid) {
      submitLoading.value = true;

      const outOrderData = {
        stockId: saleForm.value.stockId,
        productId: saleForm.value.productId,
        salePrice: saleForm.value.salePrice,
        payType: saleForm.value.payType,
        memberId: saleForm.value.memberId,
        actualSaleTime: saleForm.value.actualSaleTime,
        remark: saleForm.value.remark
      };

      createOutOrder(outOrderData).then(() => {
        ElMessage.success('提交审核成功');
        saleDialogOpen.value = false;
        // 刷新列表
        getList();
        // 如果详情页面打开，刷新详情
        if (productDetailOpen.value && currentProduct.value) {
          handleProductDetail(currentProduct.value);
        }
        submitLoading.value = false;
      }).catch(() => {
        submitLoading.value = false;
      });
    }
  });
}

// 批量售出表单
const batchSaleForm = ref({
  payType: 1, // 支付方式
  customerPhone: '', // 保留用于会员搜索
  memberId: null, // 会员ID
  totalSalePrice: 1.00, // 总实际售价
  actualSaleTime: '', // 实际售出时间
  remark: ''
});

// 批量售出表单校验规则
const batchSaleRules = {
  payType: [
    { required: true, message: '请选择支付方式', trigger: 'change' }
  ],
  customerPhone: [
    { required: false, message: '请输入会员手机号', trigger: 'blur' },
    { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号码', trigger: 'blur' }
  ],
  memberId: [
    { required: true, message: '请选择会员', trigger: 'change' }
  ],
  totalSalePrice: [
    { required: true, message: '请输入总实际售价', trigger: 'blur' },
    { type: 'number', min: 1, message: '总实际售价必须大于0', trigger: 'blur' }
  ]
};

// 预订表单
const reserveForm = ref({
  productId: null,
  salePrice: 0,
  payType: 1, // 支付方式
  depositType: 0, // 预订支付类型（0定金 1全款）
  depositAmount: 0, // 定金金额
  customerPhone: '', // 保留用于会员搜索
  memberId: null, // 会员ID
  actualSaleTime: '', // 实际售出时间
  remark: ''
});

// 预订表单校验规则
const reserveRules = {
  salePrice: [
    { required: true, message: '请输入售价', trigger: 'blur' },
    { type: 'number', min: 1, message: '售价必须大于0', trigger: 'blur' }
  ],
  payType: [
    { required: true, message: '请选择支付方式', trigger: 'change' }
  ],
  depositType: [
    { required: true, message: '请选择预订类型', trigger: 'change' }
  ],
  depositAmount: [
    { required: true, message: '请输入定金金额', trigger: 'blur' },
    { type: 'number', min: 1, message: '定金金额必须大于0', trigger: 'blur' }
  ],
  customerPhone: [
    { required: false, message: '请输入会员手机号', trigger: 'blur' },
    { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号码', trigger: 'blur' }
  ],
  memberId: [
    { required: true, message: '请选择会员', trigger: 'change' }
  ]
};

// 批量预订表单
const batchReserveForm = ref({
  payType: 1, // 支付方式
  depositType: 0, // 预订支付类型（0定金 1全款）
  depositAmount: 0, // 定金金额
  customerPhone: '', // 保留用于会员搜索
  memberId: null, // 会员ID
  totalSalePrice: 1.00, // 总实际售价
  actualSaleTime: '', // 实际售出时间
  remark: ''
});

// 批量预订表单校验规则
const batchReserveRules = {
  payType: [
    { required: true, message: '请选择支付方式', trigger: 'change' }
  ],
  depositType: [
    { required: true, message: '请选择预订类型', trigger: 'change' }
  ],
  depositAmount: [
    { required: true, message: '请输入定金金额', trigger: 'blur' },
    { type: 'number', min: 1, message: '定金金额必须大于0', trigger: 'blur' }
  ],
  customerPhone: [
    { required: false, message: '请输入会员手机号', trigger: 'blur' },
    { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号码', trigger: 'blur' }
  ],
  memberId: [
    { required: true, message: '请选择会员', trigger: 'change' }
  ],
  totalSalePrice: [
    { required: true, message: '请输入总实际售价', trigger: 'blur' },
    { type: 'number', min: 1, message: '总实际售价必须大于0', trigger: 'blur' }
  ]
};

// 新增会员表单
const newMemberForm = ref({
  memberName: '',
  phoneNumber: '',
  gender: 0,
  birthday: '',
  remark: ''
});

// 新增会员表单校验规则
const newMemberRules = {
  memberName: [
    { required: true, message: '请输入会员姓名', trigger: 'blur' },
    { min: 2, max: 20, message: '长度在 2 到 20 个字符', trigger: 'blur' }
  ],
  phoneNumber: [
    { required: true, message: '请输入手机号码', trigger: 'blur' },
    { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号码', trigger: 'blur' }
  ],
  gender: [
    { required: true, message: '请选择性别', trigger: 'change' }
  ]
};

/** 商品预订操作（从合并列表） */
function handleReserveProduct(row) {
  if (!row || !row.productId) {
    ElMessage.warning('商品信息不完整');
    return;
  }

  // 如果有非期货库存，不允许预订
  if (row.hasNonFuturesStock) {
    ElMessage.warning('该商品在当前门店有可用库存，不能预订');
    return;
  }

  console.log('预订商品初始信息:', row); // 添加日志以便调试

  // 查询商品详细信息，确保获取到零售价
  listStoreStock({
    productId: row.productId,
    pageSize: 1
  }).then(response => {
    if (response.rows && response.rows.length > 0) {
      // 合并商品信息，确保有零售价
      reserveItem.value = {
        ...row,
        productId: row.productId, // 确保包含商品ID
        retailPrice: response.rows[0].retailPrice || row.retailPrice || 0
      };

      console.log('预订商品完整信息:', reserveItem.value);

      prepareReserveForm(reserveItem.value);
      reserveDialogOpen.value = true;
    } else {
      ElMessage.warning('获取商品详情失败');
    }
  }).catch(error => {
    console.error('获取商品详情异常:', error);
    ElMessage.error('获取商品详情失败');
  });
}

/** 准备预订表单 */
function prepareReserveForm(item) {
  // 重置会员信息
  foundMember.value = null;

  if (!item || !item.productId) {
    console.error('预订商品信息不完整:', item);
    ElMessage.warning('商品信息不完整，无法预订');
    return;
  }

  console.log('准备预订表单，商品ID:', item.productId); // 添加日志以便调试

  // 获取当前时间作为默认实际售出时间
  const now = new Date();
  const currentTime = now.getFullYear() + '-' +
    String(now.getMonth() + 1).padStart(2, '0') + '-' +
    String(now.getDate()).padStart(2, '0') + ' ' +
    String(now.getHours()).padStart(2, '0') + ':' +
    String(now.getMinutes()).padStart(2, '0') + ':' +
    String(now.getSeconds()).padStart(2, '0');

  reserveForm.value = {
    productId: item.productId,
    salePrice: item.retailPrice || 0,
    payType: 1, // 默认值，根据实际情况调整
    depositType: 0, // 默认定金
    depositAmount: item.retailPrice ? Math.ceil(item.retailPrice * 0.3) : 0, // 默认定金为售价的30%
    customerPhone: '',
    memberId: null,
    actualSaleTime: currentTime,
    remark: '',
    storeId: currentUserStoreId.value // 确保包含门店ID
  };

  // 计算初始折扣
  calculateReserveDiscount();
}

/** 提交预订 */
function submitReserve() {
  proxy.$refs["reserveFormRef"].validate(valid => {
    if (valid) {
      // 验证定金金额
      if (reserveForm.value.depositType === 0) {
        if (reserveForm.value.depositAmount <= 0) {
          ElMessage.warning('定金金额必须大于0');
          return;
        }
        if (reserveForm.value.depositAmount > reserveForm.value.salePrice) {
          ElMessage.warning('定金金额不能超过商品售价');
          return;
        }
      }

      submitLoading.value = true;

      // 创建预订单数据
      const reserveData = {
        productId: reserveForm.value.productId, // 确保包含商品ID
        salePrice: reserveForm.value.salePrice,
        payType: reserveForm.value.payType,
        depositType: reserveForm.value.depositType,
        depositAmount: reserveForm.value.depositType === 0 ? reserveForm.value.depositAmount : reserveForm.value.salePrice,
        memberId: reserveForm.value.memberId,
        actualSaleTime: reserveForm.value.actualSaleTime,
        remark: reserveForm.value.remark,
        storeId: currentUserStoreId.value, // 使用当前用户的门店ID
        quantity: 1, // 默认数量为1
        batchNumber: 'R' + new Date().getTime() // 生成一个临时批次号，交货时会自动更新为真实批次号
      };

      console.log('提交预订单数据:', reserveData); // 添加日志以便调试

      reserveProduct(reserveData).then(response => {
        if (response.code === 200) {
          ElMessage.success('提交预订成功');
          reserveDialogOpen.value = false;
          // 刷新列表
          getList();
        } else {
          ElMessage.error(response.msg || '提交预订失败');
        }
        submitLoading.value = false;
      }).catch(error => {
        console.error('预订请求异常:', error);
        ElMessage.error('提交预订失败: ' + (error.message || '未知错误'));
        submitLoading.value = false;
      });
    }
  });
}

/** 批量预订按钮操作 */
function handleBatchReserve() {
  // 重置会员信息
  foundMember.value = null;
  searchMode.value = 'batchReserve'; // 设置搜索模式

  // 获取当前时间作为默认实际售出时间
  const now = new Date();
  const currentTime = now.getFullYear() + '-' +
    String(now.getMonth() + 1).padStart(2, '0') + '-' +
    String(now.getDate()).padStart(2, '0') + ' ' +
    String(now.getHours()).padStart(2, '0') + ':' +
    String(now.getMinutes()).padStart(2, '0') + ':' +
    String(now.getSeconds()).padStart(2, '0');

  // 重置批量预订表单
  batchReserveForm.value = {
    payType: 1,
    depositType: 0, // 默认定金
    depositAmount: 0,
    customerPhone: '',
    memberId: null,
    totalSalePrice: 1.00,
    actualSaleTime: currentTime,
    remark: ''
  };

  // 重置已选商品列表
  selectedProducts.value = [];

  // 重置商品搜索表单
  productSearchForm.value = {
    productNumber: ''
  };

  // 清空搜索结果
  searchedProduct.value = null;

  // 隐藏库存明细
  showStockDetails.value = false;
  stockDetailsList.value = [];

  // 打开批量预订对话框
  batchReserveDialogOpen.value = true;
}

/** 添加商品到预订列表 */
function handleAddProductToReserve(product) {
  // 检查是否已经添加过
  const exists = selectedProducts.value.some(item =>
    item.productId === product.productId
  );

  if (exists) {
    ElMessage.warning('该商品已添加到列表中');
    return;
  }

  // 添加到已选列表，并设置销售数量
  const productToAdd = {
    productId: product.productId,
    productName: product.productName,
    productNumber: product.productNumber,
    productImage: product.productImage,
    brand: product.brand,
    category: product.category,
    retailPrice: product.retailPrice || 0,
    saleQuantity: 1,
    salePrice: product.retailPrice || 0
  };

  selectedProducts.value.push(productToAdd);

  // 更新总数量和总价
  updateTotalAmount();

  // 更新定金金额（默认为总价的30%）
  if (batchReserveForm.value.depositType === 0) {
    const totalPrice = calculateTotalRetailPrice();
    batchReserveForm.value.depositAmount = Math.ceil(totalPrice * 0.3);
  }

  // 更新批量预订表单的总售价
  batchReserveForm.value.totalSalePrice = calculateTotalRetailPrice();

  // 提示用户
  ElMessage.success('已添加到已选商品列表');
}

/** 提交批量预订 */
function submitBatchReserve() {
  proxy.$refs["batchReserveFormRef"].validate(valid => {
    if (valid) {
      if (selectedProducts.value.length === 0) {
        ElMessage.warning('请至少选择一个商品');
        return;
      }

      // 验证定金金额
      if (batchReserveForm.value.depositType === 0) {
        if (batchReserveForm.value.depositAmount <= 0) {
          ElMessage.warning('定金金额必须大于0');
          return;
        }
        if (batchReserveForm.value.depositAmount > batchReserveForm.value.totalSalePrice) {
          ElMessage.warning('定金金额不能超过商品总售价');
          return;
        }
      }

      submitLoading.value = true;

      // 计算按比例分配的实际售价
      const allocatedPrices = allocatePricesByRatio(selectedProducts.value, batchReserveForm.value.totalSalePrice);

      // 创建预订明细列表，使用分配后的价格
      const reserveItems = selectedProducts.value.map((product, index) => {
        if (!product.productId) {
          console.error('商品缺少productId:', product);
        }
        return {
          productId: product.productId,
          productName: product.productName,
          productNumber: product.productNumber,
          salePrice: allocatedPrices[index], // 使用分配后的实际售价
          quantity: product.saleQuantity || 1,
          batchNumber: 'R' + new Date().getTime() + '-' + index // 生成一个临时批次号，交货时会自动更新为真实批次号
        };
      });

      console.log('预订分配后的价格:', allocatedPrices); // 添加日志以便调试

      // 创建批量预订请求
      const batchReserveData = {
        items: reserveItems,
        payType: batchReserveForm.value.payType,
        depositType: batchReserveForm.value.depositType,
        depositAmount: batchReserveForm.value.depositType === 0 ? batchReserveForm.value.depositAmount : batchReserveForm.value.totalSalePrice,
        memberId: batchReserveForm.value.memberId,
        storeId: currentUserStoreId.value, // 使用当前用户的门店ID
        totalSalePrice: batchReserveForm.value.totalSalePrice,
        actualSaleTime: batchReserveForm.value.actualSaleTime,
        remark: batchReserveForm.value.remark
      };

      console.log('提交批量预订数据:', batchReserveData); // 添加日志以便调试

      // 调用批量预订API
      batchReserveProduct(batchReserveData).then(() => {
        ElMessage.success('提交预订成功');
        batchReserveDialogOpen.value = false;
        // 刷新列表
        getList();
        submitLoading.value = false;
      }).catch(error => {
        ElMessage.error('提交预订失败: ' + (error.message || '未知错误'));
        submitLoading.value = false;
      });
    }
  });
}

/** 批量售出按钮操作 */
function handleBatchSale() {
  // 重置会员信息
  foundMember.value = null;
  searchMode.value = 'batch'; // 重置搜索模式

  // 获取当前时间作为默认实际售出时间
  const now = new Date();
  const currentTime = now.getFullYear() + '-' +
    String(now.getMonth() + 1).padStart(2, '0') + '-' +
    String(now.getDate()).padStart(2, '0') + ' ' +
    String(now.getHours()).padStart(2, '0') + ':' +
    String(now.getMinutes()).padStart(2, '0') + ':' +
    String(now.getSeconds()).padStart(2, '0');

  // 重置批量售出表单
  batchSaleForm.value = {
    payType: 1,
    customerPhone: '',
    memberId: null,
    totalSalePrice: 1.00, // 重置总实际售价，确保是数字类型且大于0
    actualSaleTime: currentTime,
    remark: ''
  };

  // 重置已选商品列表
  selectedProducts.value = [];

  // 重置商品搜索表单
  productSearchForm.value = {
    productNumber: ''
  };

  // 清空搜索结果
  searchedProduct.value = null;

  // 隐藏库存明细
  showStockDetails.value = false;
  stockDetailsList.value = [];

  // 打开批量售出对话框
  batchSaleDialogOpen.value = true;
}

// 不再需要获取柜台和渠道选项的函数

/** 查找商品按钮操作 */
function handleSearchProduct() {
  proxy.$refs["productSearchFormRef"].validate(valid => {
    if (!valid) return;

    searchLoading.value = true;
    searchedProduct.value = null;
    stockDetailsList.value = []; // 清空库存明细列表
    showStockDetails.value = false; // 隐藏库存明细

    // 根据商品货号查询库存
    const productNumber = productSearchForm.value.productNumber;

    // 判断当前操作是否是批量预订
    const isReserveMode = batchReserveDialogOpen.value;

    // 先查询商品基本信息
    listStoreStock({
      productNumber: productNumber,
      pageSize: 1
    }).then(response => {
      if (response.rows && response.rows.length > 0) {
        const product = response.rows[0];

        // 如果是预订模式，需要检查当前门店是否有该商品的库存
        if (isReserveMode) {
          // 查询当前门店该商品的库存
          getProductStockDetails(product.productId, { storeId: currentUserStoreId.value }).then(stockResponse => {
            const storeStocks = stockResponse.data || [];
            const totalStoreQuantity = storeStocks.reduce((sum, item) => sum + (item.quantity || 0), 0);

            if (totalStoreQuantity > 0) {
              // 当前门店有库存，不允许预订
              searchLoading.value = false;
              ElMessage.warning('当前门店已有该商品库存，不能预订');
            } else {
              // 当前门店没有库存，允许预订
              searchedProduct.value = {
                ...product,
                retailPrice: product.retailPrice || 0 // 确保零售价存在
              };
              searchLoading.value = false;
            }
          }).catch(() => {
            searchLoading.value = false;
            ElMessage.error('查询商品库存失败');
          });
        } else {
          // 非预订模式，直接显示商品
          searchedProduct.value = product;
          searchLoading.value = false;
        }
      } else {
        searchLoading.value = false;
        ElMessage.warning('未找到该商品货号');
      }
    }).catch(() => {
      searchLoading.value = false;
      ElMessage.error('查询商品失败');
    });
  });
}

/** 查看商品库存明细 */
function handleViewStockDetails(product) {
  if (!product || !product.productId) {
    ElMessage.warning('商品信息不完整');
    return;
  }

  detailLoading.value = true;
  stockDetailsList.value = [];

  const params = {
    storeId: currentUserStoreId.value
  };

  getProductStockDetails(product.productId, params).then(response => {
    if (response.data && response.data.length > 0) {
      // 为每个库存项添加销售数量字段
      stockDetailsList.value = response.data.map(item => ({
        ...item,
        saleQuantity: 1
      }));
      showStockDetails.value = true; // 显示库存明细
    } else {
      ElMessage.warning('该商品没有库存明细');
      showStockDetails.value = false;
    }
    detailLoading.value = false;
  }).catch(() => {
    detailLoading.value = false;
    ElMessage.error('获取库存明细失败');
    showStockDetails.value = false;
  });
}

/** 重置商品搜索表单 */
function resetProductSearch() {
  proxy.$refs["productSearchFormRef"].resetFields();
  searchedProduct.value = null;
  stockDetailsList.value = [];
  showStockDetails.value = false; // 隐藏库存明细
}

/** 添加库存明细到已选列表 */
function handleAddStockToSelected(stock) {
  // 检查是否已经添加过
  const exists = selectedProducts.value.some(item =>
    item.id === stock.id && item.productId === stock.productId
  );

  if (exists) {
    ElMessage.warning('该库存已添加到列表中');
    return;
  }

  // 检查库存
  if (stock.quantity <= 0 || stock.isFutures) {
    ElMessage.warning('该库存不可售出');
    return;
  }

  // 添加到已选列表，并设置销售数量
  const stockToAdd = {
    ...stock,
    saleQuantity: stock.saleQuantity || 1,
    productImage: searchedProduct.value ? searchedProduct.value.productImage : ''
  };

  selectedProducts.value.push(stockToAdd);

  // 更新总数量
  updateTotalAmount();

  // 提示用户
  ElMessage.success('已添加到已选商品列表');
}

/** 从已选列表中移除商品 */
function handleRemoveSelected(index) {
  selectedProducts.value.splice(index, 1);
  updateTotalAmount();
}

/** 清空已选商品 */
function clearSelectedProducts() {
  selectedProducts.value = [];
  totalQuantity.value = 0;

  // 根据当前打开的对话框重置相应的表单
  if (batchSaleDialogOpen.value) {
    // 重置批量售出表单的总实际售价为默认值
    batchSaleForm.value.totalSalePrice = 1.00;
  } else if (batchReserveDialogOpen.value) {
    // 重置批量预订表单的总实际售价为默认值
    batchReserveForm.value.totalSalePrice = 1.00;
    // 如果是定金模式，重置定金金额
    if (batchReserveForm.value.depositType === 0) {
      batchReserveForm.value.depositAmount = 0;
    }
  }

  ElMessage.success('已清空所有已选商品');
}

/** 更新总数量和总价 */
function updateTotalAmount() {
  // 更新总数量
  totalQuantity.value = selectedProducts.value.reduce((sum, item) => sum + (item.saleQuantity || 1), 0);

  // 更新总实际售价，确保是数字类型
  const totalPrice = calculateTotalRetailPrice();

  // 只有当有商品时才更新总价，避免设置为0导致验证错误
  if (selectedProducts.value.length > 0 && totalPrice > 0) {
    // 根据当前打开的对话框更新相应的表单
    if (batchSaleDialogOpen.value) {
      batchSaleForm.value.totalSalePrice = totalPrice;
      // 计算批量折扣
      calculateBatchDiscount();
    } else if (batchReserveDialogOpen.value) {
      batchReserveForm.value.totalSalePrice = totalPrice;
      // 计算批量预订折扣
      calculateBatchReserveDiscount();

      // 如果是定金模式，更新定金金额（默认为总价的30%）
      if (batchReserveForm.value.depositType === 0) {
        batchReserveForm.value.depositAmount = Math.ceil(totalPrice * 0.3);
      }
    }
  }
}

/** 计算总零售价 */
function calculateTotalRetailPrice() {
  // 计算总价，但不使用toFixed，保持数字类型
  const total = selectedProducts.value.reduce((sum, item) => {
    const price = item.retailPrice || 0;
    const quantity = item.saleQuantity || 1;
    return sum + (price * quantity);
  }, 0);

  // 返回数字类型，而不是字符串
  return parseFloat(total.toFixed(2));
}

/** 按比例分配总价格到各个商品 */
function allocatePricesByRatio(products, totalSalePrice) {
  // 计算每个商品的零售价总额
  const retailPrices = products.map(product => {
    const price = product.retailPrice || 0;
    const quantity = product.saleQuantity || 1;
    return price * quantity;
  });

  // 计算零售价总和
  const totalRetailPrice = retailPrices.reduce((sum, price) => sum + price, 0);

  if (totalRetailPrice === 0) {
    // 如果零售价总和为0，平均分配
    const averagePrice = totalSalePrice / products.length;
    return products.map(() => parseFloat(averagePrice.toFixed(2)));
  }

  // 按零售价比例分配总价格
  const allocatedPrices = retailPrices.map(retailPrice => {
    const ratio = retailPrice / totalRetailPrice;
    return parseFloat((totalSalePrice * ratio).toFixed(2));
  });

  // 处理精度问题，确保分配后的总和等于目标总价
  const allocatedTotal = allocatedPrices.reduce((sum, price) => sum + price, 0);
  const difference = parseFloat((totalSalePrice - allocatedTotal).toFixed(2));

  if (difference !== 0) {
    // 将差额加到第一个商品上
    allocatedPrices[0] = parseFloat((allocatedPrices[0] + difference).toFixed(2));
  }

  return allocatedPrices;
}

/** 获取指定索引商品的分配价格 */
function getAllocatedPrice(index) {
  if (!batchSaleForm.value.totalSalePrice || selectedProducts.value.length === 0) {
    return 0;
  }

  const allocatedPrices = allocatePricesByRatio(selectedProducts.value, batchSaleForm.value.totalSalePrice);
  return allocatedPrices[index] || 0;
}

/** 获取指定索引商品的预订分配价格 */
function getReserveAllocatedPrice(index) {
  if (!batchReserveForm.value.totalSalePrice || selectedProducts.value.length === 0) {
    return 0;
  }

  const allocatedPrices = allocatePricesByRatio(selectedProducts.value, batchReserveForm.value.totalSalePrice);
  return allocatedPrices[index] || 0;
}

/** 提交批量售出 */
function submitBatchSale() {
  proxy.$refs["batchSaleFormRef"].validate(valid => {
    if (valid) {
      if (selectedProducts.value.length === 0) {
        ElMessage.warning('请至少选择一个商品');
        return;
      }

      submitLoading.value = true;

      // 计算按比例分配的实际售价
      const allocatedPrices = allocatePricesByRatio(selectedProducts.value, batchSaleForm.value.totalSalePrice);

      // 创建出库单明细列表，使用分配后的价格
      const stockDetails = selectedProducts.value.map((product, index) => ({
        stockId: product.id,
        productId: product.productId,
        salePrice: allocatedPrices[index], // 使用分配后的实际售价
        quantity: product.saleQuantity || 1 // 确保使用用户选择的数量
      }));

      console.log('选中的商品:', selectedProducts.value); // 添加日志以便调试
      console.log('分配后的价格:', allocatedPrices); // 添加日志以便调试

      // 创建批量售出请求
      const batchOrderData = {
        stocks: stockDetails,
        payType: batchSaleForm.value.payType,
        memberId: batchSaleForm.value.memberId,
        totalSalePrice: batchSaleForm.value.totalSalePrice, // 添加总实际售价
        actualSaleTime: batchSaleForm.value.actualSaleTime,
        remark: batchSaleForm.value.remark
      };

      // 调用批量售出API
      createBatchOutOrder(batchOrderData).then(() => {
        ElMessage.success('提交审核成功');
        batchSaleDialogOpen.value = false;
        // 刷新列表
        getList();
        submitLoading.value = false;
      }).catch(error => {
        ElMessage.error('提交审核失败: ' + (error.message || '未知错误'));
        submitLoading.value = false;
      });
    }
  });
}

// 不再需要自定义的批量售出函数，直接使用API中的createBatchOutOrder

/** 搜索会员 */
function searchMember(phoneNumber, mode = 'single') {
  if (!phoneNumber) {
    ElMessage.warning('请输入会员手机号进行搜索');
    return;
  }

  searchMode.value = mode;
  memberSearchLoading.value = true;
  foundMember.value = null;

  // 清空会员ID
  if (mode === 'single') {
    saleForm.value.memberId = null;
  } else if (mode === 'batch') {
    batchSaleForm.value.memberId = null;
  } else if (mode === 'reserve') {
    reserveForm.value.memberId = null;
  } else if (mode === 'batchReserve') {
    batchReserveForm.value.memberId = null;
  }

  searchMemberByPhone(phoneNumber).then(response => {
    memberSearchLoading.value = false;
    if (response.code === 200 && response.data) {
      foundMember.value = response.data;

      // 根据搜索模式填充不同的表单
      if (mode === 'single') {
        // 只设置会员ID，其他信息通过会员卡片显示
        saleForm.value.memberId = response.data.memberId;
      } else if (mode === 'batch') {
        // 只设置会员ID，其他信息通过会员卡片显示
        batchSaleForm.value.memberId = response.data.memberId;
      } else if (mode === 'reserve') {
        // 只设置会员ID，其他信息通过会员卡片显示
        reserveForm.value.memberId = response.data.memberId;
      } else if (mode === 'batchReserve') {
        // 只设置会员ID，其他信息通过会员卡片显示
        batchReserveForm.value.memberId = response.data.memberId;
      }

      ElMessage.success('已找到会员信息');
    } else {
      ElMessage.warning('未找到会员信息，请输入客户资料或创建新会员');
    }
  }).catch(() => {
    memberSearchLoading.value = false;
    ElMessage.error('搜索会员失败');
  });
}

/** 打开新增会员对话框 */
function openNewMemberDialog(mode = 'single') {
  currentSaleMode.value = mode;

  // 重置新增会员表单
  newMemberForm.value = {
    memberName: '',
    phoneNumber: '',
    gender: 0,
    birthday: '',
    remark: ''
  };

  // 如果已经输入了手机号，自动填充到新增会员表单中
  if (mode === 'single' && saleForm.value.customerPhone) {
    newMemberForm.value.phoneNumber = saleForm.value.customerPhone;
  } else if (mode === 'batch' && batchSaleForm.value.customerPhone) {
    newMemberForm.value.phoneNumber = batchSaleForm.value.customerPhone;
  } else if (mode === 'reserve' && reserveForm.value.customerPhone) {
    newMemberForm.value.phoneNumber = reserveForm.value.customerPhone;
  } else if (mode === 'batchReserve' && batchReserveForm.value.customerPhone) {
    newMemberForm.value.phoneNumber = batchReserveForm.value.customerPhone;
  }

  // 打开新增会员对话框
  newMemberDialogOpen.value = true;
}

/** 提交新增会员 */
function submitNewMember() {
  proxy.$refs["newMemberFormRef"].validate(valid => {
    if (valid) {
      newMemberSubmitLoading.value = true;

      // 创建新会员
      addMember(newMemberForm.value).then(response => {
        if (response.code === 200) {
          ElMessage.success('新增会员成功');

          // 关闭对话框
          newMemberDialogOpen.value = false;

          // 搜索新创建的会员
          searchMember(newMemberForm.value.phoneNumber, currentSaleMode.value);
        } else {
          ElMessage.error(response.msg || '新增会员失败');
        }
        newMemberSubmitLoading.value = false;
      }).catch(() => {
        newMemberSubmitLoading.value = false;
        ElMessage.error('新增会员失败');
      });
    }
  });
}

/** 获取会员等级名称 */
function getMemberLevelName(levelId) {
  // 从会员等级配置中获取
  const level = memberLevelConfig.value.find(item => item.levelId === parseInt(levelId));
  return level ? level.levelName : '普通会员';
}

/** 获取会员等级标签类型 */
function getMemberLevelTag(levelId) {
  // 从会员等级配置中获取
  const level = memberLevelConfig.value.find(item => item.levelId === parseInt(levelId));
  return level ? level.tagType : 'info';
}

getList();
</script>

<style scoped>
.member-search-container {
  display: flex;
  align-items: center;
}
.mb-3 {
  margin-bottom: 1rem;
}

.batch-sale-container {
  display: flex;
  gap: 20px;
}

.product-search {
  flex: 1;
  border: 1px solid #ebeef5;
  border-radius: 4px;
  padding: 10px;
}

.selected-products {
  flex: 1;
  border: 1px solid #ebeef5;
  border-radius: 4px;
  padding: 10px;
}

.searched-product {
  margin-top: 20px;
  margin-bottom: 20px;
}

.search-result-card {
  display: flex;
  gap: 20px;
  align-items: flex-start;
}

.product-image {
  flex: 0 0 auto;
}

.product-info {
  flex: 1;
}

.product-action {
  flex: 0 0 auto;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  gap: 10px;
}

.stock-details-list {
  margin-top: 20px;
  border: 1px solid #ebeef5;
  border-radius: 4px;
  padding: 15px;
  background-color: #f8f9fa;
  width: 100%;
}

.stock-details-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
}

.info-row {
  margin-bottom: 8px;
  line-height: 1.5;
}

.info-row .label {
  font-weight: bold;
  margin-right: 8px;
  color: #606266;
}

.member-card {
  border: 1px solid #ebeef5;
  border-radius: 4px;
  padding: 12px;
  background-color: #f8f9fa;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
  display: flex;
  flex-direction: column;
  justify-content: center; /* 垂直居中整个卡片内容 */
}

.member-card.compact {
  padding: 8px 10px;
  margin: 0; /* 移除外边距 */
}

.member-header {
  display: flex;
  justify-content: space-between;
  align-items: center; /* 确保header内容垂直居中 */
  margin-bottom: 8px;
  padding-bottom: 8px;
  border-bottom: 1px solid #ebeef5;
  min-height: 32px; /* 设置最小高度确保有足够空间 */
}

.member-header-left {
  display: flex;
  align-items: center; /* 确保左侧内容垂直居中 */
  gap: 10px;
  height: 100%; /* 占满整个header高度 */
}

.member-name {
  font-weight: bold;
  font-size: 14px;
  color: #303133;
}

.member-id {
  font-size: 12px;
  color: #909399;
}

.member-body {
  display: flex;
  flex-direction: column;
}

.member-row {
  display: flex;
  justify-content: space-between;
  flex-wrap: wrap;
  gap: 10px;
  align-items: center; /* 确保行内元素垂直居中 */
  min-height: 30px; /* 设置最小高度确保有足够空间 */
}

.member-item {
  display: flex;
  align-items: center; /* 确保项目内容垂直居中 */
  flex: 1;
  min-width: 150px;
  height: 100%; /* 占满整个行高 */
}

.member-item .label {
  font-weight: bold;
  width: 70px;
  color: #606266;
  font-size: 13px;
  line-height: 1.5; /* 调整行高 */
  display: flex;
  align-items: center; /* 确保标签文本垂直居中 */
}

.member-item .value {
  color: #303133;
  font-size: 13px;
  line-height: 1.5; /* 调整行高 */
  display: flex;
  align-items: center; /* 确保值文本垂直居中 */
}

.selected-products-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
}

.batch-sale-summary {
  margin-top: 20px;
  padding: 15px;
  background-color: #f8f9fa;
  border-radius: 4px;
  border-left: 4px solid #409eff;
}

.summary-item {
  display: flex;
  align-items: center;
  margin-bottom: 8px;
}

.summary-label {
  font-weight: bold;
  margin-right: 10px;
  color: #606266;
}

.summary-value {
  font-size: 16px;
  color: #409eff;
  font-weight: bold;
}

h4 {
  margin-top: 0;
  margin-bottom: 10px;
  font-size: 16px;
  font-weight: 500;
}

.form-help-text {
  font-size: 12px;
  color: #909399;
  line-height: 1.4;
  margin-top: 4px;
}

.dialog-footer {
  display: flex;
  align-items: center;
  width: 100%;
}
</style>