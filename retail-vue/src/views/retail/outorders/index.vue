<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryRef" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="出库单号" prop="orderNumber">
        <el-input
          v-model="queryParams.orderNumber"
          placeholder="请输入出库单号"
          clearable
          style="width: 200px"
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item label="门店" prop="storeName">
        <el-input
          v-model="queryParams.storeName"
          placeholder="请输入门店名称"
          clearable
          style="width: 200px"
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item label="操作类型" prop="businessType">
        <el-select v-model="queryParams.businessType" placeholder="请选择操作类型" clearable style="width: 120px">
          <el-option label="库存删除" :value="5" />
          <el-option label="库存迁移" :value="6" />
        </el-select>
      </el-form-item>
      <el-form-item label="日期" prop="createTime">
        <el-date-picker
          v-model="queryParams.createTime"
          type="daterange"
          range-separator="至"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          style="width: 240px"
        />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
        <el-button icon="Refresh" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="Delete"
          @click="handleBatchDelete"
          v-hasPermi="['retail:stock:remove']"
        >库存删除</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="Switch"
          @click="handleBatchMigrate"
          v-hasPermi="['retail:stock:edit']"
        >库存迁移</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="info"
          plain
          icon="Download"
          @click="handleExport"
          v-hasPermi="['retail:outorder:export']"
        >导出</el-button>
      </el-col>
      <right-toolbar :model-value="showSearch" @update:modelValue="showSearch = $event" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="outOrderList">
      <el-table-column label="出库单号" align="center" prop="orderNumber" />
      <el-table-column label="门店名称" align="center" prop="storeName" />
      <el-table-column label="操作类型" align="center" prop="businessType" width="100">
        <template #default="scope">
          <el-tag type="danger" v-if="scope.row.businessType === 5">库存删除</el-tag>
          <el-tag type="warning" v-else-if="scope.row.businessType === 6">库存迁移</el-tag>
          <el-tag v-else>未知({{ scope.row.businessType }})</el-tag>
        </template>
      </el-table-column>
      <el-table-column label="操作状态" align="center" prop="processStatus" width="100">
        <template #default="scope">
          <el-tag type="success" v-if="scope.row.processStatus === 1">已完成</el-tag>
          <el-tag v-else>未知({{ scope.row.processStatus }})</el-tag>
        </template>
      </el-table-column>
      <el-table-column label="操作人" align="center" prop="createBy" />
      <el-table-column label="创建日期" align="center" prop="createTime" width="180">
        <template #default="scope">
          <span>{{ parseTime(scope.row.createTime) }}</span>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template #default="scope">
          <el-button link type="primary" icon="View" @click="handleDetail(scope.row)">查看详情</el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total>0"
      :total="total"
      :page="queryParams.pageNum"
      :limit="queryParams.pageSize"
      @update:page="queryParams.pageNum = $event"
      @update:limit="queryParams.pageSize = $event"
      @pagination="getList"
    />

    <!-- 出库详情对话框 -->
    <el-dialog title="出库详情" v-model="detailOpen" width="900px" append-to-body destroy-on-close>
      <template v-if="detailOpen">
        <el-descriptions :column="2" border :column-width="['50%', '50%']">
          <el-descriptions-item label="出库单号">{{ detail.orderNumber }}</el-descriptions-item>
          <el-descriptions-item label="门店名称">{{ detail.storeName }}</el-descriptions-item>
          <el-descriptions-item label="操作类型">
            <el-tag type="danger" v-if="detail.businessType === 5">库存删除</el-tag>
            <el-tag type="warning" v-else-if="detail.businessType === 6">库存迁移</el-tag>
            <el-tag v-else>未知({{ detail.businessType }})</el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="操作状态">
            <el-tag type="success" v-if="detail.processStatus === 1">已完成</el-tag>
            <el-tag v-else>未知({{ detail.processStatus }})</el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="操作人">{{ detail.createBy }}</el-descriptions-item>
          <el-descriptions-item label="创建日期">{{ parseTime(detail.createTime) }}</el-descriptions-item>
          <el-descriptions-item label="备注" v-if="detail.remark" :span="2">{{ detail.remark }}</el-descriptions-item>
        </el-descriptions>

        <el-divider content-position="center">出库明细</el-divider>
        <el-table :data="detailListRef">
          <el-table-column label="商品图" width="80">
            <template #default="scope">
              <image-preview :src="scope.row.productImage" :width="50" :height="50"/>
            </template>
          </el-table-column>
          <el-table-column label="商品名称" prop="productName" show-overflow-tooltip />
          <el-table-column label="货号" prop="productNumber" show-overflow-tooltip />
          <el-table-column label="数量" prop="quantity" />
          <el-table-column label="批次号" prop="batchNumber" show-overflow-tooltip />
          <el-table-column label="备注" prop="remark" show-overflow-tooltip width="200" />
        </el-table>

      </template>

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="detailOpen = false">关 闭</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 库存删除对话框 -->
    <el-dialog title="库存删除" v-model="deleteDialogOpen" width="1200px" append-to-body destroy-on-close>

      <el-form :model="stockQueryParams" ref="stockQueryRef" :inline="true" label-width="68px">
        <el-form-item label="商品货号" prop="productNumber">
          <el-input
            v-model="stockQueryParams.productNumber"
            placeholder="请输入商品货号"
            clearable
            style="width: 200px"
            @keyup.enter="handleStockQuery"
          />
        </el-form-item>
        <el-form-item label="门店" prop="storeId">
          <el-select v-model="stockQueryParams.storeId" placeholder="请选择门店" clearable style="width: 150px">
            <el-option
              v-for="store in storeOptions"
              :key="store.id"
              :label="store.storeName"
              :value="store.id"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="渠道" prop="channelId">
          <el-select v-model="stockQueryParams.channelId" placeholder="请选择渠道" clearable style="width: 150px">
            <el-option
              v-for="channel in channelOptions"
              :key="channel.id"
              :label="channel.channelName"
              :value="channel.id"
            />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" icon="Search" @click="handleStockQuery">搜索</el-button>
          <el-button icon="Refresh" @click="resetStockQuery">重置</el-button>
        </el-form-item>
      </el-form>
      <!-- 删除原因输入框 -->
      <el-form :model="deleteReasonForm" label-width="80px" style="margin-bottom: 20px;">
        <el-form-item label="删除原因">
          <el-input
            v-model="deleteReasonForm.remark"
            placeholder="请输入删除原因（可选）"
            type="textarea"
            :rows="2"
            maxlength="200"
            show-word-limit
            style="width: 100%"
          />
        </el-form-item>
      </el-form>

      <el-divider content-position="left">库存列表</el-divider>
      <div class="stock-table-container">
        <el-table v-loading="stockLoading" :data="stockList" @selection-change="handleStockSelectionChange" height="300">
          <el-table-column type="selection" width="55" align="center" />
          <el-table-column label="商品图" width="80">
            <template #default="scope">
              <image-preview :src="scope.row.productImage" :width="50" :height="50"/>
            </template>
          </el-table-column>
          <el-table-column label="商品货号" prop="productNumber" show-overflow-tooltip />
          <el-table-column label="商品名称" prop="productName" show-overflow-tooltip />
          <el-table-column label="门店" prop="storeName" />
          <el-table-column label="柜台" prop="counterName" />
          <el-table-column label="渠道" prop="channelName" />
          <el-table-column label="库存数量" prop="quantity" />
          <el-table-column label="操作" align="center" width="120">
            <template #default="scope">
              <el-button link type="primary" @click="addToDeleteList(scope.row)">加入删除</el-button>
            </template>
          </el-table-column>
        </el-table>

        <pagination
          v-show="stockTotal > 0"
          :total="stockTotal"
          :page="stockQueryParams.pageNum"
          :limit="stockQueryParams.pageSize"
          @update:page="stockQueryParams.pageNum = $event"
          @update:limit="stockQueryParams.pageSize = $event"
          @pagination="handleStockQuery"
          layout="total, sizes, prev, pager, next"
          :page-sizes="[10, 20, 50]"
          small
        />
      </div>

      <el-divider content-position="left">删除清单</el-divider>
      <div class="operation-list-container">
        <el-table :data="deleteList" height="200">
          <el-table-column label="商品货号" prop="productNumber" />
          <el-table-column label="商品名称" prop="productName" />
          <el-table-column label="渠道" prop="channelName" />
          <el-table-column label="删除数量" width="120">
            <template #default="scope">
              <el-input-number
                v-model="scope.row.deleteQuantity"
                :min="1"
                :max="scope.row.quantity"
                size="small"
              />
            </template>
          </el-table-column>
          <el-table-column label="操作" align="center" width="80">
            <template #default="scope">
              <el-button link type="danger" @click="removeFromDeleteList(scope.$index)">移除</el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>

      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitBatchDelete" :disabled="deleteList.length === 0">确认删除</el-button>
          <el-button @click="deleteDialogOpen = false">取 消</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 库存迁移对话框 -->
    <el-dialog title="库存迁移" v-model="migrateDialogOpen" width="1200px" append-to-body destroy-on-close>

      <el-form :model="stockQueryParams" ref="stockQueryRef2" :inline="true" label-width="68px">
        <el-form-item label="商品货号" prop="productNumber">
          <el-input
            v-model="stockQueryParams.productNumber"
            placeholder="请输入商品货号"
            clearable
            style="width: 200px"
            @keyup.enter="handleStockQuery"
          />
        </el-form-item>
        <el-form-item label="门店" prop="storeId">
          <el-select v-model="stockQueryParams.storeId" placeholder="请选择门店" clearable style="width: 150px">
            <el-option
              v-for="store in storeOptions"
              :key="store.id"
              :label="store.storeName"
              :value="store.id"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="渠道" prop="channelId">
          <el-select v-model="stockQueryParams.channelId" placeholder="请选择渠道" clearable style="width: 150px">
            <el-option
              v-for="channel in channelOptions"
              :key="channel.id"
              :label="channel.channelName"
              :value="channel.id"
            />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" icon="Search" @click="handleStockQuery">搜索</el-button>
          <el-button icon="Refresh" @click="resetStockQuery">重置</el-button>
        </el-form-item>
      </el-form>
      <!-- 迁移备注输入框 -->
      <el-form :model="migrateRemarkForm" label-width="80px" style="margin-bottom: 20px;">
        <el-form-item label="迁移备注">
          <el-input
            v-model="migrateRemarkForm.remark"
            placeholder="请输入迁移备注（可选）"
            type="textarea"
            :rows="2"
            maxlength="200"
            show-word-limit
            style="width: 100%"
          />
        </el-form-item>
      </el-form>

      <el-divider content-position="left">库存列表</el-divider>
      <div class="stock-table-container">
        <el-table v-loading="stockLoading" :data="stockList" @selection-change="handleStockSelectionChange" height="300">
          <el-table-column type="selection" width="55" align="center" />
          <el-table-column label="商品图" width="80">
            <template #default="scope">
              <image-preview :src="scope.row.productImage" :width="50" :height="50"/>
            </template>
          </el-table-column>
          <el-table-column label="商品货号" prop="productNumber" show-overflow-tooltip />
          <el-table-column label="商品名称" prop="productName" show-overflow-tooltip />
          <el-table-column label="门店" prop="storeName" />
          <el-table-column label="柜台" prop="counterName" />
          <el-table-column label="渠道" prop="channelName" />
          <el-table-column label="库存数量" prop="quantity" />
          <el-table-column label="操作" align="center" width="120">
            <template #default="scope">
              <el-button link type="primary" @click="addToMigrateList(scope.row)">加入迁移</el-button>
            </template>
          </el-table-column>
        </el-table>

        <pagination
          v-show="stockTotal > 0"
          :total="stockTotal"
          :page="stockQueryParams.pageNum"
          :limit="stockQueryParams.pageSize"
          @update:page="stockQueryParams.pageNum = $event"
          @update:limit="stockQueryParams.pageSize = $event"
          @pagination="handleStockQuery"
          layout="total, sizes, prev, pager, next"
          :page-sizes="[10, 20, 50]"
          small
        />
      </div>

      <el-divider content-position="left">迁移清单</el-divider>
      <div class="operation-list-container">
        <el-table :data="migrateList" height="200">
          <el-table-column label="商品货号" prop="productNumber" />
          <el-table-column label="商品名称" prop="productName" />
          <el-table-column label="原门店" prop="storeName" />
          <el-table-column label="原柜台" prop="counterName" />
          <el-table-column label="原渠道" prop="channelName" />
          <el-table-column label="迁移数量" width="120">
            <template #default="scope">
              <el-input-number
                v-model="scope.row.migrateQuantity"
                :min="1"
                :max="scope.row.quantity"
                size="small"
              />
            </template>
          </el-table-column>
          <el-table-column label="目标门店" width="150">
            <template #default="scope">
              <el-select v-model="scope.row.targetStoreId" placeholder="选择门店" size="small" @change="onTargetStoreChange(scope.row)">
                <el-option
                  v-for="store in storeOptions"
                  :key="store.id"
                  :label="store.storeName"
                  :value="store.id"
                />
              </el-select>
            </template>
          </el-table-column>
          <el-table-column label="目标柜台" width="150">
            <template #default="scope">
              <el-select v-model="scope.row.targetCounterId" placeholder="选择柜台" size="small">
                <el-option
                  v-for="counter in getCountersByStore(scope.row.targetStoreId)"
                  :key="counter.id"
                  :label="counter.counterName"
                  :value="counter.id"
                />
              </el-select>
            </template>
          </el-table-column>
          <el-table-column label="操作" align="center" width="80">
            <template #default="scope">
              <el-button link type="danger" @click="removeFromMigrateList(scope.$index)">移除</el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>

      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitBatchMigrate" :disabled="migrateList.length === 0">确认迁移</el-button>
          <el-button @click="migrateDialogOpen = false">取 消</el-button>
        </div>
      </template>
    </el-dialog>

  </div>
</template>

<script setup name="InventoryManage">
import { ref, reactive, toRefs, onMounted, getCurrentInstance, computed } from 'vue';
import { listOutOrder, getOutOrder } from "@/api/retail/outorder";
import { listStock, batchDeleteStock, batchMigrateStock } from "@/api/retail/inventory";
import { listStore } from "@/api/retail/store";
import { listChannel } from "@/api/retail/channel";
import { listCounterByStore } from "@/api/retail/counter";

const { proxy } = getCurrentInstance();

const outOrderList = ref([]);
const detailOpen = ref(false);
const loading = ref(true);
const showSearch = ref(true);
const total = ref(0);
const detail = ref({});

// 库存删除和迁移相关数据
const deleteDialogOpen = ref(false);
const migrateDialogOpen = ref(false);
const stockLoading = ref(false);
const stockList = ref([]);
const stockTotal = ref(0);
const selectedStocks = ref([]);
const deleteList = ref([]);
const migrateList = ref([]);
const storeOptions = ref([]);
const channelOptions = ref([]);
const deleteReasonForm = ref({ remark: '' });
const migrateRemarkForm = ref({ remark: '' });

// 计算属性，确保详情数据的安全访问
const detailListRef = computed(() => detail.value.detailList || []);

const data = reactive({
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    orderNumber: null,
    storeName: null,
    businessType: null,
    createTime: null
  },
  stockQueryParams: {
    pageNum: 1,
    pageSize: 10,
    productNumber: null,
    storeId: null,
    channelId: null
  }
});

const { queryParams, stockQueryParams } = toRefs(data);

/** 查询出库单列表 */
function getList() {
  loading.value = true;
  let params = {...queryParams.value};

  // 强制只查询库存删除和迁移的出库单，无论用户选择什么
  params['businessTypeList'] = [5, 6]; // 5=库存删除, 6=库存迁移

  // 如果用户选择了具体的业务类型，则只查询该类型
  if (params.businessType === 5) {
    params['businessTypeList'] = [5]; // 只查询库存删除
  } else if (params.businessType === 6) {
    params['businessTypeList'] = [6]; // 只查询库存迁移
  }

  // 删除单个businessType参数，使用businessTypeList
  delete params.businessType;

  // 处理日期范围
  if (params.createTime && params.createTime.length === 2) {
    params['beginCreateTime'] = params.createTime[0];
    params['endCreateTime'] = params.createTime[1];
  }
  delete params.createTime;

  listOutOrder(params).then(response => {
    outOrderList.value = response.rows;
    total.value = response.total;
    loading.value = false;
  });
}

/** 搜索按钮操作 */
function handleQuery() {
  queryParams.value.pageNum = 1;
  getList();
}

/** 重置按钮操作 */
function resetQuery() {
  proxy.resetForm("queryRef");
  queryParams.value.createTime = null;
  queryParams.value.businessType = null;
  handleQuery();
}

/** 查看详情按钮操作 */
function handleDetail(row) {
  // 防止无效点击
  if (!row || !row.id) return;

  // 先重置对话框状态，确保DOM正确卸载
  detailOpen.value = false;
  detail.value = {};

  // 使用setTimeout确保上一个对话框完全关闭
  setTimeout(() => {
    getOutOrder(row.id).then(response => {
      if (response && response.data) {
        detail.value = response.data;
        // 确保明细数据为数组
        if (!detail.value.detailList) detail.value.detailList = [];
        // 设置对话框为打开状态
        detailOpen.value = true;
      }
    }).catch(error => {
      console.error("获取出库详情失败:", error);
    });
  }, 100);
}

/** 导出按钮操作 */
function handleExport() {
  let params = {...queryParams.value};

  // 强制只导出库存删除和迁移的出库单
  params['businessTypeList'] = [5, 6]; // 5=库存删除, 6=库存迁移

  // 如果用户选择了具体的业务类型，则只导出该类型
  if (params.businessType === 5) {
    params['businessTypeList'] = [5]; // 只导出库存删除
  } else if (params.businessType === 6) {
    params['businessTypeList'] = [6]; // 只导出库存迁移
  }

  delete params.businessType;

  // 处理日期范围
  if (params.createTime && params.createTime.length === 2) {
    params['beginCreateTime'] = params.createTime[0];
    params['endCreateTime'] = params.createTime[1];
  }
  delete params.createTime;

  proxy.download('retail/outorder/export', params, `inventory_manage_${new Date().getTime()}.xlsx`)
}

/** 库存删除按钮操作 */
function handleBatchDelete() {
  deleteDialogOpen.value = true;
  deleteList.value = [];
  stockList.value = [];
  stockTotal.value = 0;
  deleteReasonForm.value = { remark: '' };
  resetStockQuery();
  loadStoreAndChannelOptions();
}

/** 库存迁移按钮操作 */
function handleBatchMigrate() {
  migrateDialogOpen.value = true;
  migrateList.value = [];
  stockList.value = [];
  stockTotal.value = 0;
  migrateRemarkForm.value = { remark: '' };
  resetStockQuery();
  loadStoreAndChannelOptions();
}

/** 加载门店和渠道选项 */
function loadStoreAndChannelOptions() {
  // 加载门店列表
  listStore({}).then(response => {
    storeOptions.value = response.rows || [];
  });

  // 加载渠道列表
  listChannel({}).then(response => {
    channelOptions.value = response.rows || [];
  });
}

/** 查询库存列表 */
function handleStockQuery() {
  // 如果没有任何查询条件，不执行查询
  if (!stockQueryParams.value.productNumber && !stockQueryParams.value.storeId && !stockQueryParams.value.channelId) {
    proxy.$modal.msgWarning("请输入查询条件");
    return;
  }

  stockLoading.value = true;
  listStock(stockQueryParams.value).then(response => {
    stockList.value = response.rows || [];
    stockTotal.value = response.total || 0;
    stockLoading.value = false;
  }).catch(() => {
    stockLoading.value = false;
  });
}

/** 重置库存查询 */
function resetStockQuery() {
  stockQueryParams.value = {
    pageNum: 1,
    pageSize: 10,
    productNumber: null,
    storeId: null,
    channelId: null
  };
  stockList.value = [];
  stockTotal.value = 0;
}

/** 库存选择变化 */
function handleStockSelectionChange(selection) {
  selectedStocks.value = selection;
}

/** 添加到删除清单 */
function addToDeleteList(row) {
  // 检查是否已经在删除清单中
  const exists = deleteList.value.find(item =>
    item.id === row.id &&
    item.storeId === row.storeId &&
    item.counterId === row.counterId &&
    item.channelId === row.channelId
  );

  if (exists) {
    proxy.$modal.msgWarning("该库存已在删除清单中");
    return;
  }

  // 添加到删除清单
  deleteList.value.push({
    ...row,
    deleteQuantity: 1
  });
}

/** 从删除清单移除 */
function removeFromDeleteList(index) {
  deleteList.value.splice(index, 1);
}

/** 添加到迁移清单 */
function addToMigrateList(row) {
  // 检查是否已经在迁移清单中
  const exists = migrateList.value.find(item =>
    item.id === row.id &&
    item.storeId === row.storeId &&
    item.counterId === row.counterId &&
    item.channelId === row.channelId
  );

  if (exists) {
    proxy.$modal.msgWarning("该库存已在迁移清单中");
    return;
  }

  // 添加到迁移清单
  migrateList.value.push({
    ...row,
    migrateQuantity: 1,
    targetStoreId: null,
    targetCounterId: null
  });
}

/** 从迁移清单移除 */
function removeFromMigrateList(index) {
  migrateList.value.splice(index, 1);
}

/** 目标门店变化时加载对应柜台 */
function onTargetStoreChange(row) {
  row.targetCounterId = null; // 重置柜台选择
  if (row.targetStoreId) {
    listCounterByStore(row.targetStoreId).then(response => {
      // 为每个迁移项单独存储柜台选项
      row.targetCounters = response.data || [];
    });
  }
}

/** 根据门店ID获取柜台列表 */
function getCountersByStore(storeId) {
  if (!storeId) return [];
  // 从迁移清单中找到对应项的柜台选项
  const item = migrateList.value.find(item => item.targetStoreId === storeId);
  return item?.targetCounters || [];
}

/** 提交批量删除 */
function submitBatchDelete() {
  if (deleteList.value.length === 0) {
    proxy.$modal.msgWarning("请先添加要删除的库存");
    return;
  }

  // 验证删除数量
  for (let item of deleteList.value) {
    if (!item.deleteQuantity || item.deleteQuantity <= 0 || item.deleteQuantity > item.quantity) {
      proxy.$modal.msgError(`商品 ${item.productName} 的删除数量无效`);
      return;
    }
  }

  proxy.$modal.confirm('确认删除选中的库存吗？此操作不可恢复！').then(() => {
    const data = {
      remark: deleteReasonForm.value.remark || '', // 删除原因
      items: deleteList.value.map(item => ({
        stockId: item.id,
        quantity: item.deleteQuantity,
        remark: `库存删除 - ${item.productName}`
      }))
    };

    batchDeleteStock(data).then(() => {
      proxy.$modal.msgSuccess("库存删除成功");
      deleteDialogOpen.value = false;
      deleteList.value = [];
      getList(); // 刷新出库单列表
    }).catch(error => {
      proxy.$modal.msgError(error.message || "库存删除失败");
    });
  });
}

/** 提交批量迁移 */
function submitBatchMigrate() {
  if (migrateList.value.length === 0) {
    proxy.$modal.msgWarning("请先添加要迁移的库存");
    return;
  }

  // 验证迁移数据
  for (let item of migrateList.value) {
    if (!item.migrateQuantity || item.migrateQuantity <= 0 || item.migrateQuantity > item.quantity) {
      proxy.$modal.msgError(`商品 ${item.productName} 的迁移数量无效`);
      return;
    }
    if (!item.targetStoreId) {
      proxy.$modal.msgError(`商品 ${item.productName} 未选择目标门店`);
      return;
    }
    if (!item.targetCounterId) {
      proxy.$modal.msgError(`商品 ${item.productName} 未选择目标柜台`);
      return;
    }
  }

  proxy.$modal.confirm('确认迁移选中的库存吗？').then(() => {
    const data = {
      remark: migrateRemarkForm.value.remark || '', // 迁移备注
      items: migrateList.value.map(item => ({
        stockId: item.id,
        quantity: item.migrateQuantity,
        targetStoreId: item.targetStoreId,
        targetCounterId: item.targetCounterId,
        targetChannelId: item.channelId, // 保持原渠道
        remark: `库存迁移 - ${item.productName}`
      }))
    };

    batchMigrateStock(data).then(() => {
      proxy.$modal.msgSuccess("库存迁移成功");
      migrateDialogOpen.value = false;
      migrateList.value = [];
      getList(); // 刷新出库单列表
    }).catch(error => {
      proxy.$modal.msgError(error.message || "库存迁移失败");
    });
  });
}

onMounted(() => {
  getList();
});
</script>

<style scoped>
/* 库存表格容器样式 */
.stock-table-container {
  border: 1px solid #ebeef5;
  border-radius: 4px;
  overflow: hidden;
}

.stock-table-container .el-table {
  border: none;
}

.stock-table-container .el-pagination {
  padding: 10px;
  background-color: #f8f9fa;
  border-top: 1px solid #ebeef5;
  text-align: center;
}

/* 操作清单容器样式 */
.operation-list-container {
  border: 1px solid #ebeef5;
  border-radius: 4px;
  overflow: hidden;
}

.operation-list-container .el-table {
  border: none;
}

/* 表格高度固定，内容滚动 */
.stock-table-container .el-table__body-wrapper,
.operation-list-container .el-table__body-wrapper {
  overflow-y: auto;
}

/* 优化滚动条样式 */
.stock-table-container .el-table__body-wrapper::-webkit-scrollbar,
.operation-list-container .el-table__body-wrapper::-webkit-scrollbar {
  width: 6px;
}

.stock-table-container .el-table__body-wrapper::-webkit-scrollbar-thumb,
.operation-list-container .el-table__body-wrapper::-webkit-scrollbar-thumb {
  background-color: #dcdfe6;
  border-radius: 3px;
}

.stock-table-container .el-table__body-wrapper::-webkit-scrollbar-track,
.operation-list-container .el-table__body-wrapper::-webkit-scrollbar-track {
  background-color: #f5f7fa;
}

/* 弹窗内容区域优化 */
.el-dialog__body {
  max-height: 70vh;
  overflow-y: auto;
}

/* 分页组件小尺寸优化 */
.el-pagination.is-small .el-pagination__total,
.el-pagination.is-small .el-pagination__sizes,
.el-pagination.is-small .el-pagination__jump {
  font-size: 12px;
}
</style>
