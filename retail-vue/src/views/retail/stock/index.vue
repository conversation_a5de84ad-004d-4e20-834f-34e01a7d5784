<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryRef" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="商品" prop="productNumber">
        <el-input
          v-model="queryParams.productNumber"
          placeholder="请输入商品货号"
          clearable
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item label="商品名称" prop="productName">
        <el-input
          v-model="queryParams.productName"
          placeholder="请输入商品名称"
          clearable
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item label="品牌" prop="brand">
        <el-input
          v-model="queryParams.brand"
          placeholder="请输入品牌"
          clearable
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item label="品类" prop="category">
        <el-input
          v-model="queryParams.category"
          placeholder="请输入品类"
          clearable
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item label="门店" prop="storeName">
        <el-input
          v-model="queryParams.storeName"
          placeholder="请输入门店名称"
          clearable
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item label="柜台" prop="counterName">
        <el-input
          v-model="queryParams.counterName"
          placeholder="请输入柜台名称"
          clearable
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item label="渠道" prop="channelName">
        <el-input
          v-model="queryParams.channelName"
          placeholder="请输入渠道名称"
          clearable
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
        <el-button icon="Refresh" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="Download"
          @click="handleExport"
          v-hasPermi="['retail:stock:export']"
        >导出</el-button>
      </el-col>
      <right-toolbar :model-value="showSearch" @update:model-value="showSearch = $event" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="stockList">
      <el-table-column label="库存ID" align="center" prop="id" />
      <el-table-column label="商品图" align="center" width="80">
        <template #default="scope">
          <image-preview :src="scope.row.productImage" :width="50" :height="50"/>
        </template>
      </el-table-column>
      <el-table-column label="商品货号" align="center" prop="productNumber" />
      <el-table-column label="商品名称" align="center" prop="productName" />
      <el-table-column label="品牌" align="center" prop="brand" />
      <el-table-column label="品类" align="center" prop="category" />
      <el-table-column label="门店" align="center" prop="storeName" />
      <el-table-column label="柜台" align="center" prop="counterName" />
      <el-table-column label="渠道" align="center" prop="channelName" />
      <el-table-column label="批次号" align="center" prop="batchNumber" />
      <el-table-column label="期货状态" align="center" prop="isFutures">
        <template #default="scope">
          <el-tag :type="scope.row.isFutures ? 'warning' : 'success'">{{ scope.row.isFutures ? '是' : '否' }}</el-tag>
        </template>
      </el-table-column>
      <el-table-column label="进货价" align="center" prop="purchasePrice">
        <template #default="scope">
          {{ scope.row.purchasePrice }} 元
        </template>
      </el-table-column>
      <el-table-column label="库存数量" align="center" prop="quantity" />
      <el-table-column label="入库时间" align="center" prop="createTime" width="180">
        <template #default="scope">
          <span>{{ parseTime(scope.row.createTime) }}</span>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width" width="120">
        <template #default="scope">
          <el-button link type="primary" icon="View" @click="handleDetail(scope.row)" v-hasPermi="['retail:stock:query']">查看</el-button>
        </template>
      </el-table-column>
    </el-table>
    
    <pagination
      v-show="total>0"
      :total="total"
      :page="queryParams.pageNum"
      @update:page="queryParams.pageNum = $event"
      :limit="queryParams.pageSize"
      @update:limit="queryParams.pageSize = $event"
      @pagination="getList"
    />

    <!-- 库存详情对话框 -->
    <el-dialog title="库存详情" v-model="detailOpen" width="700px" append-to-body>
      <el-tabs type="border-card">
        <el-tab-pane label="基本信息">
          <el-descriptions :column="2" border>
            <el-descriptions-item label="商品图" :span="2">
              <image-preview :src="detail.productImage" :width="100" :height="100"/>
            </el-descriptions-item>
            <el-descriptions-item label="商品货号">{{ detail.productNumber }}</el-descriptions-item>
            <el-descriptions-item label="商品名称">{{ detail.productName }}</el-descriptions-item>
            <el-descriptions-item label="门店名称">{{ detail.storeName }}</el-descriptions-item>
            <el-descriptions-item label="柜台名称">{{ detail.counterName }}</el-descriptions-item>
            <el-descriptions-item label="渠道名称">{{ detail.channelName }}</el-descriptions-item>
            <el-descriptions-item label="批次号">{{ detail.batchNumber }}</el-descriptions-item>
            <el-descriptions-item label="期货状态">
              <el-tag :type="detail.isFutures ? 'warning' : 'success'">{{ detail.isFutures ? '是' : '否' }}</el-tag>
            </el-descriptions-item>
            <el-descriptions-item label="进货价">{{ detail.purchasePrice }} 元</el-descriptions-item>
            <el-descriptions-item label="库存数量">{{ detail.quantity }}</el-descriptions-item>
            <el-descriptions-item label="入库时间" :span="2">{{ parseTime(detail.createTime) }}</el-descriptions-item>
          </el-descriptions>
        </el-tab-pane>
        <el-tab-pane label="商品详情">
          <el-descriptions :column="2" border>
            <el-descriptions-item label="品牌" :span="2">{{ detail.brand }}</el-descriptions-item>
            <el-descriptions-item label="品类">{{ detail.category }}</el-descriptions-item>
            <el-descriptions-item label="规格">{{ detail.specification }}</el-descriptions-item>
            <el-descriptions-item label="颜色">{{ detail.color }}</el-descriptions-item>
            <el-descriptions-item label="材质">{{ detail.material }}</el-descriptions-item>
            <el-descriptions-item label="原产地">{{ detail.origin }}</el-descriptions-item>
            <el-descriptions-item label="执行标准">{{ detail.standard }}</el-descriptions-item>
            <el-descriptions-item label="安全类别">{{ detail.safetyCategory }}</el-descriptions-item>
            <el-descriptions-item label="产品等级">{{ detail.productGrade }}</el-descriptions-item>
            <el-descriptions-item label="国内参考价" :span="2">{{ detail.referencePrice }} 元</el-descriptions-item>
            <el-descriptions-item label="门店零售价" :span="2">{{ detail.retailPrice }} 元</el-descriptions-item>
          </el-descriptions>
        </el-tab-pane>
      </el-tabs>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="detailOpen = false">关 闭</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="Stock">
import { ref, reactive, toRefs, getCurrentInstance } from 'vue';
import { PictureFilled } from '@element-plus/icons-vue';
import { listStock, getStock } from "@/api/retail/inventory";

const { proxy } = getCurrentInstance();

const stockList = ref([]);
const detailOpen = ref(false);
const loading = ref(true);
const showSearch = ref(true);
const total = ref(0);
const detail = ref({});

const data = reactive({
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    productNumber: null,
    productName: null,
    brand: null,
    category: null,
    storeName: null,
    counterName: null,
    channelName: null
  }
});

const { queryParams } = toRefs(data);

/** 查询库存列表 */
function getList() {
  loading.value = true;
  listStock(queryParams.value).then(response => {
    stockList.value = response.rows;
    total.value = response.total;
    loading.value = false;
  });
}

/** 搜索按钮操作 */
function handleQuery() {
  queryParams.value.pageNum = 1;
  getList();
}

/** 重置按钮操作 */
function resetQuery() {
  proxy.resetForm("queryRef");
  handleQuery();
}

/** 查看详情按钮操作 */
function handleDetail(row) {
  detail.value = {};
  getStock(row.id).then(response => {
    detail.value = response.data;
    detailOpen.value = true;
  });
}

/** 导出按钮操作 */
function handleExport() {
  proxy.download('retail/stock/export', {
    ...queryParams.value
  }, `stock_${new Date().getTime()}.xlsx`)
}

getList();
</script> 