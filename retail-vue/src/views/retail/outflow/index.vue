<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryRef" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="流程名称" prop="name">
        <el-input
          v-model="queryParams.name"
          placeholder="请输入流程名称"
          clearable
          style="width: 200px"
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item label="门店名称" prop="storeName">
        <el-input
          v-model="queryParams.storeName"
          placeholder="请输入门店名称"
          clearable
          style="width: 200px"
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item label="状态" prop="status">
        <el-select v-model="queryParams.status" placeholder="请选择状态" clearable style="width: 120px">
          <el-option label="停用" :value="0" />
          <el-option label="启用" :value="1" />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
        <el-button icon="Refresh" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="Plus"
          @click="handleAdd"
          v-hasPermi="['retail:outflow:add']"
        >新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="Edit"
          :disabled="single"
          @click="handleUpdate"
          v-hasPermi="['retail:outflow:edit']"
        >修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="Delete"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['retail:outflow:remove']"
        >删除</el-button>
      </el-col>
      <right-toolbar :model-value="showSearch" @update:modelValue="showSearch = $event" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="flowList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="流程ID" align="center" prop="id" />
      <el-table-column label="流程名称" align="center" prop="name" />
      <el-table-column label="流程描述" align="center" prop="description" :show-overflow-tooltip="true" />
      <el-table-column label="门店名称" align="center" prop="storeName">
        <template #default="scope">
          {{ scope.row.storeName || '全局流程' }}
        </template>
      </el-table-column>
      <el-table-column label="审批角色" align="center" prop="approverRoles" :show-overflow-tooltip="true" />
      <el-table-column label="状态" align="center" prop="status">
        <template #default="scope">
          <el-switch
            v-model="scope.row.status"
            :active-value="1"
            :inactive-value="0"
            @change="handleStatusChange(scope.row)"
          />
        </template>
      </el-table-column>
      <el-table-column label="创建时间" align="center" prop="createTime" width="180">
        <template #default="scope">
          <span>{{ parseTime(scope.row.createTime) }}</span>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template #default="scope">
          <el-button link type="primary" icon="Edit" @click="handleUpdate(scope.row)" v-hasPermi="['retail:outflow:edit']">修改</el-button>
          <el-button link type="primary" icon="Delete" @click="handleDelete(scope.row)" v-hasPermi="['retail:outflow:remove']">删除</el-button>
        </template>
      </el-table-column>
    </el-table>
    
    <pagination
      v-show="total>0"
      :total="total"
      :page="queryParams.pageNum"
      :limit="queryParams.pageSize"
      @update:page="queryParams.pageNum = $event"
      @update:limit="queryParams.pageSize = $event"
      @pagination="getList"
    />

    <!-- 添加或修改审核流程对话框 -->
    <el-dialog :title="title" v-model="open" width="600px" append-to-body>
      <el-form ref="flowRef" :model="form" :rules="rules" label-width="100px">
        <el-form-item label="流程名称" prop="name">
          <el-input v-model="form.name" placeholder="请输入流程名称" />
        </el-form-item>
        <el-form-item label="流程描述" prop="description">
          <el-input v-model="form.description" type="textarea" placeholder="请输入流程描述" />
        </el-form-item>
        <el-form-item label="门店" prop="storeId">
          <el-select v-model="form.storeId" placeholder="请选择门店" clearable>
            <el-option label="全局流程" :value="null" />
            <el-option
              v-for="dict in storeOptions"
              :key="dict.id"
              :label="dict.storeName"
              :value="dict.id"
            />
          </el-select>
          <div class="el-form-item-msg">不选择门店表示此流程为全局流程</div>
        </el-form-item>
        <el-form-item label="审批角色" prop="approverRoles">
          <el-select v-model="form.approverRoles" placeholder="请选择审批角色" multiple>
            <el-option
              v-for="dict in roleOptions"
              :key="dict.roleId"
              :label="dict.roleName"
              :value="dict.roleKey"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="状态" prop="status">
          <el-radio-group v-model="form.status">
            <el-radio :label="1">启用</el-radio>
            <el-radio :label="0">停用</el-radio>
          </el-radio-group>
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="OutFlow">
import { ref, reactive, toRefs, onMounted, getCurrentInstance } from 'vue';
import { listOutFlow, getOutFlow, delOutFlow, addOutFlow, updateOutFlow, changeFlowStatus } from "@/api/retail/outflow";
import { listStore } from "@/api/retail/store";
import { listRole } from "@/api/system/role";

const { proxy } = getCurrentInstance();

const flowList = ref([]);
const open = ref(false);
const loading = ref(true);
const showSearch = ref(true);
const ids = ref([]);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);
const title = ref("");

// 门店选项
const storeOptions = ref([]);

// 角色选项
const roleOptions = ref([]);

const data = reactive({
  form: {
    id: null,
    name: null,
    description: null,
    storeId: null,
    approverRoles: [],
    status: 1
  },
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    name: null,
    storeName: null,
    status: null
  },
  rules: {
    name: [{ required: true, message: "流程名称不能为空", trigger: "blur" }],
    approverRoles: [{ required: true, message: "审批角色不能为空", trigger: "change" }]
  }
});

const { queryParams, form, rules } = toRefs(data);

/** 查询审核流程列表 */
function getList() {
  loading.value = true;
  listOutFlow(queryParams.value).then(response => {
    flowList.value = response.rows;
    total.value = response.total;
    loading.value = false;
  });
}

/** 查询门店列表 */
function getStoreList() {
  listStore().then(response => {
    storeOptions.value = response.rows;
  });
}

/** 查询角色列表 */
function getRoleList() {
  listRole().then(response => {
    roleOptions.value = response.rows;
  });
}

// 取消按钮
function cancel() {
  open.value = false;
  reset();
}

// 表单重置
function reset() {
  form.value = {
    id: null,
    name: null,
    description: null,
    storeId: null,
    approverRoles: [],
    status: 1
  };
  proxy.resetForm("flowRef");
}

/** 搜索按钮操作 */
function handleQuery() {
  queryParams.value.pageNum = 1;
  getList();
}

/** 重置按钮操作 */
function resetQuery() {
  proxy.resetForm("queryRef");
  queryParams.value.status = null;
  handleQuery();
}

// 多选框选中数据
function handleSelectionChange(selection) {
  ids.value = selection.map(item => item.id);
  single.value = selection.length != 1;
  multiple.value = !selection.length;
}

/** 新增按钮操作 */
function handleAdd() {
  reset();
  open.value = true;
  title.value = "添加审核流程";
  getStoreList();
  getRoleList();
}

/** 修改按钮操作 */
function handleUpdate(row) {
  reset();
  const _id = row.id || ids.value;
  getOutFlow(_id).then(response => {
    form.value = response.data;
    // 为多选角色转换为数组
    if (form.value.approverRoles && typeof form.value.approverRoles === 'string') {
      form.value.approverRoles = form.value.approverRoles.split(',');
    }
    open.value = true;
    title.value = "修改审核流程";
    getStoreList();
    getRoleList();
  });
}

/** 提交按钮 */
function submitForm() {
  proxy.$refs["flowRef"].validate(valid => {
    if (valid) {
      // 处理审批角色为逗号分隔的字符串
      const submitForm = { ...form.value };
      if (Array.isArray(submitForm.approverRoles)) {
        submitForm.approverRoles = submitForm.approverRoles.join(',');
      }
      
      if (form.value.id != null) {
        updateOutFlow(submitForm).then(response => {
          proxy.$modal.msgSuccess("修改成功");
          open.value = false;
          getList();
        });
      } else {
        addOutFlow(submitForm).then(response => {
          proxy.$modal.msgSuccess("新增成功");
          open.value = false;
          getList();
        });
      }
    }
  });
}

/** 删除按钮操作 */
function handleDelete(row) {
  const _ids = row.id || ids.value;
  proxy.$modal.confirm('是否确认删除审核流程编号为"' + _ids + '"的数据项？').then(function() {
    return delOutFlow(_ids);
  }).then(() => {
    getList();
    proxy.$modal.msgSuccess("删除成功");
  }).catch(() => {});
}

/** 状态修改 */
function handleStatusChange(row) {
  const text = row.status === 1 ? "启用" : "停用";
  proxy.$modal.confirm('确认要"' + text + '""' + row.name + '"流程吗?').then(function() {
    return changeFlowStatus(row.id, row.status);
  }).then(() => {
    getList();
    proxy.$modal.msgSuccess(text + "成功");
  }).catch(() => {
    row.status = row.status === 1 ? 0 : 1;
  });
}

onMounted(() => {
  getList();
  getStoreList();
  getRoleList();
});
</script>

<style scoped>
.el-form-item-msg {
  font-size: 12px;
  color: #909399;
  margin-top: 2px;
}
</style> 