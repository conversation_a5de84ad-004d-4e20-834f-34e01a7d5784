<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryRef" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="订单号" prop="orderNumber">
        <el-input
          v-model="queryParams.orderNumber"
          placeholder="请输入订单号"
          clearable
          style="width: 200px"
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item label="货号" prop="productNumber">
        <el-input
          v-model="queryParams.productNumber"
          placeholder="请输入商品货号"
          clearable
          style="width: 200px"
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item label="客户" prop="memberName">
        <el-input
          v-model="queryParams.memberName"
          placeholder="请输入客户姓名"
          clearable
          style="width: 200px"
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item label="电话" prop="phoneNumber">
        <el-input
          v-model="queryParams.phoneNumber"
          placeholder="请输入电话号码"
          clearable
          style="width: 200px"
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item label="门店" prop="storeName">
        <el-input
          v-model="queryParams.storeName"
          placeholder="请输入门店名称"
          clearable
          style="width: 200px"
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item label="性别" prop="gender">
        <el-select v-model="queryParams.gender" placeholder="请选择性别" clearable style="width: 120px">
          <el-option label="女" :value="0" />
          <el-option label="男" :value="1" />
        </el-select>
      </el-form-item>
      <el-form-item label="类型" prop="orderType">
        <el-select v-model="queryParams.orderType" placeholder="请选择类型" clearable style="width: 120px">
          <el-option label="售出单" :value="0" />
          <el-option label="预订单" :value="1" />
        </el-select>
      </el-form-item>
      <el-form-item label="业务" prop="businessType">
        <el-select v-model="queryParams.businessType" placeholder="请选择业务" clearable style="width: 120px">
          <el-option label="售出" :value="0" />
          <el-option label="预订" :value="1" />
          <el-option label="退货" :value="2" />
          <el-option label="取消" :value="3" />
          <el-option label="交货" :value="4" />
        </el-select>
      </el-form-item>
      <el-form-item label="流程" prop="processStatus">
        <el-select v-model="queryParams.processStatus" placeholder="请选择流程" clearable style="width: 120px">
          <el-option label="待审核" :value="0" />
          <el-option label="已审核" :value="1" />
          <el-option label="已驳回" :value="2" />
          <el-option label="已撤销" :value="3" />
        </el-select>
      </el-form-item>

      <el-form-item label="售出日期" prop="actualSaleTime">
        <el-date-picker
          v-model="queryParams.actualSaleTime"
          type="daterange"
          range-separator="至"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          format="YYYY-MM-DD"
          value-format="YYYY-MM-DD"
          style="width: 240px"
        />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
        <el-button icon="Refresh" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="Download"
          @click="handleExport"
          v-hasPermi="['retail:outorder:export']"
        >导出</el-button>
      </el-col>
      <right-toolbar :model-value="showSearch" @update:modelValue="showSearch = $event" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="outOrderList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="订单单号" align="center" prop="orderNumber" />
      <el-table-column label="订单类型" align="center" prop="orderType" width="80">
        <template #default="scope">
          <el-tag type="success" v-if="scope.row.orderType === 0">售出单</el-tag>
          <el-tag type="warning" v-else-if="scope.row.orderType === 1">预订单</el-tag>
        </template>
      </el-table-column>
      <el-table-column label="会员信息" align="center" width="180">
        <template #default="scope">
          <div v-if="scope.row.memberId" class="member-info-clean">
            <div class="member-name-row">{{ scope.row.memberName }}</div>
            <div class="member-phone-row">{{ scope.row.phoneNumber }}</div>
          </div>
          <div v-else class="member-info-clean">
            <div class="member-name-row">{{ scope.row.customerName }}</div>
            <div class="member-phone-row">{{ scope.row.customerPhone }}</div>
          </div>
        </template>
      </el-table-column>
      <el-table-column label="门店名称" align="center" prop="storeName" />
      <el-table-column label="订单总金额" align="center" prop="totalAmount">
        <template #default="scope">
          {{ scope.row.totalAmount }} 元
        </template>
      </el-table-column>
      <el-table-column label="支付方式" align="center" prop="payType">
        <template #default="scope">
          {{ getPayTypeName(scope.row.payType) }}
        </template>
      </el-table-column>
      <el-table-column label="业务场景" align="center" prop="businessType" width="80">
        <template #default="scope">
          <el-tag type="success" v-if="scope.row.businessType === 0">售出</el-tag>
          <el-tag type="warning" v-else-if="scope.row.businessType === 1">预订</el-tag>
          <el-tag type="danger" v-else-if="scope.row.businessType === 2">退货</el-tag>
          <el-tag type="info" v-else-if="scope.row.businessType === 3">取消</el-tag>
          <el-tag type="primary" v-else-if="scope.row.businessType === 4">交货</el-tag>
          <el-tag v-else>未知({{ scope.row.businessType }})</el-tag>
        </template>
      </el-table-column>
      <el-table-column label="流程状态" align="center" prop="processStatus" width="80">
        <template #default="scope">
          <el-tag type="info" v-if="scope.row.processStatus === 0">待审核</el-tag>
          <el-tag type="success" v-else-if="scope.row.processStatus === 1">已审核</el-tag>
          <el-tag type="danger" v-else-if="scope.row.processStatus === 2">已驳回</el-tag>
          <el-tag type="warning" v-else-if="scope.row.processStatus === 3">已撤销</el-tag>
          <el-tag v-else>未知({{ scope.row.processStatus }})</el-tag>
        </template>
      </el-table-column>

      <el-table-column label="订单状态" align="center" width="100">
        <template #default="scope">
          <el-tag type="danger" v-if="getOrderStatus(scope.row) === 'invalid'">失效</el-tag>
          <el-tag type="warning" v-else-if="getOrderStatus(scope.row) === 'canceled'">撤销/取消</el-tag>
          <el-tag type="info" v-else-if="getOrderStatus(scope.row) === 'returned'">退货</el-tag>
          <el-tag type="success" v-else-if="getOrderStatus(scope.row) === 'completed'">完成</el-tag>
          <el-tag type="primary" v-else-if="getOrderStatus(scope.row) === 'processing'">进行中</el-tag>
        </template>
      </el-table-column>

      <el-table-column label="实际售出时间" align="center" prop="actualSaleTime" width="180">
        <template #default="scope">
          <span>{{ parseTime(scope.row.actualSaleTime) }}</span>
        </template>
      </el-table-column>
      <el-table-column label="创建日期" align="center" prop="createTime" width="180">
        <template #default="scope">
          <span>{{ parseTime(scope.row.createTime) }}</span>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template #default="scope">
          <el-button link type="primary" icon="View" @click="handleDetail(scope.row)" v-hasPermi="['retail:outorder:query']">查看</el-button>
          <el-button
            v-if="(scope.row.businessType === 0 && scope.row.processStatus === 1) ||
                 (scope.row.businessType === 4 && scope.row.processStatus === 1)"
            link type="warning" icon="Sell" @click="handleApplyReturn(scope.row)"
            v-hasPermi="['retail:outorder:return']">申请退货</el-button>
          <el-button
            v-if="scope.row.orderType === 1 &&
                 ((scope.row.businessType === 1 && scope.row.processStatus === 1) ||
                  (scope.row.businessType === 3 && scope.row.processStatus === 2))"
            link type="primary" icon="Box" @click="handleDeliver(scope.row)"
            v-hasPermi="['retail:outorder:deliver']">交货</el-button>
          <el-button
            v-if="scope.row.orderType === 1 &&
                 ((scope.row.businessType === 1 && scope.row.processStatus === 1) ||
                  (scope.row.businessType === 3 && scope.row.processStatus === 2))"
            link type="danger" icon="Delete" @click="handleCancelReserve(scope.row)"
            v-hasPermi="['retail:outorder:reservecancel']">取消预订</el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total>0"
      :total="total"
      :page="queryParams.pageNum"
      :limit="queryParams.pageSize"
      @update:page="queryParams.pageNum = $event"
      @update:limit="queryParams.pageSize = $event"
      @pagination="getList"
    />

    <!-- 订单详情对话框 -->
    <el-dialog title="订单详情" v-model="detailOpen" width="900px" append-to-body destroy-on-close>
      <template v-if="detailOpen">
        <el-descriptions :column="2" border :column-width="['50%', '50%']">
          <el-descriptions-item label="订单单号">{{ detail.orderNumber }}</el-descriptions-item>
          <el-descriptions-item label="订单类型">
            <el-tag type="success" v-if="detail.orderType === 0">售出单</el-tag>
            <el-tag type="warning" v-else-if="detail.orderType === 1">预订单</el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="会员信息" v-if="detail.memberId">
            <div class="member-detail-info">
              <div class="member-header">
                <el-tag :type="getMemberLevelTag(detail.memberLevel)">
                  {{ getMemberLevelName(detail.memberLevel) }}
                </el-tag>
                <span class="member-id">ID: {{ detail.memberId }}</span>
              </div>
              <div class="member-body">
                <div class="member-row-vertical">
                  <div class="member-item-vertical">
                    <span class="label">姓名:</span>
                    <span class="value">{{ detail.memberName }}</span>
                  </div>
                  <div class="member-item-vertical">
                    <span class="label">手机号:</span>
                    <span class="value">{{ detail.phoneNumber }}</span>
                  </div>
                  <div class="member-item-vertical">
                    <span class="label">性别:</span>
                    <span class="value">{{ detail.gender === '1' ? '男' : '女' }}</span>
                  </div>
                </div>
              </div>
            </div>
          </el-descriptions-item>
          <el-descriptions-item label="客户信息" v-else>
            <div class="member-body">
              <div class="member-row-vertical">
                <div class="member-item-vertical">
                  <span class="label">姓名:</span>
                  <span class="value">{{ detail.customerName }}</span>
                </div>
                <div class="member-item-vertical">
                  <span class="label">手机号:</span>
                  <span class="value">{{ detail.customerPhone }}</span>
                </div>
                <div class="member-item-vertical">
                  <span class="label">性别:</span>
                  <span class="value">{{ detail.customerGender === 1 ? '男' : '女' }}</span>
                </div>
              </div>
            </div>
          </el-descriptions-item>
          <el-descriptions-item label="门店名称">{{ detail.storeName }}</el-descriptions-item>
          <el-descriptions-item label="订单总金额">{{ detail.totalAmount }} 元</el-descriptions-item>
          <el-descriptions-item label="支付方式">{{ getPayTypeName(detail.payType) }}</el-descriptions-item>

          <!-- 预订单特有字段 -->
          <template v-if="detail.orderType === 1">
            <el-descriptions-item label="预订支付类型">
              <el-tag type="warning" v-if="detail.depositType === 0">定金</el-tag>
              <el-tag type="success" v-else-if="detail.depositType === 1">全款</el-tag>
            </el-descriptions-item>
            <el-descriptions-item label="定金金额" v-if="detail.depositType === 0">{{ detail.depositAmount }} 元</el-descriptions-item>
            <el-descriptions-item label="尾款金额" v-if="detail.depositType === 0 && detail.status >= 8">{{ detail.balanceAmount }} 元</el-descriptions-item>
            <el-descriptions-item label="尾款支付方式" v-if="detail.depositType === 0 && detail.status >= 8">{{ getPayTypeName(detail.balancePayType) }}</el-descriptions-item>
          </template>

          <el-descriptions-item label="业务场景">
            <el-tag type="success" v-if="detail.businessType === 0">售出</el-tag>
            <el-tag type="warning" v-else-if="detail.businessType === 1">预订</el-tag>
            <el-tag type="danger" v-else-if="detail.businessType === 2">退货</el-tag>
            <el-tag type="info" v-else-if="detail.businessType === 3">取消</el-tag>
            <el-tag type="primary" v-else-if="detail.businessType === 4">交货</el-tag>
            <el-tag v-else>未知({{ detail.businessType }})</el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="流程状态">
            <el-tag type="info" v-if="detail.processStatus === 0">待审核</el-tag>
            <el-tag type="success" v-else-if="detail.processStatus === 1">已审核</el-tag>
            <el-tag type="danger" v-else-if="detail.processStatus === 2">已驳回</el-tag>
            <el-tag type="warning" v-else-if="detail.processStatus === 3">已撤销</el-tag>
            <el-tag v-else>未知({{ detail.processStatus }})</el-tag>
          </el-descriptions-item>

          <el-descriptions-item label="订单状态">
            <el-tag type="danger" v-if="getOrderStatus(detail) === 'invalid'">已失效</el-tag>
            <el-tag type="warning" v-else-if="getOrderStatus(detail) === 'canceled'">已撤销/取消</el-tag>
            <el-tag type="info" v-else-if="getOrderStatus(detail) === 'returned'">已退货</el-tag>
            <el-tag type="success" v-else-if="getOrderStatus(detail) === 'completed'">已完成</el-tag>
            <el-tag type="primary" v-else-if="getOrderStatus(detail) === 'processing'">进行中</el-tag>
          </el-descriptions-item>

          <el-descriptions-item label="实际售出时间">{{ parseTime(detail.actualSaleTime) }}</el-descriptions-item>
          <el-descriptions-item label="创建日期">{{ parseTime(detail.createTime) }}</el-descriptions-item>
          <el-descriptions-item label="备注" v-if="detail.remark" :span="2">{{ detail.remark }}</el-descriptions-item>
        </el-descriptions>

        <el-divider content-position="center">订单明细</el-divider>
        <el-table :data="detailListRef">
          <el-table-column label="序号" type="index" width="50" />
          <el-table-column label="商品图" width="80">
            <template #default="scope">
              <image-preview :src="scope.row.productImage" :width="50" :height="50"/>
            </template>
          </el-table-column>
          <el-table-column label="商品" prop="productName" show-overflow-tooltip />
          <el-table-column label="货号" prop="productNumber" show-overflow-tooltip />
          <el-table-column label="柜台" prop="counterName" />
          <el-table-column label="渠道" prop="channelName" />
          <el-table-column label="数量" prop="quantity" />
          <el-table-column label="销售价" prop="salePrice">
            <template #default="scope">
              {{ scope.row.salePrice }} 元
            </template>
          </el-table-column>
          <el-table-column label="批次号" prop="batchNumber" show-overflow-tooltip />
          <el-table-column label="备注" prop="remark" show-overflow-tooltip />
        </el-table>

        <el-divider content-position="center" v-if="auditListRef && auditListRef.length > 0">
          <el-icon><Histogram /></el-icon> 审核记录
        </el-divider>
        <div v-if="auditListRef && auditListRef.length > 0" class="audit-records-container">
          <el-timeline>
            <el-timeline-item
              v-for="(activity, index) in auditListRef"
              :key="index"
              :type="getAuditItemType(activity.businessType, activity.processStatus)"
              :color="getAuditItemColor(activity.businessType, activity.processStatus)"
              :timestamp="parseTime(activity.operateTime)"
            >
              <el-card>
                <template #header>
                  <div style="display: flex; justify-content: space-between; align-items: center;">
                    <el-tag :type="getAuditTagType(activity.businessType, activity.processStatus)">
                      {{ getAuditActionName(activity.businessType, activity.processStatus) }}
                    </el-tag>
                    <div style="display: flex; align-items: center;">
                      <el-avatar :size="24" :src="getAvatarUrl(activity.operator)" style="margin-right: 5px;">{{ getAvatarText(activity.operator) }}</el-avatar>
                      <span>{{ activity.operator }}</span>
                    </div>
                  </div>
                </template>
                <div v-if="activity.reason">
                  <el-alert
                    :title="activity.reason"
                    :type="getAlertType(activity.businessType, activity.processStatus)"
                    :closable="false"
                    show-icon
                  />
                </div>
                <div v-else style="text-align: center; color: #909399; font-size: 12px;">
                  无备注信息
                </div>
              </el-card>
            </el-timeline-item>
          </el-timeline>
        </div>
      </template>

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="detailOpen = false">关 闭</el-button>
          <el-button
            v-if="(detail.businessType === 0 && detail.processStatus === 1) ||
                 (detail.businessType === 4 && detail.processStatus === 1)"
            type="warning" @click="handleApplyReturn(detail)">申请退货</el-button>
          <el-button
            v-if="detail.orderType === 1 &&
                 ((detail.businessType === 1 && detail.processStatus === 1) ||
                  (detail.businessType === 3 && detail.processStatus === 2))"
            type="primary" @click="handleDeliver(detail)">交货</el-button>
          <el-button
            v-if="detail.orderType === 1 &&
                 ((detail.businessType === 1 && detail.processStatus === 1) ||
                  (detail.businessType === 3 && detail.processStatus === 2))"
            type="danger" @click="handleCancelReserve(detail)">取消预订</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 退货申请对话框 -->
    <el-dialog title="退货申请" v-model="applyReturnOpen" width="500px" append-to-body destroy-on-close>
      <el-form :model="returnForm" ref="returnFormRef" :rules="returnRules" label-width="100px">
        <el-form-item label="退货原因" prop="returnReason">
          <el-input v-model="returnForm.returnReason" type="textarea" placeholder="请输入退货原因" :rows="3" />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitApplyReturn">确 认</el-button>
          <el-button @click="applyReturnOpen = false">取 消</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 交货对话框 -->
    <el-dialog title="预订单交货" v-model="deliverOpen" width="500px" append-to-body destroy-on-close>
      <el-form :model="deliverForm" ref="deliverFormRef" :rules="deliverRules" label-width="120px">
        <el-descriptions :column="1" border size="small" class="mb20">
          <el-descriptions-item label="订单单号">{{ deliverOrder.orderNumber }}</el-descriptions-item>
          <el-descriptions-item label="会员姓名">{{ deliverOrder.memberName }}</el-descriptions-item>
          <el-descriptions-item label="订单总金额">{{ deliverOrder.totalAmount }} 元</el-descriptions-item>
          <el-descriptions-item label="预订支付类型">
            <el-tag type="warning" v-if="deliverOrder.depositType === 0">定金</el-tag>
            <el-tag type="success" v-else-if="deliverOrder.depositType === 1">全款</el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="定金金额" v-if="deliverOrder.depositType === 0">{{ deliverOrder.depositAmount }} 元</el-descriptions-item>
        </el-descriptions>

        <template v-if="deliverOrder.depositType === 0">
          <el-form-item label="尾款金额" prop="balanceAmount">
            <el-input v-model="deliverForm.balanceAmount" disabled style="width: 200px" />
          </el-form-item>
          <el-form-item label="尾款支付方式" prop="balancePayType">
            <el-select v-model="deliverForm.balancePayType" placeholder="请选择支付方式" style="width: 200px">
              <el-option
                v-for="dict in retail_pay_type"
                :key="dict.value"
                :label="dict.label"
                :value="parseInt(dict.value)"
              />
            </el-select>
          </el-form-item>
        </template>

        <el-form-item label="备注" prop="remark">
          <el-input v-model="deliverForm.remark" type="textarea" placeholder="请输入备注信息" :rows="3" />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitDeliver">确 认</el-button>
          <el-button @click="deliverOpen = false">取 消</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 取消预订单对话框 -->
    <el-dialog title="取消预订单" v-model="cancelReserveOpen" width="500px" append-to-body destroy-on-close>
      <el-form :model="cancelReserveForm" ref="cancelReserveFormRef" :rules="cancelReserveRules" label-width="100px">
        <el-descriptions :column="1" border size="small" class="mb20">
          <el-descriptions-item label="订单单号">{{ cancelOrder.orderNumber }}</el-descriptions-item>
          <el-descriptions-item label="会员姓名">{{ cancelOrder.memberName }}</el-descriptions-item>
          <el-descriptions-item label="订单总金额">{{ cancelOrder.totalAmount }} 元</el-descriptions-item>
        </el-descriptions>

        <el-form-item label="取消原因" prop="cancelReason">
          <el-input v-model="cancelReserveForm.cancelReason" type="textarea" placeholder="请输入取消原因" :rows="3" />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitCancelReserve">确 认</el-button>
          <el-button @click="cancelReserveOpen = false">取 消</el-button>
        </div>
      </template>
    </el-dialog>

  </div>
</template>

<script setup name="OrderManage">
import { ref, reactive, toRefs, onMounted, getCurrentInstance, computed } from 'vue';
import { listOutOrder, getOutOrder, applyReturnOutOrder } from "@/api/retail/outorder";
import { cancelReserveOrder, deliverReserveOrderDetail } from "@/api/retail/reserve";
import { listAllMemberLevel } from "@/api/member/index";
import { Histogram } from '@element-plus/icons-vue';
import useUserStore from '@/store/modules/user';

const { proxy } = getCurrentInstance();
const userStore = useUserStore();
const userName = userStore.name; // 获取当前登录用户名

const outOrderList = ref([]);
const detailOpen = ref(false);
const applyReturnOpen = ref(false);
const deliverOpen = ref(false);
const cancelReserveOpen = ref(false); // 取消预订单对话框
const loading = ref(true);
const showSearch = ref(true);
const ids = ref([]);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);
const detail = ref({});
const memberLevels = ref([]); // 会员等级列表
const deliverOrder = ref({}); // 交货订单
const cancelOrder = ref({}); // 取消预订单

// 退货申请表单
const returnFormRef = ref(null);
const returnForm = ref({
  id: null,
  returnReason: ''
});
const returnRules = {
  returnReason: [{ required: true, message: "退货原因不能为空", trigger: "blur" }]
};

// 交货表单
const deliverFormRef = ref(null);
const deliverForm = ref({
  id: null,
  balanceAmount: 0,
  balancePayType: null,
  remark: ''
});
const deliverRules = {
  balancePayType: [{ required: true, message: "尾款支付方式不能为空", trigger: "change" }]
};

// 取消预订单表单
const cancelReserveFormRef = ref(null);
const cancelReserveForm = ref({
  id: null,
  cancelReason: ''
});
const cancelReserveRules = {
  cancelReason: [{ required: true, message: "取消原因不能为空", trigger: "blur" }]
};



// 计算属性，确保详情数据的安全访问
const detailListRef = computed(() => detail.value.detailList || []);
const auditListRef = computed(() => detail.value.auditList || []);

// 引入字典数据
const { retail_pay_type } = proxy.useDict("retail_pay_type");

const data = reactive({
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    orderNumber: null,
    memberName: null,
    phoneNumber: null,
    gender: null,
    storeName: null,
    productNumber: null,
    orderType: null,
    businessType: null,
    processStatus: null,
    actualSaleTime: null
  }
});

const { queryParams } = toRefs(data);

/** 查询订单列表 */
function getList() {
  loading.value = true;
  let params = {...queryParams.value};

  // 确保 params 对象存在
  if (!params.params) {
    params.params = {};
  }

  // 排除库存删除和迁移的出库单，只显示正常的订单
  params['excludeBusinessTypes'] = [5, 6]; // 5=库存删除, 6=库存迁移

  // 处理日期范围
  if (params.actualSaleTime && params.actualSaleTime.length === 2) {
    // 日期选择器已经配置为 YYYY-MM-DD 格式，直接使用
    params.params.beginActualSaleTime = params.actualSaleTime[0];
    params.params.endActualSaleTime = params.actualSaleTime[1];
  }
  delete params.actualSaleTime;

  listOutOrder(params).then(response => {
    outOrderList.value = response.rows;
    total.value = response.total;
    loading.value = false;
  });
}

/** 搜索按钮操作 */
function handleQuery() {
  queryParams.value.pageNum = 1;
  getList();
}

/** 重置按钮操作 */
function resetQuery() {
  proxy.resetForm("queryRef");
  queryParams.value.actualSaleTime = null;
  queryParams.value.gender = null;
  queryParams.value.productNumber = null;
  queryParams.value.orderType = null;
  queryParams.value.businessType = null;
  queryParams.value.processStatus = null;
  handleQuery();
}

// 多选框选中数据
function handleSelectionChange(selection) {
  ids.value = selection.map(item => item.id);
  single.value = selection.length != 1;
  multiple.value = !selection.length;
}

/** 查看详情按钮操作 */
function handleDetail(row) {
  // 防止无效点击
  if (!row || !row.id) return;

  // 先重置对话框状态，确保DOM正确卸载
  detailOpen.value = false;
  detail.value = {};

  // 使用setTimeout确保上一个对话框完全关闭
  setTimeout(() => {
    getOutOrder(row.id).then(response => {
      if (response && response.data) {
        detail.value = response.data;
        // 确保明细和审核记录数据为数组
        if (!detail.value.detailList) detail.value.detailList = [];
        if (!detail.value.auditList) detail.value.auditList = [];
        // 设置对话框为打开状态
        detailOpen.value = true;
      }
    }).catch(error => {
      console.error("获取订单详情失败:", error);
    });
  }, 100);
}

/** 导出按钮操作 */
function handleExport() {
  let params = {...queryParams.value};

  // 确保 params 对象存在
  if (!params.params) {
    params.params = {};
  }

  // 排除库存删除和迁移的出库单，只导出正常的订单
  params['excludeBusinessTypes'] = [5, 6]; // 5=库存删除, 6=库存迁移

  // 处理日期范围
  if (params.actualSaleTime && params.actualSaleTime.length === 2) {
    // 日期选择器已经配置为 YYYY-MM-DD 格式，直接使用
    params.params.beginActualSaleTime = params.actualSaleTime[0];
    params.params.endActualSaleTime = params.actualSaleTime[1];
  }
  delete params.actualSaleTime;

  proxy.download('retail/outorder/export', params, `outorder_${new Date().getTime()}.xlsx`)
}

/** 获取支付方式名称 */
function getPayTypeName(payType) {
  if (!payType && payType !== 0) return '';
  const payTypeObj = retail_pay_type.value.find(item => Number(item.value) === Number(payType));
  return payTypeObj ? payTypeObj.label : '未知支付方式';
}

/** 获取订单状态 */
function getOrderStatus(order) {
  if (!order) return 'processing';

  // 1. 已失效：售出单申请被驳回、预订单申请被驳回
  if ((order.businessType === 0 || order.businessType === 1) && order.processStatus === 2) {
    return 'invalid';
  }

  // 2. 已撤销/取消：售出单已撤销、预订单已撤销、预订单取消已审核通过
  if (((order.businessType === 0 || order.businessType === 1) && order.processStatus === 3) ||
      (order.businessType === 3 && order.processStatus === 1)) {
    return 'canceled';
  }

  // 3. 已退货：售出单退货已审核通过、预订单退货已审核通过
  if (order.businessType === 2 && order.processStatus === 1) {
    return 'returned';
  }

  // 4. 已完成：售出单申请已审核通过、预订单交货已审核通过、售出单退货申请被驳回、预订单退货申请被驳回
  if ((order.businessType === 0 && order.processStatus === 1) ||
      (order.businessType === 4 && order.processStatus === 1) ||
      (order.businessType === 2 && order.processStatus === 2)) {
    return 'completed';
  }

  // 5. 进行中：其他的情况
  return 'processing';
}

/** 获取审核操作类型名称 */
function getAuditActionName(businessType, processStatus) {
  // 使用新的 businessType 和 processStatus 参数
  if (businessType === 0) { // 售出业务
    if (processStatus === 0) return "提交售出审核";
    if (processStatus === 1) return "售出审核通过";
    if (processStatus === 2) return "售出审核驳回";
    if (processStatus === 3) return "售出订单撤销";
  } else if (businessType === 1) { // 预订业务
    if (processStatus === 0) return "提交预订审核";
    if (processStatus === 1) return "预订审核通过";
    if (processStatus === 2) return "预订审核驳回";
    if (processStatus === 3) return "预订订单撤销";
  } else if (businessType === 2) { // 退货业务
    if (processStatus === 0) return "申请退货";
    if (processStatus === 1) return "退货审核通过";
    if (processStatus === 2) return "退货审核驳回";
    if (processStatus === 3) return "退货已撤销";
  } else if (businessType === 3) { // 取消业务
    if (processStatus === 0) return "申请取消";
    if (processStatus === 1) return "取消审核通过";
    if (processStatus === 2) return "取消审核驳回";
    if (processStatus === 3) return "取消申请撤销";
  } else if (businessType === 4) { // 交货业务
    if (processStatus === 0) return "申请交货";
    if (processStatus === 1) return "交货审核通过";
    if (processStatus === 2) return "交货审核驳回";
    if (processStatus === 3) return "交货申请撤销";
  }

  return "未知操作";
}

/** 获取审核时间线项目类型 */
function getAuditItemType(_, processStatus) {
  // 使用新的 processStatus 参数
  // 根据流程状态返回类型
  if (processStatus === 0) return "primary";  // 待审核
  if (processStatus === 1) return "success";  // 已审核
  if (processStatus === 2) return "danger";   // 已驳回
  if (processStatus === 3) return "warning";  // 已撤销

  return "info";
}

/** 获取审核时间线项目颜色 */
function getAuditItemColor(_, processStatus) {
  // 使用新的 processStatus 参数
  // 根据流程状态返回颜色
  if (processStatus === 0) return "#409EFF";  // 待审核 - 蓝色
  if (processStatus === 1) return "#67C23A";  // 已审核 - 绿色
  if (processStatus === 2) return "#F56C6C";  // 已驳回 - 红色
  if (processStatus === 3) return "#E6A23C";  // 已撤销 - 黄色

  return "#909399"; // 默认灰色
}

/** 获取Alert类型 */
function getAlertType(_, processStatus) {
  // 使用新的 processStatus 参数
  // 根据流程状态返回 Alert 类型
  if (processStatus === 0) return "info";     // 待审核
  if (processStatus === 1) return "success";  // 已审核
  if (processStatus === 2) return "error";    // 已驳回
  if (processStatus === 3) return "warning";  // 已撤销

  return "info"; // 默认信息类型
}

/** 获取审核操作类型Tag类型 */
function getAuditTagType(_, processStatus) {
  // 使用新的 processStatus 参数
  // 根据流程状态返回 Tag 类型
  if (processStatus === 0) return "primary";  // 待审核
  if (processStatus === 1) return "success";  // 已审核
  if (processStatus === 2) return "danger";   // 已驳回
  if (processStatus === 3) return "warning";  // 已撤销

  return "info"; // 默认信息类型
}



/** 获取头像URL */
function getAvatarUrl() {
  // 这里可以根据实际情况返回用户头像URL
  // 如果有用户头像服务，可以调用对应的服务获取
  return ''; // 返回空字符串会使用默认的文本头像
}

/** 获取头像文本 */
function getAvatarText(username) {
  if (!username) return '用';
  // 返回用户名的第一个字符作为头像文本
  return username.charAt(0);
}

/** 处理取消预订单 */
function handleCancelReserve(row) {
  // 防止无效点击
  if (!row || !row.id) return;

  // 检查订单状态
  if (row.orderType !== 1 ||
      !((row.businessType === 1 && row.processStatus === 1) ||
         (row.businessType === 3 && row.processStatus === 2))) {
    proxy.$modal.msgError("只有已审核的预订单或取消被驳回的预订单才能进行取消操作");
    return;
  }

  // 重置表单
  cancelReserveForm.value = {
    id: row.id,
    cancelReason: ''
  };

  // 保存当前订单信息
  cancelOrder.value = row;

  // 打开对话框
  cancelReserveOpen.value = true;
}

/** 提交取消预订单 */
function submitCancelReserve() {
  cancelReserveFormRef.value.validate(valid => {
    if (valid) {
      const data = {
        id: cancelReserveForm.value.id,
        cancelReason: cancelReserveForm.value.cancelReason
      };

      cancelReserveOrder(data).then(() => {
        proxy.$modal.msgSuccess("取消预订申请提交成功");
        cancelReserveOpen.value = false;
        getList();
      }).catch(error => {
        proxy.$modal.msgError(error.message || "取消预订申请提交失败");
      });
    }
  });
}



/** 提交交货 */
function submitDeliver() {
  proxy.$refs.deliverFormRef.validate(valid => {
    if (valid) {
      const data = {
        orderId: deliverForm.value.id,
        balancePayType: deliverForm.value.balancePayType,
        balanceAmount: deliverForm.value.balanceAmount,
        remark: deliverForm.value.remark
      };

      deliverReserveOrderDetail(data).then(() => {
        proxy.$modal.msgSuccess("交货申请提交成功");
        deliverOpen.value = false;
        getList();
      }).catch(error => {
        proxy.$modal.msgError(error.message || "交货申请提交失败");
      });
    }
  });
}

/** 获取会员等级标签类型 */
function getMemberLevelTag(level) {
  if (!level) return 'info';
  const tagMap = {
    1: 'info',    // 普通会员
    2: 'info',    // 银卡会员
    3: 'warning', // 金卡会员
    4: 'success', // VIP会员
    5: 'danger'   // 钻石会员
  };
  return tagMap[level] || 'info';
}

/** 获取会员等级名称 */
function getMemberLevelName(level) {
  if (!level) return '普通会员';
  const nameMap = {
    1: '普通会员',
    2: '银卡会员',
    3: '金卡会员',
    4: 'VIP会员',
    5: '钻石会员'
  };
  return nameMap[level] || '普通会员';
}



/** 获取会员等级列表 */
function getMemberLevels() {
  listAllMemberLevel().then(response => {
    memberLevels.value = response.data || [];
  });
}


/** 处理交货按钮操作 */
function handleDeliver(row) {
  // 防止无效点击
  if (!row || !row.id) return;

  // 检查订单状态
  if (row.orderType !== 1 ||
      !((row.businessType === 1 && row.processStatus === 1) ||
         (row.businessType === 3 && row.processStatus === 2))) {
    proxy.$modal.msgError("只有已审核的预订单或取消被驳回的预订单才能进行交货操作");
    return;
  }

  // 重置表单
  deliverForm.value = {
    id: row.id,
    balanceAmount: 0,
    balancePayType: null,
    remark: ''
  };

  // 保存当前订单信息
  deliverOrder.value = row;

  // 如果是定金支付，设置固定尾款金额
  if (row.depositType === 0) {
    deliverForm.value.balanceAmount = (row.totalAmount - row.depositAmount).toFixed(2);
  }

  // 打开对话框
  deliverOpen.value = true;
}

/** 申请退货按钮操作 */
function handleApplyReturn(row) {
  // 防止无效点击
  if (!row || !row.id) return;

  // 重置表单
  returnForm.value = {
    id: row.id,
    returnReason: ''
  };

  // 打开退货申请对话框
  applyReturnOpen.value = true;
}

/** 提交退货申请 */
function submitApplyReturn() {
  proxy.$refs.returnFormRef.validate(valid => {
    if (!valid) return;

    // 构造与后端接口匹配的参数
    const params = {
      id: returnForm.value.id,
      returnReason: returnForm.value.returnReason
    };

    // 调用退货申请API
    applyReturnOutOrder(params).then(() => {
      proxy.$modal.msgSuccess("退货申请提交成功");
      applyReturnOpen.value = false;
      getList();
      // 如果详情对话框打开，刷新详情
      if (detailOpen.value && detail.value.id === returnForm.value.id) {
        handleDetail({id: returnForm.value.id});
      }
    }).catch(error => {
      proxy.$modal.msgError(error.message || "退货申请提交失败");
    });
  });
}





onMounted(() => {
  getList();
  getMemberLevels(); // 获取会员等级列表
});
</script>

<style scoped>
.audit-timeline-container {
  padding: 15px 10px;
  background-color: #f9f9f9;
  border-radius: 4px;
  margin-bottom: 15px;
}

.audit-card {
  border-radius: 8px;
  transition: all 0.3s;
}

.audit-card :deep(.el-card__header) {
  padding: 10px;
}

.audit-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 12px rgba(0, 0, 0, 0.1);
}

.audit-card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.audit-card-operator {
  display: flex;
  align-items: center;
  color: #909399;
  font-size: 13px;
}

.mr5 {
  margin-right: 5px;
}

.audit-reason {
  margin-top: 10px;
}

.audit-no-reason {
  display: flex;
  justify-content: center;
  padding: 5px 0;
  color: #909399;
  font-size: 12px;
  font-style: italic;
}

.audit-debug-info {
  margin-top: 8px;
  padding: 5px;
  background-color: #fff9e6;
  border-radius: 4px;
  border-left: 3px solid #e6a23c;
}

.ml10 {
  margin-left: 10px;
}

.mt10 {
  margin-top: 10px;
}

/* 会员信息样式 */
.member-info-cell {
  display: flex;
  align-items: center;
  gap: 5px;
  margin-bottom: 3px;
}

.member-name {
  font-weight: bold;
  font-size: 14px;
}

.member-phone {
  color: #606266;
  font-size: 13px;
}

.member-info-clean {
  display: flex;
  flex-direction: column;
  gap: 3px;
  text-align: center;
  padding: 2px 0;
}

.member-name-row {
  font-weight: 500;
  font-size: 14px;
  color: #303133;
  line-height: 1.4;
}

.member-phone-row {
  font-size: 13px;
  color: #909399;
  line-height: 1.4;
}

/* 会员详情信息样式 */
.member-detail-info {
  border: 1px solid #ebeef5;
  border-radius: 4px;
  padding: 8px 10px;
  background-color: #f8f9fa;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.member-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
  padding-bottom: 8px;
  border-bottom: 1px solid #ebeef5;
}

.member-id {
  font-size: 12px;
  color: #909399;
}

.member-body {
  display: flex;
  flex-direction: column;
}

.member-row {
  display: flex;
  justify-content: space-between;
  flex-wrap: wrap;
  gap: 10px;
  align-items: center;
}

.member-item {
  display: flex;
  align-items: center;
  flex: 1;
  min-width: 120px;
}

.member-item .label {
  font-weight: bold;
  width: 60px;
  color: #606266;
  font-size: 13px;
}

.member-item .value {
  color: #303133;
  font-size: 13px;
}

/* 垂直布局的会员信息样式 */
.member-row-vertical {
  display: flex;
  flex-direction: column;
  gap: 5px;
}

.member-item-vertical {
  display: flex;
  align-items: center;
  margin-bottom: 3px;
}

.member-item-vertical .label {
  font-weight: bold;
  width: 60px;
  color: #606266;
  font-size: 13px;
}

.member-item-vertical .value {
  color: #303133;
  font-size: 13px;
}

/* 调整会员信息区域宽度 */
.member-detail-info {
  max-width: 250px;
}

/* 审核记录容器样式 */
.audit-records-container {
  max-height: 300px;
  overflow-y: auto;
  padding: 0 5px;
}

/* 滚动条样式优化 */
.audit-records-container::-webkit-scrollbar {
  width: 6px;
}

.audit-records-container::-webkit-scrollbar-thumb {
  background-color: #dcdfe6;
  border-radius: 3px;
}

.audit-records-container::-webkit-scrollbar-track {
  background-color: #f5f7fa;
}


</style>