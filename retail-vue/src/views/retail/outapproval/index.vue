<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryRef" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="订单号" prop="orderNumber">
        <el-input
          v-model="queryParams.orderNumber"
          placeholder="请输入订单号"
          clearable
          style="width: 200px"
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item label="客户" prop="customerName">
        <el-input
          v-model="queryParams.customerName"
          placeholder="请输入客户姓名"
          clearable
          style="width: 200px"
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item label="电话" prop="customerPhone">
        <el-input
          v-model="queryParams.customerPhone"
          placeholder="请输入电话号码"
          clearable
          style="width: 200px"
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item label="门店" prop="storeName">
        <el-input
          v-model="queryParams.storeName"
          placeholder="请输入门店名称"
          clearable
          style="width: 200px"
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <!-- 已移除状态筛选，因为我们使用了专门的 API 端点只返回待审核和退货申请中的订单 -->
      <el-form-item label="日期" prop="createTime">
        <el-date-picker
          v-model="queryParams.createTime"
          type="daterange"
          range-separator="至"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          style="width: 240px"
        />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
        <el-button icon="Refresh" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="Download"
          @click="handleExport"
          v-hasPermi="['retail:outorder:export']"
        >导出</el-button>
      </el-col>
      <right-toolbar :model-value="showSearch" @update:modelValue="showSearch = $event" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="outOrderList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="订单单号" align="center" prop="orderNumber" />
      <el-table-column label="订单类型" align="center" prop="orderType" width="80">
        <template #default="scope">
          <el-tag type="success" v-if="scope.row.orderType === 0">售出单</el-tag>
          <el-tag type="warning" v-else-if="scope.row.orderType === 1">预订单</el-tag>
        </template>
      </el-table-column>
      <el-table-column label="业务场景" align="center" prop="businessType" width="100">
        <template #default="scope">
          <el-tag type="success" v-if="scope.row.businessType === 0">售出</el-tag>
          <el-tag type="warning" v-else-if="scope.row.businessType === 1">预订</el-tag>
          <el-tag type="danger" v-else-if="scope.row.businessType === 2">退货</el-tag>
          <el-tag type="info" v-else-if="scope.row.businessType === 3">取消</el-tag>
          <el-tag type="primary" v-else-if="scope.row.businessType === 4">交货</el-tag>
        </template>
      </el-table-column>
      <el-table-column label="会员信息" align="center" width="180">
        <template #default="scope">
          <div v-if="scope.row.memberId" class="member-info-clean">
            <div class="member-name-row">{{ scope.row.memberName }}</div>
            <div class="member-phone-row">{{ scope.row.phoneNumber }}</div>
          </div>
          <div v-else class="member-info-clean">
            <div class="member-name-row">{{ scope.row.customerName }}</div>
            <div class="member-phone-row">{{ scope.row.customerPhone }}</div>
          </div>
        </template>
      </el-table-column>
      <el-table-column label="门店名称" align="center" prop="storeName" />
      <el-table-column label="订单总金额" align="center" prop="totalAmount">
        <template #default="scope">
          {{ scope.row.totalAmount }} 元
        </template>
      </el-table-column>
      <el-table-column label="支付方式" align="center" prop="payType">
        <template #default="scope">
          {{ getPayTypeName(scope.row.payType) }}
        </template>
      </el-table-column>
      <el-table-column label="流程状态" align="center" prop="processStatus" width="100">
        <template #default="scope">
          <el-tag type="primary" v-if="scope.row.processStatus === 0">待审核</el-tag>
          <el-tag type="success" v-if="scope.row.processStatus === 1">已审核</el-tag>
          <el-tag type="danger" v-if="scope.row.processStatus === 2">已驳回</el-tag>
          <el-tag type="warning" v-if="scope.row.processStatus === 3">已撤销</el-tag>
        </template>
      </el-table-column>
      <el-table-column label="创建日期" align="center" prop="createTime" width="180">
        <template #default="scope">
          <span>{{ parseTime(scope.row.createTime) }}</span>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template #default="scope">
          <el-button link type="primary" icon="View" @click="handleDetail(scope.row)" v-hasPermi="['retail:outorder:query']">查看</el-button>
          <!-- 售出/预订业务，待审核状态 -->
          <el-button link type="success" icon="Check" @click="handleApprove(scope.row)" v-hasPermi="['retail:outorder:approve']" v-if="(scope.row.businessType === 0 || scope.row.businessType === 1) && scope.row.processStatus === 0">通过</el-button>
          <el-button link type="danger" icon="Close" @click="handleReject(scope.row)" v-hasPermi="['retail:outorder:reject']" v-if="(scope.row.businessType === 0 || scope.row.businessType === 1) && scope.row.processStatus === 0">驳回</el-button>
          <el-button link type="warning" icon="CircleClose" @click="handleCancel(scope.row)" v-hasPermi="['retail:outorder:cancel']" v-if="(scope.row.businessType === 0 || scope.row.businessType === 1) && scope.row.processStatus === 0 && isCreatedByCurrentUser(scope.row)">撤销</el-button>
          <!-- 退货业务，待审核状态 -->
          <el-button link type="success" icon="Check" @click="handleApproveReturn(scope.row)" v-hasPermi="['retail:outorder:approve']" v-if="scope.row.businessType === 2 && scope.row.processStatus === 0">通过退货</el-button>
          <el-button link type="danger" icon="Close" @click="handleRejectReturn(scope.row)" v-hasPermi="['retail:outorder:reject']" v-if="scope.row.businessType === 2 && scope.row.processStatus === 0">驳回退货</el-button>
          <el-button link type="warning" icon="CircleClose" @click="handleCancelReturn(scope.row)" v-hasPermi="['retail:outorder:cancel']" v-if="scope.row.businessType === 2 && scope.row.processStatus === 0 && isCreatedByCurrentUser(scope.row)">撤销退货</el-button>
          <!-- 取消业务，待审核状态 -->
          <el-button link type="success" icon="Check" @click="handleApproveCancel(scope.row)" v-hasPermi="['retail:outorder:approve']" v-if="scope.row.businessType === 3 && scope.row.processStatus === 0">通过取消</el-button>
          <el-button link type="danger" icon="Close" @click="handleRejectCancel(scope.row)" v-hasPermi="['retail:outorder:reject']" v-if="scope.row.businessType === 3 && scope.row.processStatus === 0">驳回取消</el-button>
          <!-- 交货业务，待审核状态 -->
          <el-button link type="success" icon="Check" @click="handleApproveDeliver(scope.row)" v-hasPermi="['retail:outorder:approve']" v-if="scope.row.businessType === 4 && scope.row.processStatus === 0">通过交货</el-button>
          <el-button link type="danger" icon="Close" @click="handleRejectDeliver(scope.row)" v-hasPermi="['retail:outorder:reject']" v-if="scope.row.businessType === 4 && scope.row.processStatus === 0">驳回交货</el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total>0"
      :total="total"
      :page="queryParams.pageNum"
      :limit="queryParams.pageSize"
      @update:page="queryParams.pageNum = $event"
      @update:limit="queryParams.pageSize = $event"
      @pagination="getList"
    />

    <!-- 订单详情对话框 -->
    <el-dialog title="订单详情" v-model="detailOpen" width="900px" append-to-body destroy-on-close>
      <template v-if="detailOpen">
        <el-descriptions :column="2" border :column-width="['50%', '50%']">
          <el-descriptions-item label="订单单号">{{ detail.orderNumber }}</el-descriptions-item>
          <el-descriptions-item label="订单类型">
            <el-tag type="success" v-if="detail.orderType === 0">售出单</el-tag>
            <el-tag type="warning" v-else-if="detail.orderType === 1">预订单</el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="会员信息" v-if="detail.memberId">
            <div class="member-detail-info">
              <div class="member-header">
                <el-tag :type="getMemberLevelTag(detail.memberLevel)">
                  {{ getMemberLevelName(detail.memberLevel) }}
                </el-tag>
                <span class="member-id">ID: {{ detail.memberId }}</span>
              </div>
              <div class="member-body">
                <div class="member-row-vertical">
                  <div class="member-item-vertical">
                    <span class="label">姓名:</span>
                    <span class="value">{{ detail.memberName }}</span>
                  </div>
                  <div class="member-item-vertical">
                    <span class="label">手机号:</span>
                    <span class="value">{{ detail.phoneNumber }}</span>
                  </div>
                  <div class="member-item-vertical">
                    <span class="label">性别:</span>
                    <span class="value">{{ detail.gender === '1' ? '男' : '女' }}</span>
                  </div>
                </div>
              </div>
            </div>
          </el-descriptions-item>
          <el-descriptions-item label="客户信息" v-else>
            <div class="member-body">
              <div class="member-row-vertical">
                <div class="member-item-vertical">
                  <span class="label">姓名:</span>
                  <span class="value">{{ detail.customerName }}</span>
                </div>
                <div class="member-item-vertical">
                  <span class="label">手机号:</span>
                  <span class="value">{{ detail.customerPhone }}</span>
                </div>
                <div class="member-item-vertical">
                  <span class="label">性别:</span>
                  <span class="value">{{ detail.customerGender === 1 ? '男' : '女' }}</span>
                </div>
              </div>
            </div>
          </el-descriptions-item>
          <el-descriptions-item label="门店名称">{{ detail.storeName }}</el-descriptions-item>
          <el-descriptions-item label="订单总金额">{{ detail.totalAmount }} 元</el-descriptions-item>
          <el-descriptions-item label="支付方式">{{ getPayTypeName(detail.payType) }}</el-descriptions-item>

          <!-- 预订单特有字段 -->
          <template v-if="detail.orderType === 1">
            <el-descriptions-item label="预订支付类型">
              <el-tag type="warning" v-if="detail.depositType === 0">定金</el-tag>
              <el-tag type="success" v-else-if="detail.depositType === 1">全款</el-tag>
            </el-descriptions-item>
            <el-descriptions-item label="定金金额" v-if="detail.depositType === 0">{{ detail.depositAmount }} 元</el-descriptions-item>
          </template>

          <el-descriptions-item label="业务场景">
            <el-tag type="success" v-if="detail.businessType === 0">售出</el-tag>
            <el-tag type="warning" v-else-if="detail.businessType === 1">预订</el-tag>
            <el-tag type="danger" v-else-if="detail.businessType === 2">退货</el-tag>
            <el-tag type="info" v-else-if="detail.businessType === 3">取消</el-tag>
            <el-tag type="primary" v-else-if="detail.businessType === 4">交货</el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="流程状态">
            <el-tag type="primary" v-if="detail.processStatus === 0">待审核</el-tag>
            <el-tag type="success" v-else-if="detail.processStatus === 1">已审核</el-tag>
            <el-tag type="danger" v-else-if="detail.processStatus === 2">已驳回</el-tag>
            <el-tag type="warning" v-else-if="detail.processStatus === 3">已撤销</el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="创建日期">{{ parseTime(detail.createTime) }}</el-descriptions-item>
          <el-descriptions-item label="备注" v-if="detail.remark" :span="2">{{ detail.remark }}</el-descriptions-item>
        </el-descriptions>

        <el-divider content-position="center">订单明细</el-divider>
        <el-table :data="detailListRef">
          <el-table-column label="序号" type="index" width="50" />
          <el-table-column label="商品图" width="80">
            <template #default="scope">
              <image-preview :src="scope.row.productImage" :width="50" :height="50"/>
            </template>
          </el-table-column>
          <el-table-column label="商品" prop="productName" show-overflow-tooltip />
          <el-table-column label="货号" prop="productNumber" show-overflow-tooltip />
          <el-table-column label="柜台" prop="counterName" />
          <el-table-column label="渠道" prop="channelName" />
          <el-table-column label="数量" prop="quantity" />
          <el-table-column label="销售价" prop="salePrice">
            <template #default="scope">
              {{ scope.row.salePrice }} 元
            </template>
          </el-table-column>
          <el-table-column label="批次号" prop="batchNumber" show-overflow-tooltip />
          <el-table-column label="备注" prop="remark" show-overflow-tooltip />
        </el-table>

        <el-divider content-position="center" v-if="auditListRef && auditListRef.length > 0">
          <el-icon><Histogram /></el-icon> 审核记录
        </el-divider>
        <div v-if="auditListRef && auditListRef.length > 0" class="audit-records-container">
          <el-timeline>
            <el-timeline-item
              v-for="(activity, index) in auditListRef"
              :key="index"
              :type="getAuditItemType(activity.businessType, activity.processStatus)"
              :color="getAuditItemColor(activity.businessType, activity.processStatus)"
              :timestamp="parseTime(activity.operateTime)"
            >
              <el-card>
                <template #header>
                  <div style="display: flex; justify-content: space-between; align-items: center;">
                    <el-tag :type="getAuditTagType(activity.businessType, activity.processStatus)">
                      {{ getAuditActionName(activity.businessType, activity.processStatus) }}
                    </el-tag>
                    <div style="display: flex; align-items: center;">
                      <el-avatar :size="24" :src="getAvatarUrl(activity.operator)" style="margin-right: 5px;">{{ getAvatarText(activity.operator) }}</el-avatar>
                      <span>{{ activity.operator }}</span>
                    </div>
                  </div>
                </template>
                <div v-if="activity.reason">
                  <el-alert
                    :title="activity.reason"
                    :type="getAlertType(activity.businessType, activity.processStatus)"
                    :closable="false"
                    show-icon
                  />
                </div>
                <div v-else-if="activity.processStatus !== 0" style="text-align: center; color: #909399; font-size: 12px;">
                  无备注信息
                </div>
              </el-card>
            </el-timeline-item>
          </el-timeline>
        </div>
      </template>

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="detailOpen = false">关 闭</el-button>
          <!-- 售出/预订业务，待审核状态 -->
          <el-button type="success" @click="handleApprove(detail)" v-if="(detail.businessType === 0 || detail.businessType === 1) && detail.processStatus === 0" v-hasPermi="['retail:outorder:approve']">审核通过</el-button>
          <el-button type="danger" @click="handleReject(detail)" v-if="(detail.businessType === 0 || detail.businessType === 1) && detail.processStatus === 0" v-hasPermi="['retail:outorder:approve']">驳回</el-button>
          <el-button type="warning" @click="handleCancel(detail)" v-if="(detail.businessType === 0 || detail.businessType === 1) && detail.processStatus === 0 && isCreatedByCurrentUser(detail)" v-hasPermi="['retail:outorder:cancel']">撤销</el-button>
          <!-- 退货业务，待审核状态 -->
          <el-button type="success" @click="handleApproveReturn(detail)" v-if="detail.businessType === 2 && detail.processStatus === 0" v-hasPermi="['retail:outorder:approve']">通过退货</el-button>
          <el-button type="danger" @click="handleRejectReturn(detail)" v-if="detail.businessType === 2 && detail.processStatus === 0" v-hasPermi="['retail:outorder:reject']">驳回退货</el-button>
          <el-button type="warning" @click="handleCancelReturn(detail)" v-if="detail.businessType === 2 && detail.processStatus === 0 && isCreatedByCurrentUser(detail)" v-hasPermi="['retail:outorder:cancel']">撤销退货</el-button>
          <!-- 取消业务，待审核状态 -->
          <el-button type="success" @click="handleApproveCancel(detail)" v-if="detail.businessType === 3 && detail.processStatus === 0" v-hasPermi="['retail:outorder:approve']">通过取消</el-button>
          <el-button type="danger" @click="handleRejectCancel(detail)" v-if="detail.businessType === 3 && detail.processStatus === 0" v-hasPermi="['retail:outorder:reject']">驳回取消</el-button>
          <!-- 交货业务，待审核状态 -->
          <el-button type="success" @click="handleApproveDeliver(detail)" v-if="detail.businessType === 4 && detail.processStatus === 0" v-hasPermi="['retail:outorder:approve']">通过交货</el-button>
          <el-button type="danger" @click="handleRejectDeliver(detail)" v-if="detail.businessType === 4 && detail.processStatus === 0" v-hasPermi="['retail:outorder:reject']">驳回交货</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 审核对话框 -->
    <el-dialog :title="approveTitle" v-model="approveOpen" width="500px" append-to-body>
      <el-form ref="approveFormRef" :model="approveForm" :rules="approveRules">
        <el-form-item label="审核意见" prop="reason" :rules="[{ required: approveForm.action === 2 || approveForm.action === 3 || approveForm.action === 7 || approveForm.action === 8, message: '驳回/撤销时必须填写理由', trigger: 'blur' }]">
          <el-input v-model="approveForm.reason" type="textarea" placeholder="请输入审核意见" :rows="4" />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitApprove">确 定</el-button>
          <el-button @click="approveOpen = false">取 消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="OutApproval">
import { ref, reactive, toRefs, onMounted, getCurrentInstance, computed } from 'vue';
import { listApprovalOutOrder, getOutOrder, approveOutOrder, rejectOutOrder, cancelOutOrder, approveReturnOutOrder, rejectReturnOutOrder, cancelReturnOutOrder, approveCancelReserveOrder, rejectCancelReserveOrder, approveDeliverReserveOrder, rejectDeliverReserveOrder } from "@/api/retail/inventory";
import { listAllMemberLevel } from "@/api/member/index";
import useUserStore from '@/store/modules/user';
import { Histogram } from '@element-plus/icons-vue';

const { proxy } = getCurrentInstance();
const userStore = useUserStore();
const userName = userStore.name; // 获取当前登录用户名
const { retail_pay_type } = proxy.useDict("retail_pay_type");

const outOrderList = ref([]);
const detailOpen = ref(false);
const loading = ref(true);
const showSearch = ref(true);
const ids = ref([]);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);
const detail = ref({});
const approveOpen = ref(false);
const approveTitle = ref("");
const memberLevels = ref([]); // 会员等级列表
const approveForm = ref({
  orderId: null,
  action: 1, // 默认通过
  reason: ""
});

// 审核规则
const approveRules = {
  reason: [
    { required: false, message: "审核通过可以不填写理由", trigger: "blur" }
  ]
};

const data = reactive({
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    orderNumber: null,
    customerName: null,
    customerPhone: null,
    storeName: null,
    // 不需要设置 status，因为我们使用了专门的 API 端点
    createTime: null
  }
});

const { queryParams } = toRefs(data);

const detailListRef = computed(() => detail.value.detailList || []);
const auditListRef = computed(() => detail.value.auditList || []);

/** 查询订单列表 */
function getList() {
  loading.value = true;
  let params = {...queryParams.value};

  // 处理日期范围
  if (params.createTime && params.createTime.length === 2) {
    params.beginTime = params.createTime[0];
    params.endTime = params.createTime[1];
  }
  delete params.createTime;

  listApprovalOutOrder(params).then(response => {
    outOrderList.value = response.rows;
    total.value = response.total;
    loading.value = false;
  });
}

/** 搜索按钮操作 */
function handleQuery() {
  queryParams.value.pageNum = 1;
  getList();
}

/** 重置按钮操作 */
function resetQuery() {
  proxy.resetForm("queryRef");
  queryParams.value.createTime = null;
  handleQuery();
}

// 多选框选中数据
function handleSelectionChange(selection) {
  ids.value = selection.map(item => item.id);
  single.value = selection.length != 1;
  multiple.value = !selection.length;
}

/** 查看详情按钮操作 */
function handleDetail(row) {
  // 防止重复点击或无效点击
  if (!row || !row.id) return;

  // 先重置对话框状态，确保DOM正确卸载
  detailOpen.value = false;
  detail.value = {};

  // 使用setTimeout确保上一个对话框完全关闭
  setTimeout(() => {
    getOutOrder(row.id).then(response => {
      if (response && response.data) {
        detail.value = response.data;
        // 确保明细和审核记录数据为数组
        if (!detail.value.detailList) detail.value.detailList = [];
        if (!detail.value.auditList) detail.value.auditList = [];
        // 设置对话框为打开状态
        detailOpen.value = true;
      }
    }).catch(error => {
      console.error("获取订单详情失败:", error);
    });
  }, 100);
}

/** 导出按钮操作 */
function handleExport() {
  proxy.download('retail/outorder/export', {
    ...queryParams.value
  }, `outorder_${new Date().getTime()}.xlsx`)
}

/** 审核通过按钮操作 */
function handleApprove(row) {
  approveForm.value = {
    orderId: row.id,
    businessType: row.businessType,  // 保持原业务类型
    processStatus: 1,  // 已审核
    reason: ""
  };
  approveTitle.value = "审核通过订单";
  approveOpen.value = true;
}

/** 驳回按钮操作 */
function handleReject(row) {
  approveForm.value = {
    orderId: row.id,
    businessType: row.businessType,  // 保持原业务类型
    processStatus: 2,  // 已驳回
    reason: ""
  };
  approveTitle.value = "驳回订单";
  approveOpen.value = true;
}

/** 撤销按钮操作 */
function handleCancel(row) {
  // 检查是否是自己创建的订单
  if (!isCreatedByCurrentUser(row)) {
    proxy.$modal.msgError("只有申请人本人才能撤销订单");
    return;
  }

  approveForm.value = {
    orderId: row.id,
    businessType: row.businessType,  // 保持原业务类型
    processStatus: 3,  // 已撤销
    reason: ""
  };
  approveTitle.value = "撤销订单";
  approveOpen.value = true;
}

/** 通过退货按钮操作 */
function handleApproveReturn(row) {
  approveForm.value = {
    orderId: row.id,
    businessType: 2,  // 退货业务
    processStatus: 1,  // 已审核
    reason: ""
  };
  approveTitle.value = "通过退货申请";
  approveOpen.value = true;
}

/** 驳回退货按钮操作 */
function handleRejectReturn(row) {
  approveForm.value = {
    orderId: row.id,
    businessType: 2,  // 退货业务
    processStatus: 2,  // 已驳回
    reason: ""
  };
  approveTitle.value = "驳回退货申请";
  approveOpen.value = true;
}

/** 撤销退货申请按钮操作 */
function handleCancelReturn(row) {
  // 检查是否是自己创建的订单
  if (!isCreatedByCurrentUser(row)) {
    proxy.$modal.msgError("只有申请人才能撤销退货申请");
    return;
  }

  approveForm.value = {
    orderId: row.id,
    businessType: 2,  // 退货业务
    processStatus: 3,  // 已撤销
    reason: ""
  };
  approveTitle.value = "撤销退货申请";
  approveOpen.value = true;
}

/** 通过取消预订单申请按钮操作 */
function handleApproveCancel(row) {
  approveForm.value = {
    orderId: row.id,
    businessType: 3,  // 取消业务
    processStatus: 1,  // 已审核
    reason: ""
  };
  approveTitle.value = "通过取消预订单申请";
  approveOpen.value = true;
}

/** 驳回取消预订单申请按钮操作 */
function handleRejectCancel(row) {
  approveForm.value = {
    orderId: row.id,
    businessType: 3,  // 取消业务
    processStatus: 2,  // 已驳回
    reason: ""
  };
  approveTitle.value = "驳回取消预订单申请";
  approveOpen.value = true;
}

/** 通过交货申请按钮操作 */
function handleApproveDeliver(row) {
  approveForm.value = {
    orderId: row.id,
    businessType: 4,  // 交货业务
    processStatus: 1,  // 已审核
    reason: ""
  };
  approveTitle.value = "通过交货申请";
  approveOpen.value = true;
}

/** 驳回交货申请按钮操作 */
function handleRejectDeliver(row) {
  approveForm.value = {
    orderId: row.id,
    businessType: 4,  // 交货业务
    processStatus: 2,  // 已驳回
    reason: ""
  };
  approveTitle.value = "驳回交货申请";
  approveOpen.value = true;
}

/** 提交审核/驳回/撤销 */
function submitApprove() {
  // 如果是驳回、撤销或退货相关操作，必须填写理由
  const needsReason = (
    approveForm.value.processStatus === 2 || // 驳回
    approveForm.value.processStatus === 3 || // 撤销
    (approveForm.value.businessType === 2 && approveForm.value.processStatus === 2) || // 退货驳回
    (approveForm.value.businessType === 2 && approveForm.value.processStatus === 3)    // 撤销退货
  );

  if (needsReason && !approveForm.value.reason) {
    let errorMsg = "理由不能为空";
    if (approveForm.value.processStatus === 2) {
      errorMsg = "驳回时必须填写理由";
    } else if (approveForm.value.processStatus === 3) {
      errorMsg = "撤销时必须填写理由";
    } else if (approveForm.value.businessType === 2 && approveForm.value.processStatus === 2) {
      errorMsg = "退货驳回时必须填写理由";
    } else if (approveForm.value.businessType === 2 && approveForm.value.processStatus === 3) {
      errorMsg = "撤销退货申请时必须填写理由";
    }
    proxy.$modal.msgError(errorMsg);
    return;
  }

  // 审核通过
  if (approveForm.value.processStatus === 1) {
    // 退货审核通过
    if (approveForm.value.businessType === 2) {
      // 构造与后端接口匹配的参数
      const params = {
        id: approveForm.value.orderId,
        remark: approveForm.value.reason
      };

      // 退货审核通过
      approveReturnOutOrder(params).then(() => {
        proxy.$modal.msgSuccess("退货审核通过成功");
        approveOpen.value = false;
        getList();
        if (detailOpen.value) {
          handleDetail({id: approveForm.value.orderId});
        }
      }).catch(error => {
        proxy.$modal.msgError(error.message || "退货审核通过失败");
      });
    }
    // 取消预订单审核通过
    else if (approveForm.value.businessType === 3) {
      // 构造与后端接口匹配的参数
      const params = {
        id: approveForm.value.orderId,
        remark: approveForm.value.reason
      };

      // 取消预订单审核通过
      approveCancelReserveOrder(params).then(() => {
        proxy.$modal.msgSuccess("取消预订单审核通过成功");
        approveOpen.value = false;
        getList();
        if (detailOpen.value) {
          handleDetail({id: approveForm.value.orderId});
        }
      }).catch(error => {
        proxy.$modal.msgError(error.message || "取消预订单审核通过失败");
      });
    }
    // 交货审核通过
    else if (approveForm.value.businessType === 4) {
      // 构造与后端接口匹配的参数
      const params = {
        id: approveForm.value.orderId,
        remark: approveForm.value.reason
      };

      // 交货审核通过
      approveDeliverReserveOrder(params).then(() => {
        proxy.$modal.msgSuccess("交货审核通过成功");
        approveOpen.value = false;
        getList();
        if (detailOpen.value) {
          handleDetail({id: approveForm.value.orderId});
        }
      }).catch(error => {
        proxy.$modal.msgError(error.message || "交货审核通过失败");
      });
    }
    // 普通审核通过
    else {
      // 构造与后端接口匹配的参数
      const params = {
        id: approveForm.value.orderId,
        remark: approveForm.value.reason
      };

      // 审核通过
      approveOutOrder(params).then(() => {
        proxy.$modal.msgSuccess("审核通过成功");
        approveOpen.value = false;
        getList();
        if (detailOpen.value) {
          handleDetail({id: approveForm.value.orderId});
        }
      });
    }
  }
  // 驳回
  else if (approveForm.value.processStatus === 2) {
    // 退货驳回
    if (approveForm.value.businessType === 2) {
      // 构造与后端接口匹配的参数
      const params = {
        id: approveForm.value.orderId,
        rejectReason: approveForm.value.reason
      };

      // 退货审核驳回
      rejectReturnOutOrder(params).then(() => {
        proxy.$modal.msgSuccess("退货审核驳回成功");
        approveOpen.value = false;
        getList();
        if (detailOpen.value) {
          handleDetail({id: approveForm.value.orderId});
        }
      }).catch(error => {
        proxy.$modal.msgError(error.message || "退货审核驳回失败");
      });
    }
    // 取消预订单驳回
    else if (approveForm.value.businessType === 3) {
      // 构造与后端接口匹配的参数
      const params = {
        id: approveForm.value.orderId,
        rejectReason: approveForm.value.reason
      };

      // 取消预订单驳回
      rejectCancelReserveOrder(params).then(() => {
        proxy.$modal.msgSuccess("取消预订单驳回成功");
        approveOpen.value = false;
        getList();
        if (detailOpen.value) {
          handleDetail({id: approveForm.value.orderId});
        }
      }).catch(error => {
        proxy.$modal.msgError(error.message || "取消预订单驳回失败");
      });
    }
    // 交货驳回
    else if (approveForm.value.businessType === 4) {
      // 构造与后端接口匹配的参数
      const params = {
        id: approveForm.value.orderId,
        rejectReason: approveForm.value.reason
      };

      // 交货驳回
      rejectDeliverReserveOrder(params).then(() => {
        proxy.$modal.msgSuccess("交货驳回成功");
        approveOpen.value = false;
        getList();
        if (detailOpen.value) {
          handleDetail({id: approveForm.value.orderId});
        }
      }).catch(error => {
        proxy.$modal.msgError(error.message || "交货驳回失败");
      });
    }
    // 普通驳回
    else {
      // 构造与后端接口匹配的参数
      const params = {
        id: approveForm.value.orderId,
        rejectReason: approveForm.value.reason
      };

      // 驳回
      rejectOutOrder(params).then(() => {
        proxy.$modal.msgSuccess("驳回成功");
        approveOpen.value = false;
        getList();
        if (detailOpen.value) {
          handleDetail({id: approveForm.value.orderId});
        }
      });
    }
  }
  // 撤销
  else if (approveForm.value.processStatus === 3) {
    // 撤销退货申请
    if (approveForm.value.businessType === 2) {
      // 再次检查当前用户是否为订单创建人
      getOutOrder(approveForm.value.orderId).then(response => {
        const orderData = response.data;
        if (!isCreatedByCurrentUser(orderData)) {
          proxy.$modal.msgError("只有申请人才能撤销退货申请");
          return;
        }

        // 构造与后端接口匹配的参数
        const params = {
          id: approveForm.value.orderId,
          cancelReason: approveForm.value.reason
        };

        cancelReturnOutOrder(params).then(() => {
          proxy.$modal.msgSuccess("退货申请撤销成功");
          approveOpen.value = false;
          getList();
          if (detailOpen.value) {
            handleDetail({id: approveForm.value.orderId});
          }
        }).catch(error => {
          proxy.$modal.msgError(error.message || "退货申请撤销失败");
        });
      });
    }
    // 普通撤销
    else {
      // 再次检查当前用户是否为订单创建人
      getOutOrder(approveForm.value.orderId).then(response => {
        const orderData = response.data;
        if (!isCreatedByCurrentUser(orderData)) {
          proxy.$modal.msgError("只有申请人本人才能撤销订单");
          return;
        }

        // 构造与后端接口匹配的参数
        const params = {
          id: approveForm.value.orderId,
          cancelReason: approveForm.value.reason
        };

        cancelOutOrder(params).then(() => {
          proxy.$modal.msgSuccess("撤销成功");
          approveOpen.value = false;
          getList();
          if (detailOpen.value) {
            handleDetail({id: approveForm.value.orderId});
          }
        }).catch(error => {
          proxy.$modal.msgError(error.message || "撤销操作失败");
        });
      });
    }
  }
}

/** 获取审核操作类型名称 */
function getAuditActionName(businessType, processStatus) {
  // 使用新的 businessType 和 processStatus 参数
  if (businessType === 0) { // 售出业务
    if (processStatus === 0) return "提交售出审核";
    if (processStatus === 1) return "售出审核通过";
    if (processStatus === 2) return "售出审核驳回";
    if (processStatus === 3) return "售出订单撤销";
  } else if (businessType === 1) { // 预订业务
    if (processStatus === 0) return "提交预订审核";
    if (processStatus === 1) return "预订审核通过";
    if (processStatus === 2) return "预订审核驳回";
    if (processStatus === 3) return "预订订单撤销";
  } else if (businessType === 2) { // 退货业务
    if (processStatus === 0) return "申请退货";
    if (processStatus === 1) return "退货审核通过";
    if (processStatus === 2) return "退货审核驳回";
    if (processStatus === 3) return "退货已撤销";
  } else if (businessType === 3) { // 取消业务
    if (processStatus === 0) return "申请取消";
    if (processStatus === 1) return "取消审核通过";
    if (processStatus === 2) return "取消审核驳回";
    if (processStatus === 3) return "取消申请撤销";
  } else if (businessType === 4) { // 交货业务
    if (processStatus === 0) return "申请交货";
    if (processStatus === 1) return "交货审核通过";
    if (processStatus === 2) return "交货审核驳回";
    if (processStatus === 3) return "交货申请撤销";
  }

  return "未知操作";
}

/** 获取审核时间线项目类型 */
function getAuditItemType(_, processStatus) {
  // 使用新的 processStatus 参数
  // 根据流程状态返回类型
  if (processStatus === 0) return "primary";  // 待审核
  if (processStatus === 1) return "success";  // 已审核
  if (processStatus === 2) return "danger";   // 已驳回
  if (processStatus === 3) return "warning";  // 已撤销

  return "info";
}

/** 获取审核时间线项目颜色 */
function getAuditItemColor(_, processStatus) {
  // 使用新的 processStatus 参数
  // 根据流程状态返回颜色
  if (processStatus === 0) return "#409EFF";  // 待审核 - 蓝色
  if (processStatus === 1) return "#67C23A";  // 已审核 - 绿色
  if (processStatus === 2) return "#F56C6C";  // 已驳回 - 红色
  if (processStatus === 3) return "#E6A23C";  // 已撤销 - 黄色

  return "#909399"; // 默认灰色
}

/** 获取Alert类型 */
function getAlertType(_, processStatus) {
  // 使用新的 processStatus 参数
  // 根据流程状态返回 Alert 类型
  if (processStatus === 0) return "info";     // 待审核
  if (processStatus === 1) return "success";  // 已审核
  if (processStatus === 2) return "error";    // 已驳回
  if (processStatus === 3) return "warning";  // 已撤销

  return "info"; // 默认信息类型
}

/** 获取支付方式名称 */
function getPayTypeName(payType) {
  if (!payType && payType !== 0) return '';
  const payTypeObj = retail_pay_type.value.find(item => Number(item.value) === payType);
  return payTypeObj ? payTypeObj.label : '未知支付方式';
}

// 判断是否是自己创建的订单
const isCreatedByCurrentUser = (row) => {
  return row.createBy === userName;
};

/** 获取会员等级标签类型 */
function getMemberLevelTag(level) {
  if (!level) return 'info';
  const tagMap = {
    1: 'info',    // 普通会员
    2: 'info',    // 银卡会员
    3: 'warning', // 金卡会员
    4: 'success', // VIP会员
    5: 'danger'   // 钻石会员
  };
  return tagMap[level] || 'info';
}

/** 获取会员等级名称 */
function getMemberLevelName(level) {
  if (!level) return '普通会员';
  const nameMap = {
    1: '普通会员',
    2: '银卡会员',
    3: '金卡会员',
    4: 'VIP会员',
    5: '钻石会员'
  };
  return nameMap[level] || '普通会员';
}

/** 获取会员等级列表 */
function getMemberLevels() {
  listAllMemberLevel().then(response => {
    memberLevels.value = response.data || [];
  });
}

/** 获取审核操作类型Tag类型 */
function getAuditTagType(_, processStatus) {
  // 使用新的 processStatus 参数
  // 根据流程状态返回 Tag 类型
  if (processStatus === 0) return "primary";  // 待审核
  if (processStatus === 1) return "success";  // 已审核
  if (processStatus === 2) return "danger";   // 已驳回
  if (processStatus === 3) return "warning";  // 已撤销

  return "info"; // 默认信息类型
}



/** 获取头像URL */
function getAvatarUrl() {
  // 这里可以根据实际情况返回用户头像URL
  return ''; // 返回空字符串会使用默认的文本头像
}

/** 获取头像文本 */
function getAvatarText(username) {
  if (!username) return '用';
  // 返回用户名的第一个字符作为头像文本
  return username.charAt(0);
}

onMounted(() => {
  getList();
  getMemberLevels(); // 获取会员等级列表
});
</script>

<style scoped>
.audit-timeline-container {
  padding: 15px 10px;
  background-color: #f9f9f9;
  border-radius: 4px;
  margin-bottom: 15px;
}

.audit-card {
  border-radius: 8px;
  transition: all 0.3s;
}

.audit-card :deep(.el-card__header) {
  padding: 10px;
}

.audit-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 12px rgba(0, 0, 0, 0.1);
}

.audit-card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.audit-card-operator {
  display: flex;
  align-items: center;
  color: #909399;
  font-size: 13px;
}

.mr5 {
  margin-right: 5px;
}

.audit-reason {
  margin-top: 10px;
}

.audit-no-reason {
  display: flex;
  justify-content: center;
  padding: 5px 0;
  color: #909399;
  font-size: 12px;
  font-style: italic;
}

.ml10 {
  margin-left: 10px;
}

.mt10 {
  margin-top: 10px;
}

/* 会员信息样式 */
.member-info-cell {
  display: flex;
  align-items: center;
  gap: 5px;
  margin-bottom: 3px;
}

.member-name {
  font-weight: bold;
  font-size: 14px;
}

.member-phone {
  color: #606266;
  font-size: 13px;
}

.member-info-clean {
  display: flex;
  flex-direction: column;
  gap: 3px;
  text-align: center;
  padding: 2px 0;
}

.member-name-row {
  font-weight: 500;
  font-size: 14px;
  color: #303133;
  line-height: 1.4;
}

.member-phone-row {
  font-size: 13px;
  color: #909399;
  line-height: 1.4;
}

/* 会员详情信息样式 */
.member-detail-info {
  border: 1px solid #ebeef5;
  border-radius: 4px;
  padding: 8px 10px;
  background-color: #f8f9fa;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.member-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
  padding-bottom: 8px;
  border-bottom: 1px solid #ebeef5;
}

.member-id {
  font-size: 12px;
  color: #909399;
}

.member-body {
  display: flex;
  flex-direction: column;
}

.member-row {
  display: flex;
  justify-content: space-between;
  flex-wrap: wrap;
  gap: 10px;
  align-items: center;
}

.member-item {
  display: flex;
  align-items: center;
  flex: 1;
  min-width: 120px;
}

.member-item .label {
  font-weight: bold;
  width: 60px;
  color: #606266;
  font-size: 13px;
}

.member-item .value {
  color: #303133;
  font-size: 13px;
}

/* 垂直布局的会员信息样式 */
.member-row-vertical {
  display: flex;
  flex-direction: column;
  gap: 5px;
}

.member-item-vertical {
  display: flex;
  align-items: center;
  margin-bottom: 3px;
}

.member-item-vertical .label {
  font-weight: bold;
  width: 60px;
  color: #606266;
  font-size: 13px;
}

.member-item-vertical .value {
  color: #303133;
  font-size: 13px;
}

/* 调整会员信息区域宽度 */
.member-detail-info {
  max-width: 250px;
}

/* 审核记录容器样式 */
.audit-records-container {
  max-height: 300px;
  overflow-y: auto;
  padding: 0 5px;
}

/* 滚动条样式优化 */
.audit-records-container::-webkit-scrollbar {
  width: 6px;
}

.audit-records-container::-webkit-scrollbar-thumb {
  background-color: #dcdfe6;
  border-radius: 3px;
}

.audit-records-container::-webkit-scrollbar-track {
  background-color: #f5f7fa;
}



</style>