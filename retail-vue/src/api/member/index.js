import request from '@/utils/request'

// 查询会员列表
export function listMember(query) {
  return request({
    url: '/member/list',
    method: 'get',
    params: query
  })
}

// 查询会员详细
export function getMember(memberId) {
  return request({
    url: '/member/' + memberId,
    method: 'get'
  })
}

// 新增会员
export function addMember(data) {
  return request({
    url: '/member',
    method: 'post',
    data: data
  })
}

// 修改会员
export function updateMember(data) {
  return request({
    url: '/member',
    method: 'put',
    data: data
  })
}

// 删除会员
export function delMember(memberId) {
  return request({
    url: '/member/' + memberId,
    method: 'delete'
  })
}

// 导出会员
export function exportMember(query) {
  return request({
    url: '/member/export',
    method: 'get',
    params: query
  })
}

// 查询会员等级列表
export function listMemberLevel(query) {
  return request({
    url: '/member/level/list',
    method: 'get',
    params: query
  })
}

// 查询会员等级详细
export function getMemberLevel(levelId) {
  return request({
    url: '/member/level/' + levelId,
    method: 'get'
  })
}

// 新增会员等级
export function addMemberLevel(data) {
  return request({
    url: '/member/level',
    method: 'post',
    data: data
  })
}

// 修改会员等级
export function updateMemberLevel(data) {
  return request({
    url: '/member/level',
    method: 'put',
    data: data
  })
}

// 删除会员等级
export function delMemberLevel(levelId) {
  return request({
    url: '/member/level/' + levelId,
    method: 'delete'
  })
}

// 导出会员等级
export function exportMemberLevel(query) {
  return request({
    url: '/member/level/export',
    method: 'get',
    params: query
  })
}

// 获取所有会员等级
export function listAllMemberLevel() {
  return request({
    url: '/member/level/list-all',
    method: 'get'
  })
}

// 根据手机号码搜索会员
export function searchMemberByPhone(phoneNumber) {
  return request({
    url: '/member/search',
    method: 'get',
    params: { phoneNumber }
  })
}

// 更新会员累计消费金额
export function updateMemberAmount(data) {
  return request({
    url: '/member/update-amount',
    method: 'put',
    data: data
  })
}