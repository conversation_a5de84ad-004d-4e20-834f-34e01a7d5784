import request from '@/utils/request'

// 获取销售统计数据
export function getSaleStatistics(query) {
  return request({
    url: '/analysis/sale/statistics',
    method: 'get',
    params: query
  })
}

// 获取销售趋势数据
export function getSaleTrend(query) {
  return request({
    url: '/analysis/sale/trend',
    method: 'get',
    params: query
  })
}

// 获取销售渠道分布数据
export function getSaleChannelDistribution(query) {
  return request({
    url: '/analysis/sale/channel',
    method: 'get',
    params: query
  })
}

// 获取销售门店排行数据
export function getSaleStoreRanking(query) {
  return request({
    url: '/analysis/sale/storeRanking',
    method: 'get',
    params: query
  })
}

// 获取销售商品排行数据
export function getSaleProductRanking(query) {
  return request({
    url: '/analysis/sale/productRanking',
    method: 'get',
    params: query
  })
}

// 获取客户性别分布数据
export function getCustomerGenderDistribution(query) {
  return request({
    url: '/analysis/sale/customerGender',
    method: 'get',
    params: query
  })
}

// 获取库存分析数据
export function getInventoryAnalysis(query) {
  return request({
    url: '/analysis/inventory/statistics',
    method: 'get',
    params: query
  })
}

// 获取利润分析数据
export function getProfitAnalysis(query) {
  return request({
    url: '/analysis/sale/profit',
    method: 'get',
    params: query
  })
}
