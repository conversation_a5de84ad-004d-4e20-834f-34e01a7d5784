import request from '@/utils/request'

// 查询入库单列表
export function listInOrder(query) {
  return request({
    url: '/retail/inorder/list',
    method: 'get',
    params: query
  })
}

// 查询入库单详细
export function getInOrder(id) {
  return request({
    url: '/retail/inorder/' + id,
    method: 'get'
  })
}

// 新增入库单
export function addInOrder(data) {
  return request({
    url: '/retail/inorder',
    method: 'post',
    data: data
  })
}

// 新增入库单及明细
export function saveInOrderWithDetails(data) {
  return request({
    url: '/retail/inorder/save',
    method: 'post',
    data: data
  })
}

// 修改入库单
export function updateInOrder(data) {
  return request({
    url: '/retail/inorder',
    method: 'put',
    data: data
  })
}

// 删除入库单
export function delInOrder(id) {
  return request({
    url: '/retail/inorder/' + id,
    method: 'delete'
  })
}

// 查询出库单列表
export function listOutOrder(query) {
  return request({
    url: '/retail/outorder/list',
    method: 'get',
    params: query
  })
}

// 查询待审核出库单列表
export function listApprovalOutOrder(query) {
  return request({
    url: '/retail/outorder/approval-list',
    method: 'get',
    params: query
  })
}

// 查询出库单详细
export function getOutOrder(id) {
  return request({
    url: '/retail/outorder/' + id,
    method: 'get'
  })
}

// 新增出库单
export function addOutOrder(data) {
  return request({
    url: '/retail/outorder',
    method: 'post',
    data: data
  })
}

// 新增出库单及明细
export function saveOutOrderWithDetails(data) {
  return request({
    url: '/retail/outorder/save',
    method: 'post',
    data: data
  })
}

// 修改出库单
export function updateOutOrder(data) {
  return request({
    url: '/retail/outorder',
    method: 'put',
    data: data
  })
}

// 删除出库单
export function delOutOrder(id) {
  return request({
    url: '/retail/outorder/' + id,
    method: 'delete'
  })
}

// 导出出库单
export function exportOutOrder(query) {
  return request({
    url: '/retail/outorder/export',
    method: 'get',
    params: query
  })
}

// 审核通过出库单
export function approveOutOrder(data) {
  return request({
    url: '/retail/outorder/approve',
    method: 'post',
    data: data
  })
}

// 驳回出库单
export function rejectOutOrder(data) {
  return request({
    url: '/retail/outorder/reject',
    method: 'post',
    data: data
  })
}

// 撤销出库单
export function cancelOutOrder(data) {
  return request({
    url: '/retail/outorder/cancel',
    method: 'post',
    data: data
  })
}

// 查询出库单审核记录
export function listOutOrderAudit(orderId) {
  return request({
    url: '/retail/outorder/audit/' + orderId,
    method: 'get'
  })
}

// 申请退货
export function applyReturnOutOrder(data) {
  return request({
    url: '/retail/outorder/apply-return',
    method: 'post',
    data: data
  })
}

// 审核通过退货申请
export function approveReturnOutOrder(data) {
  return request({
    url: '/retail/outorder/approve-return',
    method: 'post',
    data: data
  })
}

// 驳回退货申请
export function rejectReturnOutOrder(data) {
  return request({
    url: '/retail/outorder/reject-return',
    method: 'post',
    data: data
  })
}

// 撤销退货申请
export function cancelReturnOutOrder(data) {
  return request({
    url: '/retail/outorder/cancel-return',
    method: 'post',
    data: data
  })
}

// 审核通过取消预订单申请
export function approveCancelReserveOrder(data) {
  return request({
    url: '/retail/outorder/approve-cancel-reserve',
    method: 'post',
    data: data
  })
}

// 驳回取消预订单申请
export function rejectCancelReserveOrder(data) {
  return request({
    url: '/retail/outorder/reject-cancel-reserve',
    method: 'post',
    data: data
  })
}

// 审核通过交货申请
export function approveDeliverReserveOrder(data) {
  return request({
    url: '/retail/outorder/approve-deliver-reserve',
    method: 'post',
    data: data
  })
}

// 驳回交货申请
export function rejectDeliverReserveOrder(data) {
  return request({
    url: '/retail/outorder/reject-deliver-reserve',
    method: 'post',
    data: data
  })
}

// 查询库存列表
export function listStock(query) {
  return request({
    url: '/retail/stock/list',
    method: 'get',
    params: query
  })
}

// 查询库存详细
export function getStock(id) {
  return request({
    url: '/retail/stock/' + id,
    method: 'get'
  })
}

// 查询商品在指定位置的库存
export function getStockByLocation(productId, storeId, counterId, channelId) {
  return request({
    url: `/retail/stock/location/${productId}/${storeId}/${counterId}/${channelId}`,
    method: 'get'
  })
}

// 批量删除库存
export function batchDeleteStock(data) {
  return request({
    url: '/retail/stock/batch-delete',
    method: 'post',
    data: data
  })
}

// 批量迁移库存
export function batchMigrateStock(data) {
  return request({
    url: '/retail/stock/batch-migrate',
    method: 'post',
    data: data
  })
}