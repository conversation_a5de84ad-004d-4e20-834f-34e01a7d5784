import request from '@/utils/request'

// 获取当前用户所属门店
export function getUserStore() {
  return request({
    url: '/retail/store/current',
    method: 'get'
  })
}

// 查询门店管理列表
export function listStore(query) {
  return request({
    url: '/retail/store/list',
    method: 'get',
    params: query
  })
}

// 查询门店管理详细
export function getStore(id) {
  return request({
    url: '/retail/store/' + id,
    method: 'get'
  })
}

// 新增门店管理
export function addStore(data) {
  return request({
    url: '/retail/store',
    method: 'post',
    data: data
  })
}

// 修改门店管理
export function updateStore(data) {
  return request({
    url: '/retail/store',
    method: 'put',
    data: data
  })
}

// 删除门店管理
export function delStore(id) {
  return request({
    url: '/retail/store/' + id,
    method: 'delete'
  })
}

// 查询所有门店列表（不分页）
export function listAllStores(query) {
  return request({
    url: '/retail/store/all',
    method: 'get',
    params: query
  })
}

// 查询门店管理员用户列表
export function listStoreUsers() {
  return request({
    url: '/retail/store/storeUsers',
    method: 'get'
  })
}
