import request from '@/utils/request'

// 查询柜台管理列表
export function listCounter(query) {
  return request({
    url: '/retail/counter/list',
    method: 'get',
    params: query
  })
}

// 根据门店ID查询柜台列表
export function listCounterByStore(storeId) {
  return request({
    url: '/retail/counter/listByStore/' + storeId,
    method: 'get'
  })
}

// 查询柜台管理详细
export function getCounter(id) {
  return request({
    url: '/retail/counter/' + id,
    method: 'get'
  })
}

// 新增柜台管理
export function addCounter(data) {
  return request({
    url: '/retail/counter',
    method: 'post',
    data: data
  })
}

// 修改柜台管理
export function updateCounter(data) {
  return request({
    url: '/retail/counter',
    method: 'put',
    data: data
  })
}

// 删除柜台管理
export function delCounter(id) {
  return request({
    url: '/retail/counter/' + id,
    method: 'delete'
  })
}
