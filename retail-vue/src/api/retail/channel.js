import request from '@/utils/request'

// 查询渠道管理列表
export function listChannel(query) {
  return request({
    url: '/retail/channel/list',
    method: 'get',
    params: query
  })
}

// 查询渠道管理详细
export function getChannel(id) {
  return request({
    url: '/retail/channel/' + id,
    method: 'get'
  })
}

// 新增渠道管理
export function addChannel(data) {
  return request({
    url: '/retail/channel',
    method: 'post',
    data: data
  })
}

// 修改渠道管理
export function updateChannel(data) {
  return request({
    url: '/retail/channel',
    method: 'put',
    data: data
  })
}

// 删除渠道管理
export function delChannel(id) {
  return request({
    url: '/retail/channel/' + id,
    method: 'delete'
  })
}

// 查询所有渠道列表（不分页）
export function listAllChannels(query) {
  return request({
    url: '/retail/channel/all',
    method: 'get',
    params: query
  })
}

// 查询渠道管理员用户列表
export function listChannelUsers() {
  return request({
    url: '/retail/channel/channelUsers',
    method: 'get'
  })
}
