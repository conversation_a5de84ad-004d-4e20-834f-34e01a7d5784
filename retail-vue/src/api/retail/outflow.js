import request from '@/utils/request'

// 查询出库审核流程列表
export function listOutFlow(query) {
  return request({
    url: '/retail/outflow/list',
    method: 'get',
    params: query
  })
}

// 查询出库审核流程详细
export function getOutFlow(id) {
  return request({
    url: '/retail/outflow/' + id,
    method: 'get'
  })
}

// 新增出库审核流程
export function addOutFlow(data) {
  return request({
    url: '/retail/outflow',
    method: 'post',
    data: data
  })
}

// 修改出库审核流程
export function updateOutFlow(data) {
  return request({
    url: '/retail/outflow',
    method: 'put',
    data: data
  })
}

// 删除出库审核流程
export function delOutFlow(id) {
  return request({
    url: '/retail/outflow/' + id,
    method: 'delete'
  })
}

// 修改出库审核流程状态
export function changeFlowStatus(id, status) {
  const data = {
    id,
    status
  }
  return request({
    url: '/retail/outflow/changeStatus',
    method: 'put',
    data: data
  })
} 