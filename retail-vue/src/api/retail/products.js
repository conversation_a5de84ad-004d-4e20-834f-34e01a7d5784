import request from '@/utils/request'

// 查询商品列表
export function listProducts(query) {
  return request({
    url: '/retail/products/list',
    method: 'get',
    params: query
  })
}

// 查询商品详细
export function getProducts(id) {
  return request({
    url: '/retail/products/' + id,
    method: 'get'
  })
}

// 新增商品
export function addProducts(data) {
  return request({
    url: '/retail/products',
    method: 'post',
    data: data
  })
}

// 修改商品
export function updateProducts(data) {
  return request({
    url: '/retail/products',
    method: 'put',
    data: data
  })
}

// 删除商品
export function delProducts(id) {
  return request({
    url: '/retail/products/' + id,
    method: 'delete'
  })
}

// 查询所有商品列表（不分页）
export function listAllProducts(query) {
  return request({
    url: '/retail/products/all',
    method: 'get',
    params: query
  })
}
