import request from '@/utils/request'

// 单个商品预订
export function reserveProduct(data) {
  return request({
    url: '/retail/reserve/reserve',
    method: 'post',
    data: data
  })
}

// 批量商品预订
export function batchReserveProduct(data) {
  return request({
    url: '/retail/reserve/batch-reserve',
    method: 'post',
    data: data
  })
}

// 预订单交货
export function deliverReserveOrderDetail(data) {
  return request({
    url: '/retail/reserve/deliver-order',
    method: 'post',
    data: data
  })
}

// 撤销预订单
export function cancelReserveOrder(data) {
  return request({
    url: '/retail/reserve/cancel',
    method: 'post',
    data: data
  })
}
