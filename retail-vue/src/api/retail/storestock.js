import request from '@/utils/request'

// 查询门店库存列表
export function listStoreStock(query) {
  return request({
    url: '/retail/storestock/list',
    method: 'get',
    params: query
  })
}

// 查询合并后的门店库存列表（按产品合并）
export function listMergedStoreStock(query) {
  return request({
    url: '/retail/storestock/mergedList',
    method: 'get',
    params: query
  })
}

// 查询特定产品的详细库存
export function getProductStockDetails(productId, query) {
  return request({
    url: '/retail/storestock/product/' + productId,
    method: 'get',
    params: query
  })
}

// 获取产品最早入库的非期货库存
export function getEarliestNonFuturesStock(productId, storeId) {
  return request({
    url: '/retail/storestock/earliest/' + productId,
    method: 'get',
    params: { storeId }
  })
}

// 创建商品售出出库单
export function createOutOrder(data) {
  return request({
    url: '/retail/outorder/sale',
    method: 'post',
    data: data
  })
}

// 创建批量商品售出出库单
export function createBatchOutOrder(data) {
  return request({
    url: '/retail/outorder/batch-sale',
    method: 'post',
    data: data
  })
}

// 查询门店库存详细
export function getStoreStock(id) {
  return request({
    url: '/retail/storestock/' + id,
    method: 'get'
  })
}