import request from '@/utils/request'

// 查询出库单列表
export function listOutOrder(query) {
  return request({
    url: '/retail/outorder/list',
    method: 'get',
    params: query
  })
}

// 查询待审核出库单列表
export function listApprovalOutOrder(query) {
  return request({
    url: '/retail/outorder/approval-list',
    method: 'get',
    params: query
  })
}

// 查询出库单详细
export function getOutOrder(id) {
  return request({
    url: '/retail/outorder/' + id,
    method: 'get'
  })
}

// 查询出库单审核记录
export function getOutOrderAudit(id) {
  return request({
    url: '/retail/outorder/audit/' + id,
    method: 'get'
  })
}

// 审核通过出库单
export function approveOutOrder(data) {
  return request({
    url: '/retail/outorder/approve',
    method: 'post',
    data: data
  })
}

// 驳回出库单
export function rejectOutOrder(data) {
  return request({
    url: '/retail/outorder/reject',
    method: 'post',
    data: data
  })
}

// 撤销出库单
export function cancelOutOrder(data) {
  return request({
    url: '/retail/outorder/cancel',
    method: 'post',
    data: data
  })
}

// 申请退货
export function applyReturnOutOrder(data) {
  return request({
    url: '/retail/outorder/apply-return',
    method: 'post',
    data: data
  })
}

// 审核通过退货申请
export function approveReturnOutOrder(data) {
  return request({
    url: '/retail/outorder/approve-return',
    method: 'post',
    data: data
  })
}

// 驳回退货申请
export function rejectReturnOutOrder(data) {
  return request({
    url: '/retail/outorder/reject-return',
    method: 'post',
    data: data
  })
}

// 撤销退货申请
export function cancelReturnOutOrder(data) {
  return request({
    url: '/retail/outorder/cancel-return',
    method: 'post',
    data: data
  })
}
