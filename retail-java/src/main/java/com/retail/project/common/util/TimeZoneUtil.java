package com.retail.project.common.util;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.CommandLineRunner;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Component;

import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.TimeZone;

/**
 * 时区工具类，用于验证系统和数据库时区设置
 * 
 * <AUTHOR>
 */
@Component
public class TimeZoneUtil implements CommandLineRunner {
    
    private static final Logger log = LoggerFactory.getLogger(TimeZoneUtil.class);
    
    @Autowired
    private JdbcTemplate jdbcTemplate;
    
    @Override
    public void run(String... args) throws Exception {
        // 输出JVM默认时区
        TimeZone jvmTimeZone = TimeZone.getDefault();
        log.info("JVM默认时区: {} ({})", jvmTimeZone.getID(), jvmTimeZone.getDisplayName());
        
        // 输出当前Java时间
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        log.info("当前Java时间: {}", sdf.format(new Date()));
        
        // 输出数据库时区和时间
        try {
            List<Map<String, Object>> result = jdbcTemplate.queryForList("SELECT @@global.time_zone AS global_tz, @@session.time_zone AS session_tz, NOW() AS db_time");
            if (!result.isEmpty()) {
                Map<String, Object> row = result.get(0);
                log.info("数据库全局时区: {}", row.get("global_tz"));
                log.info("数据库会话时区: {}", row.get("session_tz"));
                log.info("当前数据库时间: {}", row.get("db_time"));
            }
        } catch (Exception e) {
            log.error("获取数据库时区信息失败", e);
        }
    }
}
