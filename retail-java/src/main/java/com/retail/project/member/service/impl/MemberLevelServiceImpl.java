package com.retail.project.member.service.impl;

import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.retail.project.member.mapper.MemberLevelMapper;
import com.retail.project.member.domain.MemberLevel;
import com.retail.project.member.service.IMemberLevelService;

/**
 * 会员等级Service业务层处理
 * 
 * <AUTHOR>
 * @date 2023-07-10
 */
@Service
public class MemberLevelServiceImpl implements IMemberLevelService 
{
    @Autowired
    private MemberLevelMapper memberLevelMapper;

    /**
     * 查询会员等级
     * 
     * @param levelId 会员等级主键
     * @return 会员等级
     */
    @Override
    public MemberLevel selectMemberLevelByLevelId(Long levelId)
    {
        return memberLevelMapper.selectMemberLevelByLevelId(levelId);
    }

    /**
     * 查询会员等级列表
     * 
     * @param memberLevel 会员等级
     * @return 会员等级
     */
    @Override
    public List<MemberLevel> selectMemberLevelList(MemberLevel memberLevel)
    {
        return memberLevelMapper.selectMemberLevelList(memberLevel);
    }

    /**
     * 新增会员等级
     * 
     * @param memberLevel 会员等级
     * @return 结果
     */
    @Override
    public int insertMemberLevel(MemberLevel memberLevel)
    {
        return memberLevelMapper.insertMemberLevel(memberLevel);
    }

    /**
     * 修改会员等级
     * 
     * @param memberLevel 会员等级
     * @return 结果
     */
    @Override
    public int updateMemberLevel(MemberLevel memberLevel)
    {
        return memberLevelMapper.updateMemberLevel(memberLevel);
    }

    /**
     * 批量删除会员等级
     * 
     * @param levelIds 需要删除的会员等级主键
     * @return 结果
     */
    @Override
    public int deleteMemberLevelByLevelIds(Long[] levelIds)
    {
        return memberLevelMapper.deleteMemberLevelByLevelIds(levelIds);
    }

    /**
     * 删除会员等级信息
     * 
     * @param levelId 会员等级主键
     * @return 结果
     */
    @Override
    public int deleteMemberLevelByLevelId(Long levelId)
    {
        return memberLevelMapper.deleteMemberLevelByLevelId(levelId);
    }
    
    /**
     * 查询所有会员等级
     * 
     * @return 会员等级列表
     */
    @Override
    public List<MemberLevel> selectMemberLevelAll()
    {
        return memberLevelMapper.selectMemberLevelAll();
    }
}
