package com.retail.project.member.domain;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.retail.framework.aspectj.lang.annotation.Excel;
import com.retail.framework.web.domain.BaseEntity;

import java.math.BigDecimal;

/**
 * 会员等级对象 member_level
 *
 * <AUTHOR>
 * @date 2023-07-10
 */
public class MemberLevel extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 等级ID */
    private Long levelId;

    /** 等级名称 */
    @Excel(name = "等级名称")
    private String levelName;

    /** 最小消费金额 */
    @Excel(name = "最小消费金额")
    private BigDecimal minAmount;

    /** 最大消费金额 */
    @Excel(name = "最大消费金额")
    private BigDecimal maxAmount;

    /** 折扣率 */
    @Excel(name = "折扣率")
    private BigDecimal discountRate;

    /** 标签类型 */
    private String tagType;

    /** 状态（0停用 1正常） */
    @Excel(name = "状态", readConverterExp = "0=停用,1=正常")
    private Integer status;

    public void setLevelId(Long levelId)
    {
        this.levelId = levelId;
    }

    public Long getLevelId()
    {
        return levelId;
    }
    public void setLevelName(String levelName)
    {
        this.levelName = levelName;
    }

    public String getLevelName()
    {
        return levelName;
    }
    public void setMinAmount(BigDecimal minAmount)
    {
        this.minAmount = minAmount;
    }

    public BigDecimal getMinAmount()
    {
        return minAmount;
    }
    public void setMaxAmount(BigDecimal maxAmount)
    {
        this.maxAmount = maxAmount;
    }

    public BigDecimal getMaxAmount()
    {
        return maxAmount;
    }
    public void setDiscountRate(BigDecimal discountRate)
    {
        this.discountRate = discountRate;
    }

    public BigDecimal getDiscountRate()
    {
        return discountRate;
    }
    public void setTagType(String tagType)
    {
        this.tagType = tagType;
    }

    public String getTagType()
    {
        return tagType;
    }
    public void setStatus(Integer status)
    {
        this.status = status;
    }

    public Integer getStatus()
    {
        return status;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("levelId", getLevelId())
            .append("levelName", getLevelName())
            .append("minAmount", getMinAmount())
            .append("maxAmount", getMaxAmount())
            .append("discountRate", getDiscountRate())
            .append("tagType", getTagType())
            .append("status", getStatus())
            .append("createBy", getCreateBy())
            .append("createTime", getCreateTime())
            .append("updateBy", getUpdateBy())
            .append("updateTime", getUpdateTime())
            .append("remark", getRemark())
            .toString();
    }
}
