package com.retail.project.member.controller;

import java.util.List;
import java.math.BigDecimal;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.retail.framework.aspectj.lang.annotation.Log;
import com.retail.framework.aspectj.lang.enums.BusinessType;
import com.retail.project.member.domain.Member;
import com.retail.framework.web.controller.BaseController;
import com.retail.framework.web.domain.AjaxResult;
import com.retail.project.member.service.IMemberService;
import com.retail.common.utils.poi.ExcelUtil;
import com.retail.framework.web.page.TableDataInfo;

/**
 * 会员管理Controller
 *
 * <AUTHOR>
 * @date 2023-06-01
 */
@RestController
@RequestMapping("/member")
public class MemberController extends BaseController
{
    @Autowired
    private IMemberService memberService;

    /**
     * 查询会员列表
     */
    @PreAuthorize("@ss.hasPermi('member:member:list')")
    @GetMapping("/list")
    public TableDataInfo list(Member member)
    {
        startPage();
        List<Member> list = memberService.selectMemberList(member);
        return getDataTable(list);
    }

    /**
     * 导出会员列表
     */
    @PreAuthorize("@ss.hasPermi('member:member:export')")
    @Log(title = "会员", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public AjaxResult export(Member member)
    {
        List<Member> list = memberService.selectMemberList(member);
        ExcelUtil<Member> util = new ExcelUtil<Member>(Member.class);
        return util.exportExcel(list, "会员数据");
    }

    /**
     * 获取会员详细信息
     */
    @PreAuthorize("@ss.hasPermi('member:member:query')")
    @GetMapping(value = "/{memberId}")
    public AjaxResult getInfo(@PathVariable("memberId") Long memberId)
    {
        return AjaxResult.success(memberService.selectMemberByMemberId(memberId));
    }

    /**
     * 新增会员
     */
    @PreAuthorize("@ss.hasPermi('member:member:add')")
    @Log(title = "会员", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody Member member)
    {
        member.setCreateBy(getUsername());
        return toAjax(memberService.insertMember(member));
    }

    /**
     * 修改会员
     */
    @PreAuthorize("@ss.hasPermi('member:member:edit')")
    @Log(title = "会员", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody Member member)
    {
        member.setUpdateBy(getUsername());
        return toAjax(memberService.updateMember(member));
    }

    /**
     * 删除会员
     */
    @PreAuthorize("@ss.hasPermi('member:member:remove')")
    @Log(title = "会员", businessType = BusinessType.DELETE)
	@DeleteMapping("/{memberIds}")
    public AjaxResult remove(@PathVariable Long[] memberIds)
    {
        return toAjax(memberService.deleteMemberByMemberIds(memberIds));
    }

    /**
     * 根据手机号码搜索会员
     */
    @GetMapping("/search")
    public AjaxResult searchByPhone(String phoneNumber)
    {
        if (phoneNumber == null || phoneNumber.isEmpty()) {
            return AjaxResult.error("手机号码不能为空");
        }
        Member member = memberService.selectMemberByPhoneNumber(phoneNumber);
        return AjaxResult.success(member);
    }

    /**
     * 更新会员累计消费金额
     */
    @PreAuthorize("@ss.hasPermi('member:member:edit')")
    @Log(title = "会员", businessType = BusinessType.UPDATE)
    @PutMapping("/update-amount")
    public AjaxResult updateAmount(@RequestBody Member member)
    {
        if (member.getMemberId() == null) {
            return AjaxResult.error("会员ID不能为空");
        }
        if (member.getTotalAmount() == null) {
            return AjaxResult.error("累计消费金额不能为空");
        }

        // 获取当前会员信息
        Member currentMember = memberService.selectMemberByMemberId(member.getMemberId());
        if (currentMember == null) {
            return AjaxResult.error("会员不存在");
        }

        // 计算增加的金额
        BigDecimal currentAmount = currentMember.getTotalAmount() != null ? currentMember.getTotalAmount() : new BigDecimal(0);
        BigDecimal newAmount = member.getTotalAmount();
        double amountDiff = newAmount.subtract(currentAmount).doubleValue();

        // 更新会员累计消费金额
        return toAjax(memberService.updateMemberAmount(member.getMemberId(), amountDiff));
    }
}
