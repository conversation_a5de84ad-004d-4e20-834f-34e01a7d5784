package com.retail.project.member.service.impl;

import java.util.List;
import java.math.BigDecimal;
import java.util.Random;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.retail.project.member.mapper.MemberMapper;
import com.retail.project.member.service.IMemberService;
import com.retail.project.member.domain.Member;

/**
 * 会员Service业务层处理
 *
 * <AUTHOR>
 * @date 2023-06-01
 */
@Service
public class MemberServiceImpl implements IMemberService
{
    @Autowired
    private MemberMapper memberMapper;

    /**
     * 查询会员
     *
     * @param memberId 会员主键
     * @return 会员
     */
    @Override
    public Member selectMemberByMemberId(Long memberId)
    {
        return memberMapper.selectMemberByMemberId(memberId);
    }

    /**
     * 查询会员列表
     *
     * @param member 会员
     * @return 会员
     */
    @Override
    public List<Member> selectMemberList(Member member)
    {
        return memberMapper.selectMemberList(member);
    }

    /**
     * 新增会员
     *
     * @param member 会员
     * @return 结果
     */
    @Override
    public int insertMember(Member member)
    {
        // 生成10位会员ID
        Long memberId = generateMemberId();
        member.setMemberId(memberId);

        // 如果没有设置会员等级，根据累计消费金额自动计算
        if (member.getMemberLevel() == null && member.getTotalAmount() != null) {
            member.setMemberLevel(calculateMemberLevel(member.getTotalAmount()));
        }

        return memberMapper.insertMember(member);
    }

    /**
     * 生成10位会员ID
     *
     * 格式：年月日(6位) + 4位随机数
     * 例如：2306011234
     *
     * @return 10位会员ID
     */
    private Long generateMemberId() {
        // 获取当前日期，格式为yyMMdd（年月日，共6位）
        String datePrefix = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyMMdd"));

        // 生成4位随机数
        Random random = new Random();
        int randomNum = random.nextInt(10000);
        String randomSuffix = String.format("%04d", randomNum); // 确保是4位，不足前面补0

        // 组合成10位会员ID
        String memberIdStr = datePrefix + randomSuffix;
        Long memberId = Long.parseLong(memberIdStr);

        // 检查会员ID是否已存在，如果存在则重新生成
        Member existingMember = memberMapper.selectMemberByMemberId(memberId);
        if (existingMember != null) {
            return generateMemberId(); // 递归调用，直到生成唯一的会员ID
        }

        return memberId;
    }

    /**
     * 修改会员
     *
     * @param member 会员
     * @return 结果
     */
    @Override
    public int updateMember(Member member)
    {
        // 如果修改了累计消费金额，自动更新会员等级
        if (member.getTotalAmount() != null && member.getMemberLevel() == null) {
            member.setMemberLevel(calculateMemberLevel(member.getTotalAmount()));
        }

        return memberMapper.updateMember(member);
    }

    /**
     * 批量删除会员
     *
     * @param memberIds 需要删除的会员主键
     * @return 结果
     */
    @Override
    public int deleteMemberByMemberIds(Long[] memberIds)
    {
        return memberMapper.deleteMemberByMemberIds(memberIds);
    }

    /**
     * 删除会员信息
     *
     * @param memberId 会员主键
     * @return 结果
     */
    @Override
    public int deleteMemberByMemberId(Long memberId)
    {
        return memberMapper.deleteMemberByMemberId(memberId);
    }

    /**
     * 根据手机号码查询会员
     *
     * @param phoneNumber 手机号码
     * @return 会员
     */
    @Override
    public Member selectMemberByPhoneNumber(String phoneNumber)
    {
        return memberMapper.selectMemberByPhoneNumber(phoneNumber);
    }

    /**
     * 更新会员累计消费金额
     *
     * @param memberId 会员ID
     * @param amount 增加的消费金额
     * @return 结果
     */
    @Override
    public int updateMemberAmount(Long memberId, double amount)
    {
        // 查询会员信息
        Member member = memberMapper.selectMemberByMemberId(memberId);
        if (member == null) {
            return 0;
        }

        // 计算新的累计消费金额
        BigDecimal currentAmount = member.getTotalAmount() != null ? member.getTotalAmount() : new BigDecimal(0);
        BigDecimal newAmount = currentAmount.add(new BigDecimal(amount));

        // 更新会员信息
        Member updateMember = new Member();
        updateMember.setMemberId(memberId);
        updateMember.setTotalAmount(newAmount);
        updateMember.setMemberLevel(calculateMemberLevel(newAmount));

        return memberMapper.updateMemberAmount(updateMember);
    }

    /**
     * 根据累计消费金额计算会员等级
     *
     * @param totalAmount 累计消费金额
     * @return 会员等级
     */
    @Override
    public Integer calculateMemberLevel(BigDecimal totalAmount)
    {
        if (totalAmount == null) {
            return 1; // 默认为普通会员
        }

        // 根据累计消费金额计算会员等级
        // 这里的等级划分应该与会员等级管理中的设置保持一致
        if (totalAmount.compareTo(new BigDecimal("50000")) >= 0) {
            return 5; // 钻石会员
        } else if (totalAmount.compareTo(new BigDecimal("10000")) >= 0) {
            return 4; // VIP会员
        } else if (totalAmount.compareTo(new BigDecimal("5000")) >= 0) {
            return 3; // 金卡会员
        } else if (totalAmount.compareTo(new BigDecimal("1000")) >= 0) {
            return 2; // 银卡会员
        } else {
            return 1; // 普通会员
        }
    }
}
