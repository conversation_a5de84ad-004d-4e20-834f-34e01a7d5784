package com.retail.project.member.service;

import java.util.List;
import java.math.BigDecimal;
import com.retail.project.member.domain.Member;

/**
 * 会员Service接口
 *
 * <AUTHOR>
 * @date 2023-06-01
 */
public interface IMemberService
{
    /**
     * 查询会员
     *
     * @param memberId 会员主键
     * @return 会员
     */
    public Member selectMemberByMemberId(Long memberId);

    /**
     * 查询会员列表
     *
     * @param member 会员
     * @return 会员集合
     */
    public List<Member> selectMemberList(Member member);

    /**
     * 新增会员
     *
     * @param member 会员
     * @return 结果
     */
    public int insertMember(Member member);

    /**
     * 修改会员
     *
     * @param member 会员
     * @return 结果
     */
    public int updateMember(Member member);

    /**
     * 批量删除会员
     *
     * @param memberIds 需要删除的会员主键集合
     * @return 结果
     */
    public int deleteMemberByMemberIds(Long[] memberIds);

    /**
     * 删除会员信息
     *
     * @param memberId 会员主键
     * @return 结果
     */
    public int deleteMemberByMemberId(Long memberId);

    /**
     * 根据手机号码查询会员
     *
     * @param phoneNumber 手机号码
     * @return 会员
     */
    public Member selectMemberByPhoneNumber(String phoneNumber);

    /**
     * 更新会员累计消费金额
     *
     * @param memberId 会员ID
     * @param amount 增加的消费金额
     * @return 结果
     */
    public int updateMemberAmount(Long memberId, double amount);

    /**
     * 根据累计消费金额计算会员等级
     *
     * @param totalAmount 累计消费金额
     * @return 会员等级
     */
    public Integer calculateMemberLevel(BigDecimal totalAmount);
}
