package com.retail.project.analysis.controller;

import java.math.BigDecimal;
import java.util.*;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.retail.framework.web.controller.BaseController;
import com.retail.framework.web.domain.AjaxResult;
import com.retail.project.retail.domain.InventoryStock;
import com.retail.project.retail.domain.RetailStore;
import com.retail.project.retail.domain.RetailChannel;
import com.retail.project.retail.service.IInventoryStockService;
import com.retail.project.retail.service.IRetailStoreService;
import com.retail.project.retail.service.IRetailChannelService;

/**
 * 库存数据分析Controller
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/analysis/inventory")
public class InventoryAnalysisController extends BaseController
{
    @Autowired
    private IInventoryStockService inventoryStockService;

    @Autowired
    private IRetailStoreService retailStoreService;

    @Autowired
    private IRetailChannelService retailChannelService;
    /**
     * 获取库存统计数据
     */
    @PreAuthorize("@ss.hasPermi('analysis:sale:data')")
    @GetMapping("/statistics")
    public AjaxResult getStatistics()
    {
        Map<String, Object> data = new HashMap<>();

        // 查询所有库存
        List<InventoryStock> allStocks = inventoryStockService.selectInventoryStockList(new InventoryStock());

        // 计算库存总量
        int totalInventory = allStocks.stream().mapToInt(InventoryStock::getQuantity).sum();

        // 计算库存总价值
        BigDecimal totalValue = BigDecimal.ZERO;
        for (InventoryStock stock : allStocks) {
            if (stock.getPurchasePrice() != null && stock.getQuantity() != null) {
                BigDecimal itemValue = stock.getPurchasePrice().multiply(new BigDecimal(stock.getQuantity()));
                totalValue = totalValue.add(itemValue);
            }
        }

        // 计算期货库存量和价值
        int futuresInventory = 0;
        BigDecimal futuresValue = BigDecimal.ZERO;
        for (InventoryStock stock : allStocks) {
            if (stock.getIsFutures() != null && stock.getIsFutures()) {
                futuresInventory += stock.getQuantity();
                if (stock.getPurchasePrice() != null) {
                    BigDecimal itemValue = stock.getPurchasePrice().multiply(new BigDecimal(stock.getQuantity()));
                    futuresValue = futuresValue.add(itemValue);
                }
            }
        }

        // 计算库存周转率（这里简化处理，实际应该根据一段时间内的销售量和平均库存计算）
        BigDecimal turnoverRate = new BigDecimal("3.5"); // 示例值，实际项目中应该计算

        data.put("totalInventory", totalInventory);
        data.put("totalValue", totalValue);
        data.put("futuresInventory", futuresInventory);
        data.put("futuresValue", futuresValue);
        data.put("turnoverRate", turnoverRate);

        // 按门店分组统计库存 - 使用StoreStockController的信息
        Map<Long, Integer> inventoryByStore = new HashMap<>();
        Map<Long, BigDecimal> valueByStore = new HashMap<>();
        Map<Long, String> storeNames = new HashMap<>();

        // 获取所有门店
        List<RetailStore> allStores = retailStoreService.selectRetailStoreList(new RetailStore());
        for (RetailStore store : allStores) {
            inventoryByStore.put(store.getId(), 0);
            valueByStore.put(store.getId(), BigDecimal.ZERO);
            storeNames.put(store.getId(), store.getStoreName());
        }

        // 统计每个门店的库存
        for (InventoryStock stock : allStocks) {
            Long storeId = stock.getStoreId();
            if (storeId != null && inventoryByStore.containsKey(storeId)) {
                inventoryByStore.put(storeId, inventoryByStore.get(storeId) + stock.getQuantity());

                if (stock.getPurchasePrice() != null) {
                    BigDecimal itemValue = stock.getPurchasePrice().multiply(new BigDecimal(stock.getQuantity()));
                    valueByStore.put(storeId, valueByStore.get(storeId).add(itemValue));
                }
            }
        }

        // 构建门店库存分布数据
        List<Map<String, Object>> inventoryDistribution = new ArrayList<>();
        for (Map.Entry<Long, Integer> entry : inventoryByStore.entrySet()) {
            if (entry.getValue() > 0) { // 只显示有库存的门店
                Map<String, Object> storeData = new HashMap<>();
                storeData.put("name", storeNames.getOrDefault(entry.getKey(), "未知门店"));
                storeData.put("value", entry.getValue());
                storeData.put("inventoryValue", valueByStore.get(entry.getKey()));
                inventoryDistribution.add(storeData);
            }
        }

        // 按销售量排序，只保留前5个
        inventoryDistribution.sort((a, b) -> ((Integer) b.get("value")).compareTo((Integer) a.get("value")));
        if (inventoryDistribution.size() > 5) {
            inventoryDistribution = inventoryDistribution.subList(0, 5);
        }

        data.put("inventoryDistribution", inventoryDistribution);

        // 库存渠道分布（替换原来的库存类型分布）
        Map<Long, Integer> inventoryByChannel = new HashMap<>();
        Map<Long, BigDecimal> valueByChannel = new HashMap<>();
        Map<Long, String> channelNames = new HashMap<>();

        // 获取所有渠道
        List<RetailChannel> allChannels = retailChannelService.selectRetailChannelList(new RetailChannel());
        for (RetailChannel channel : allChannels) {
            inventoryByChannel.put(channel.getId(), 0);
            valueByChannel.put(channel.getId(), BigDecimal.ZERO);
            channelNames.put(channel.getId(), channel.getChannelName());
        }

        // 统计每个渠道的库存
        for (InventoryStock stock : allStocks) {
            Long channelId = stock.getChannelId();
            if (channelId != null && inventoryByChannel.containsKey(channelId)) {
                inventoryByChannel.put(channelId, inventoryByChannel.get(channelId) + stock.getQuantity());

                if (stock.getPurchasePrice() != null) {
                    BigDecimal itemValue = stock.getPurchasePrice().multiply(new BigDecimal(stock.getQuantity()));
                    valueByChannel.put(channelId, valueByChannel.get(channelId).add(itemValue));
                }
            }
        }

        // 构建渠道库存分布数据
        List<Map<String, Object>> inventoryChannelDistribution = new ArrayList<>();
        for (Map.Entry<Long, Integer> entry : inventoryByChannel.entrySet()) {
            if (entry.getValue() > 0) { // 只显示有库存的渠道
                Map<String, Object> channelData = new HashMap<>();
                channelData.put("name", channelNames.getOrDefault(entry.getKey(), "未知渠道"));
                channelData.put("value", entry.getValue());
                channelData.put("inventoryValue", valueByChannel.get(entry.getKey()));
                inventoryChannelDistribution.add(channelData);
            }
        }

        // 按销售量排序，只保留前5个
        inventoryChannelDistribution.sort((a, b) -> ((Integer) b.get("value")).compareTo((Integer) a.get("value")));
        if (inventoryChannelDistribution.size() > 5) {
            inventoryChannelDistribution = inventoryChannelDistribution.subList(0, 5);
        }

        data.put("inventoryChannelDistribution", inventoryChannelDistribution);

        return AjaxResult.success(data);
    }
}
