package com.retail.project.analysis.controller;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.stream.Collectors;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.retail.framework.web.controller.BaseController;
import com.retail.framework.web.domain.AjaxResult;
import com.retail.project.retail.domain.InventoryOutOrder;
import com.retail.project.retail.domain.InventoryOutDetail;
import com.retail.project.retail.domain.InventoryInDetail;
import com.retail.project.retail.domain.InventoryStock;
import com.retail.project.member.domain.Member;
import com.retail.project.member.service.IMemberService;
import com.retail.project.retail.service.IInventoryOutOrderService;
import com.retail.project.retail.service.IInventoryStockService;
import com.retail.project.retail.service.IRetailStoreService;
import com.retail.project.retail.service.IRetailChannelService;
import com.retail.project.retail.service.IRetailProductsService;
import com.retail.project.retail.mapper.InventoryOutDetailMapper;
import com.retail.project.retail.mapper.InventoryInDetailMapper;

/**
 * 销售数据分析Controller
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/analysis/sale")
public class SaleAnalysisController extends BaseController
{
    @Autowired
    private IInventoryOutOrderService inventoryOutOrderService;

    @Autowired
    private InventoryOutDetailMapper inventoryOutDetailMapper;

    @Autowired
    private InventoryInDetailMapper inventoryInDetailMapper;

    @Autowired
    private IInventoryStockService inventoryStockService;

    @Autowired
    private IRetailStoreService retailStoreService;

    @Autowired
    private IRetailChannelService retailChannelService;

    @Autowired
    private IRetailProductsService retailProductsService;

    @Autowired
    private IMemberService memberService;
    /**
     * 获取销售统计数据（优化版本）
     */
    @PreAuthorize("@ss.hasPermi('analysis:sale:data')")
    @GetMapping("/statistics")
    public AjaxResult getStatistics(String startDate, String endDate, Long storeId, Long channelId)
    {
        // 构建查询参数
        Map<String, Object> params = new HashMap<>();

        if (storeId != null) {
            params.put("storeId", storeId);
        }

        if (channelId != null) {
            params.put("channelId", channelId);
        }

        if (startDate != null && endDate != null) {
            params.put("beginActualSaleTime", startDate + " 00:00:00");
            params.put("endActualSaleTime", endDate + " 23:59:59");
        }

        // 使用优化的SQL查询直接获取统计数据
        Map<String, Object> statistics = inventoryOutDetailMapper.selectSaleStatistics(params);

        // 处理查询结果
        Map<String, Object> data = new HashMap<>();

        BigDecimal totalSales = (BigDecimal) statistics.get("totalSales");
        BigDecimal totalProfit = (BigDecimal) statistics.get("totalProfit");

        // 处理可能是BigDecimal类型的数值字段
        Object totalOrdersObj = statistics.get("totalOrders");
        Object totalQuantityObj = statistics.get("totalQuantity");

        Long totalOrders = 0L;
        Long totalQuantity = 0L;

        if (totalOrdersObj instanceof BigDecimal) {
            totalOrders = ((BigDecimal) totalOrdersObj).longValue();
        } else if (totalOrdersObj instanceof Long) {
            totalOrders = (Long) totalOrdersObj;
        } else if (totalOrdersObj instanceof Integer) {
            totalOrders = ((Integer) totalOrdersObj).longValue();
        }

        if (totalQuantityObj instanceof BigDecimal) {
            totalQuantity = ((BigDecimal) totalQuantityObj).longValue();
        } else if (totalQuantityObj instanceof Long) {
            totalQuantity = (Long) totalQuantityObj;
        } else if (totalQuantityObj instanceof Integer) {
            totalQuantity = ((Integer) totalQuantityObj).longValue();
        }

        // 确保不为null
        totalSales = totalSales != null ? totalSales : BigDecimal.ZERO;
        totalProfit = totalProfit != null ? totalProfit : BigDecimal.ZERO;

        // 计算平均客单价
        BigDecimal averageOrderValue = totalOrders > 0
                ? totalSales.divide(new BigDecimal(totalOrders), 2, RoundingMode.HALF_UP)
                : BigDecimal.ZERO;

        // 计算平均单品价格
        BigDecimal averageItemPrice = totalQuantity > 0
                ? totalSales.divide(new BigDecimal(totalQuantity), 2, RoundingMode.HALF_UP)
                : BigDecimal.ZERO;

        data.put("totalSales", totalSales);
        data.put("totalOrders", totalOrders.intValue());
        data.put("totalQuantity", totalQuantity.intValue());
        data.put("totalProfit", totalProfit);
        data.put("averageOrderValue", averageOrderValue);
        data.put("averageItemPrice", averageItemPrice);

        return AjaxResult.success(data);
    }

    /**
     * 获取销售趋势数据（优化版本）
     */
    @PreAuthorize("@ss.hasPermi('analysis:sale:data')")
    @GetMapping("/trend")
    public AjaxResult getTrend(String startDate, String endDate, Long storeId, Long channelId, String timeUnit)
    {
        // 构建查询参数
        Map<String, Object> params = new HashMap<>();

        if (storeId != null) {
            params.put("storeId", storeId);
        }

        if (channelId != null) {
            params.put("channelId", channelId);
        }

        if (startDate != null && endDate != null) {
            params.put("beginActualSaleTime", startDate + " 00:00:00");
            params.put("endActualSaleTime", endDate + " 23:59:59");
        }

        // 设置时间维度，默认为月
        if (timeUnit == null || timeUnit.isEmpty()) {
            timeUnit = "month";
        }
        params.put("timeUnit", timeUnit);

        // 使用优化的SQL查询直接获取趋势数据
        List<Map<String, Object>> trendData = inventoryOutDetailMapper.selectSaleTrendData(params);

        // 处理查询结果
        List<String> dateList = new ArrayList<>();
        List<BigDecimal> salesData = new ArrayList<>();
        List<Integer> orderData = new ArrayList<>();
        List<Integer> quantityData = new ArrayList<>();

        for (Map<String, Object> row : trendData) {
            String timeLabel = (String) row.get("timeLabel");
            BigDecimal salesAmount = (BigDecimal) row.get("salesAmount");

            // 处理可能是BigDecimal类型的数值字段
            Object orderCountObj = row.get("orderCount");
            Object totalQuantityObj = row.get("totalQuantity");

            int orderCount = 0;
            int totalQuantityInt = 0;

            if (orderCountObj instanceof BigDecimal) {
                orderCount = ((BigDecimal) orderCountObj).intValue();
            } else if (orderCountObj instanceof Long) {
                orderCount = ((Long) orderCountObj).intValue();
            } else if (orderCountObj instanceof Integer) {
                orderCount = (Integer) orderCountObj;
            }

            if (totalQuantityObj instanceof BigDecimal) {
                totalQuantityInt = ((BigDecimal) totalQuantityObj).intValue();
            } else if (totalQuantityObj instanceof Long) {
                totalQuantityInt = ((Long) totalQuantityObj).intValue();
            } else if (totalQuantityObj instanceof Integer) {
                totalQuantityInt = (Integer) totalQuantityObj;
            }

            dateList.add(timeLabel);
            salesData.add(salesAmount != null ? salesAmount : BigDecimal.ZERO);
            orderData.add(orderCount);
            quantityData.add(totalQuantityInt);
        }

        Map<String, Object> data = new HashMap<>();
        data.put("dateList", dateList);
        data.put("salesData", salesData);
        data.put("orderData", orderData);
        data.put("quantityData", quantityData);

        return AjaxResult.success(data);
    }

    /**
     * 获取销售渠道分布数据（优化版本）
     */
    @PreAuthorize("@ss.hasPermi('analysis:sale:data')")
    @GetMapping("/channel")
    public AjaxResult getChannelDistribution(String startDate, String endDate, Long storeId, Long channelId)
    {
        // 构建查询参数
        Map<String, Object> params = new HashMap<>();

        if (storeId != null) {
            params.put("storeId", storeId);
        }

        if (channelId != null) {
            params.put("channelId", channelId);
        }

        if (startDate != null && endDate != null) {
            params.put("beginActualSaleTime", startDate + " 00:00:00");
            params.put("endActualSaleTime", endDate + " 23:59:59");
        }

        // 使用优化的SQL查询直接获取渠道分布数据
        List<Map<String, Object>> channelData = inventoryOutDetailMapper.selectChannelSalesDistribution(params);

        // 处理查询结果
        List<Map<String, Object>> data = new ArrayList<>();
        for (Map<String, Object> row : channelData) {
            Map<String, Object> channelInfo = new HashMap<>();
            channelInfo.put("name", row.get("channelName") != null ? row.get("channelName") : "未知渠道");

            // 处理销售额，可能是BigDecimal类型
            Object salesAmountObj = row.get("salesAmount");
            BigDecimal salesAmount = BigDecimal.ZERO;
            if (salesAmountObj instanceof BigDecimal) {
                salesAmount = (BigDecimal) salesAmountObj;
            } else if (salesAmountObj instanceof Long) {
                salesAmount = new BigDecimal((Long) salesAmountObj);
            } else if (salesAmountObj instanceof Integer) {
                salesAmount = new BigDecimal((Integer) salesAmountObj);
            }

            channelInfo.put("value", salesAmount);
            data.add(channelInfo);
        }

        return AjaxResult.success(data);
    }

    /**
     * 获取销售门店排行数据（优化版本）
     */
    @PreAuthorize("@ss.hasPermi('analysis:sale:data')")
    @GetMapping("/storeRanking")
    public AjaxResult getStoreRanking(String startDate, String endDate, Long storeId, Long channelId)
    {
        // 构建查询参数
        Map<String, Object> params = new HashMap<>();

        if (storeId != null) {
            params.put("storeId", storeId);
        }

        if (channelId != null) {
            params.put("channelId", channelId);
        }

        if (startDate != null && endDate != null) {
            params.put("beginActualSaleTime", startDate + " 00:00:00");
            params.put("endActualSaleTime", endDate + " 23:59:59");
        }

        // 使用优化的SQL查询直接获取门店排行数据
        List<Map<String, Object>> storeData = inventoryOutDetailMapper.selectStoreSalesRanking(params);

        // 处理查询结果
        List<Map<String, Object>> data = new ArrayList<>();
        for (Map<String, Object> row : storeData) {
            Map<String, Object> storeInfo = new HashMap<>();
            storeInfo.put("name", row.get("storeName") != null ? row.get("storeName") : "未知门店");

            // 处理销售额，可能是BigDecimal类型
            Object salesAmountObj = row.get("salesAmount");
            BigDecimal salesAmount = BigDecimal.ZERO;
            if (salesAmountObj instanceof BigDecimal) {
                salesAmount = (BigDecimal) salesAmountObj;
            } else if (salesAmountObj instanceof Long) {
                salesAmount = new BigDecimal((Long) salesAmountObj);
            } else if (salesAmountObj instanceof Integer) {
                salesAmount = new BigDecimal((Integer) salesAmountObj);
            }

            // 处理订单数，可能是BigDecimal类型
            Object orderCountObj = row.get("orderCount");
            int orderCount = 0;
            if (orderCountObj instanceof BigDecimal) {
                orderCount = ((BigDecimal) orderCountObj).intValue();
            } else if (orderCountObj instanceof Long) {
                orderCount = ((Long) orderCountObj).intValue();
            } else if (orderCountObj instanceof Integer) {
                orderCount = (Integer) orderCountObj;
            }

            storeInfo.put("sales", salesAmount);
            storeInfo.put("orders", orderCount);
            data.add(storeInfo);
        }

        return AjaxResult.success(data);
    }

    /**
     * 获取销售商品排行数据（优化版本）
     */
    @PreAuthorize("@ss.hasPermi('analysis:sale:data')")
    @GetMapping("/productRanking")
    public AjaxResult getProductRanking(String startDate, String endDate, Long storeId, Long channelId)
    {
        // 构建查询参数
        Map<String, Object> params = new HashMap<>();

        if (storeId != null) {
            params.put("storeId", storeId);
        }

        if (channelId != null) {
            params.put("channelId", channelId);
        }

        if (startDate != null && endDate != null) {
            params.put("beginActualSaleTime", startDate + " 00:00:00");
            params.put("endActualSaleTime", endDate + " 23:59:59");
        }

        // 使用优化的SQL查询直接获取商品排行数据
        List<Map<String, Object>> productData = inventoryOutDetailMapper.selectProductSalesRanking(params);

        // 处理查询结果
        List<Map<String, Object>> data = new ArrayList<>();
        for (Map<String, Object> row : productData) {
            Map<String, Object> productInfo = new HashMap<>();
            productInfo.put("name", row.get("productName") != null ? row.get("productName") : "未知商品");
            productInfo.put("productNumber", row.get("productNumber") != null ? row.get("productNumber") : "");
            productInfo.put("productImage", row.get("productImage") != null ? row.get("productImage") : "");

            // 处理销售额，可能是BigDecimal类型
            Object salesAmountObj = row.get("salesAmount");
            BigDecimal salesAmount = BigDecimal.ZERO;
            if (salesAmountObj instanceof BigDecimal) {
                salesAmount = (BigDecimal) salesAmountObj;
            } else if (salesAmountObj instanceof Long) {
                salesAmount = new BigDecimal((Long) salesAmountObj);
            } else if (salesAmountObj instanceof Integer) {
                salesAmount = new BigDecimal((Integer) salesAmountObj);
            }

            // 处理销售数量，可能是BigDecimal类型
            Object totalQuantityObj = row.get("totalQuantity");
            int totalQuantity = 0;
            if (totalQuantityObj instanceof BigDecimal) {
                totalQuantity = ((BigDecimal) totalQuantityObj).intValue();
            } else if (totalQuantityObj instanceof Long) {
                totalQuantity = ((Long) totalQuantityObj).intValue();
            } else if (totalQuantityObj instanceof Integer) {
                totalQuantity = (Integer) totalQuantityObj;
            }

            productInfo.put("sales", salesAmount);
            productInfo.put("quantity", totalQuantity);
            data.add(productInfo);
        }

        return AjaxResult.success(data);
    }

    /**
     * 获取客户性别分布数据（优化版本）
     */
    @PreAuthorize("@ss.hasPermi('analysis:sale:data')")
    @GetMapping("/customerGender")
    public AjaxResult getCustomerGenderDistribution(String startDate, String endDate, Long storeId, Long channelId)
    {
        // 构建查询参数
        Map<String, Object> params = new HashMap<>();

        if (storeId != null) {
            params.put("storeId", storeId);
        }

        if (channelId != null) {
            params.put("channelId", channelId);
        }

        if (startDate != null && endDate != null) {
            params.put("beginActualSaleTime", startDate + " 00:00:00");
            params.put("endActualSaleTime", endDate + " 23:59:59");
        }

        // 使用优化的SQL查询直接获取性别分布数据
        List<Map<String, Object>> genderData = inventoryOutDetailMapper.selectCustomerGenderDistribution(params);

        // 初始化统计数据
        int maleCount = 0;
        int femaleCount = 0;

        // 处理查询结果
        for (Map<String, Object> row : genderData) {
            Object genderObj = row.get("gender");
            Object orderCountObj = row.get("orderCount");

            int orderCount = 0;
            if (orderCountObj instanceof BigDecimal) {
                orderCount = ((BigDecimal) orderCountObj).intValue();
            } else if (orderCountObj instanceof Long) {
                orderCount = ((Long) orderCountObj).intValue();
            } else if (orderCountObj instanceof Integer) {
                orderCount = (Integer) orderCountObj;
            }

            if (genderObj != null) {
                Integer gender = null;

                // 处理各种类型的性别字段
                if (genderObj instanceof Boolean) {
                    // Boolean类型：false=0(女性), true=1(男性)
                    gender = ((Boolean) genderObj) ? 1 : 0;
                } else if (genderObj instanceof Integer) {
                    gender = (Integer) genderObj;
                } else if (genderObj instanceof Long) {
                    gender = ((Long) genderObj).intValue();
                } else if (genderObj instanceof BigDecimal) {
                    gender = ((BigDecimal) genderObj).intValue();
                } else if (genderObj instanceof String) {
                    try {
                        gender = Integer.parseInt((String) genderObj);
                    } catch (NumberFormatException e) {
                        // 如果转换失败，跳过这条记录
                        continue;
                    }
                }

                if (gender != null) {
                    if (gender == 0) {
                        femaleCount += orderCount; // 0代表女性
                    } else if (gender == 1) {
                        maleCount += orderCount; // 1代表男性
                    }
                }
            }
        }

        // 构建返回数据
        List<Map<String, Object>> data = new ArrayList<>();

        Map<String, Object> male = new HashMap<>();
        male.put("name", "男性");
        male.put("value", maleCount);
        data.add(male);

        Map<String, Object> female = new HashMap<>();
        female.put("name", "女性");
        female.put("value", femaleCount);
        data.add(female);

        return AjaxResult.success(data);
    }

    /**
     * 获取利润分析数据（优化版本）
     */
    @PreAuthorize("@ss.hasPermi('analysis:sale:data')")
    @GetMapping("/profit")
    public AjaxResult getProfitAnalysis(String startDate, String endDate, Long storeId, Long channelId, String timeUnit)
    {
        // 构建查询参数
        Map<String, Object> params = new HashMap<>();

        if (storeId != null) {
            params.put("storeId", storeId);
        }

        if (channelId != null) {
            params.put("channelId", channelId);
        }

        if (startDate != null && endDate != null) {
            params.put("beginActualSaleTime", startDate + " 00:00:00");
            params.put("endActualSaleTime", endDate + " 23:59:59");
        }

        // 设置时间维度，默认为月
        if (timeUnit == null || timeUnit.isEmpty()) {
            timeUnit = "month";
        }
        params.put("timeUnit", timeUnit);

        // 使用优化的SQL查询直接获取利润分析数据
        List<Map<String, Object>> profitData = inventoryOutDetailMapper.selectProfitAnalysisData(params);

        // 处理查询结果
        List<String> dateList = new ArrayList<>();
        List<BigDecimal> salesData = new ArrayList<>();
        List<BigDecimal> profitDataList = new ArrayList<>();
        List<BigDecimal> costData = new ArrayList<>();

        for (Map<String, Object> row : profitData) {
            String timeLabel = (String) row.get("timeLabel");
            BigDecimal salesAmount = (BigDecimal) row.get("salesAmount");
            BigDecimal profitAmount = (BigDecimal) row.get("profitAmount");

            // 计算成本 = 销售额 - 利润
            BigDecimal costAmount = salesAmount != null && profitAmount != null
                ? salesAmount.subtract(profitAmount)
                : BigDecimal.ZERO;

            dateList.add(timeLabel);
            salesData.add(salesAmount != null ? salesAmount : BigDecimal.ZERO);
            profitDataList.add(profitAmount != null ? profitAmount : BigDecimal.ZERO);
            costData.add(costAmount);
        }

        Map<String, Object> data = new HashMap<>();
        data.put("dateList", dateList);
        data.put("salesData", salesData);
        data.put("costData", costData);
        data.put("profitData", profitDataList);

        return AjaxResult.success(data);
    }

    /**
     * 根据批次号获取进货价
     */
    private BigDecimal getPurchasePriceByBatchNumber(String batchNumber) {
        if (batchNumber == null || batchNumber.isEmpty()) {
            return null;
        }

        // 查询库存记录
        InventoryStock stockQuery = new InventoryStock();
        stockQuery.setBatchNumber(batchNumber);
        List<InventoryStock> stocks = inventoryStockService.selectInventoryStockList(stockQuery);

        if (!stocks.isEmpty()) {
            return stocks.get(0).getPurchasePrice();
        }

        // 如果库存中没有，查询入库明细
        InventoryInDetail inDetailQuery = new InventoryInDetail();
        inDetailQuery.setBatchNumber(batchNumber);
        List<InventoryInDetail> inDetails = inventoryInDetailMapper.selectInventoryInDetailList(inDetailQuery);

        if (!inDetails.isEmpty()) {
            return inDetails.get(0).getPurchasePrice();
        }

        return null;
    }
}
