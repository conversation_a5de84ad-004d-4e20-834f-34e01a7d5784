package com.retail.project.analysis.controller;

import java.util.*;
import java.util.stream.Collectors;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import com.retail.framework.web.controller.BaseController;
import com.retail.framework.web.domain.AjaxResult;
import com.retail.framework.web.page.TableDataInfo;
import com.retail.common.utils.SecurityUtils;
import com.retail.project.retail.domain.InventoryStock;
import com.retail.project.retail.domain.RetailStore;
import com.retail.project.retail.domain.RetailChannel;
import com.retail.project.retail.domain.RetailProducts;
import com.retail.project.retail.service.IInventoryStockService;
import com.retail.project.retail.service.IRetailStoreService;
import com.retail.project.retail.service.IRetailChannelService;
import com.retail.project.retail.service.IRetailProductsService;
import com.retail.project.retail.mapper.InventoryOutDetailMapper;

/**
 * 渠道数据分析Controller
 */
@RestController
@RequestMapping("/analysis/channel")
public class ChannelAnalysisController extends BaseController
{
    private static final Logger log = LoggerFactory.getLogger(ChannelAnalysisController.class);

    @Autowired
    private InventoryOutDetailMapper inventoryOutDetailMapper;

    @Autowired
    private IInventoryStockService inventoryStockService;

    @Autowired
    private IRetailStoreService retailStoreService;

    @Autowired
    private IRetailChannelService retailChannelService;

    @Autowired
    private IRetailProductsService retailProductsService;

    /**
     * 获取渠道统计数据
     */
    @PreAuthorize("@ss.hasPermi('analysis:channel:data')")
    @GetMapping("/statistics")
    public AjaxResult getStatistics(Long channelId, String timeUnit, Long storeId, String beginTime, String endTime)
    {
        Map<String, Object> data = new HashMap<>();

        // 检查用户权限
        Long userId = SecurityUtils.getUserId();
        boolean isAdmin = SecurityUtils.isAdmin(userId);
        boolean isManager = SecurityUtils.hasRole("manager");
        log.info("用户ID: {}, 是否管理员: {}", userId, isAdmin || isManager);

        // 如果不是管理员，需要查询用户所属的渠道
        if (!isAdmin && !isManager) {
            // 使用我们之前实现的方法直接获取用户的渠道
            RetailChannel userChannel = retailChannelService.selectRetailChannelByUserId(userId);
            log.info("用户所属渠道: {}", userChannel);

            if (userChannel == null) {
                return AjaxResult.error("您没有被授权任何渠道");
            }

            // 如果没有指定渠道ID，使用用户授权的渠道
            if (channelId == null) {
                channelId = userChannel.getId();
                log.info("未指定渠道ID，使用用户所属渠道ID: {}", channelId);
            } else {
                // 如果指定了渠道ID，检查用户是否有权限访问该渠道
                if (!channelId.equals(userChannel.getId())) {
                    log.info("用户尝试访问未授权的渠道，指定渠道ID: {}, 用户所属渠道ID: {}", channelId, userChannel.getId());
                    return AjaxResult.error("您没有权限查看该渠道的数据");
                }
            }
        } else {
            log.info("管理员用户，可以查看所有渠道数据");
        }

        // 获取渠道信息
        RetailChannel channel = null;
        if (channelId != null) {
            channel = retailChannelService.selectRetailChannelById(channelId);
            if (channel == null) {
                return AjaxResult.error("渠道不存在");
            }
            data.put("channelName", channel.getChannelName());
        }

        // 获取所有渠道（仅管理员可见）
        if (isAdmin || isManager) {
            List<RetailChannel> channels = retailChannelService.selectRetailChannelList(new RetailChannel());
            List<Map<String, Object>> channelOptions = channels.stream().map(c -> {
                Map<String, Object> option = new HashMap<>();
                option.put("id", c.getId());
                option.put("name", c.getChannelName());
                return option;
            }).collect(Collectors.toList());
            data.put("channelOptions", channelOptions);
        }

        // 获取所有门店选项
        List<RetailStore> stores = retailStoreService.selectRetailStoreList(new RetailStore());
        List<Map<String, Object>> storeOptions = stores.stream().map(s -> {
            Map<String, Object> option = new HashMap<>();
            option.put("id", s.getId());
            option.put("name", s.getStoreName());
            return option;
        }).collect(Collectors.toList());
        data.put("storeOptions", storeOptions);

        // 获取渠道商品库存在所有门店的分布情况
        data.put("storeInventoryDistribution", getStoreInventoryDistribution(channelId));

        // 如果渠道ID为空且不是管理员，则不查询销售数据
        if (channelId == null && !isAdmin && !isManager) {
            log.warn("非管理员用户且渠道ID为空，无法查询销售数据");
            data.put("productSalesData", new ArrayList<>());
            data.put("productInventoryData", new ArrayList<>());
            data.put("timeSalesData", new ArrayList<>());
            return AjaxResult.success(data);
        }

        // 如果是管理员且渠道ID为空，则查询所有渠道的数据
        if ((isAdmin || isManager) && channelId == null) {
            log.info("管理员用户，查询所有渠道的数据");
        }

        // 构建销售数据查询参数
        Map<String, Object> salesParams = new HashMap<>();
        salesParams.put("channelId", channelId);
        salesParams.put("storeId", storeId);
        salesParams.put("beginTime", beginTime);
        salesParams.put("endTime", endTime);
        salesParams.put("timeUnit", timeUnit);

        // 获取渠道商品销售情况（受筛选条件影响）
        log.info("获取渠道商品销售情况，参数: {}", salesParams);
        List<Map<String, Object>> productSalesData = getProductSalesData(salesParams);
        log.info("渠道商品销售情况数据条数: {}", productSalesData.size());
        data.put("productSalesData", productSalesData);

        // 获取渠道不同商品库存统计（不受日期和门店筛选影响）
        log.info("获取渠道不同商品库存统计，渠道ID: {}", channelId);
        List<Map<String, Object>> productInventoryData = getProductInventoryData(channelId);
        log.info("渠道不同商品库存统计数据条数: {}", productInventoryData.size());
        data.put("productInventoryData", productInventoryData);

        // 获取渠道时间销售数据（受筛选条件影响）
        log.info("获取渠道时间销售数据，参数: {}", salesParams);
        List<Map<String, Object>> timeSalesData = getTimeSalesData(salesParams);
        log.info("渠道时间销售数据条数: {}", timeSalesData.size());
        data.put("timeSalesData", timeSalesData);
        data.put("timeUnit", timeUnit != null ? timeUnit : "month");

        return AjaxResult.success(data);
    }

    /**
     * 获取渠道产品销售明细
     */
    @PreAuthorize("@ss.hasPermi('analysis:channel:data')")
    @GetMapping("/productDetails")
    public TableDataInfo getProductDetails(Long channelId, Long storeId, String beginTime, String endTime)
    {
        // 检查用户权限
        Long userId = SecurityUtils.getUserId();
        boolean isAdmin = SecurityUtils.isAdmin(userId);
        boolean isManager = SecurityUtils.hasRole("manager");
        log.info("用户ID: {}, 是否管理员: {}", userId, isAdmin || isManager);

        // 如果不是管理员，需要查询用户所属的渠道
        if (!isAdmin && !isManager) {
            // 使用我们之前实现的方法直接获取用户的渠道
            RetailChannel userChannel = retailChannelService.selectRetailChannelByUserId(userId);
            log.info("用户所属渠道: {}", userChannel);

            if (userChannel == null) {
                log.warn("用户没有被授权任何渠道");
                return getDataTable(new ArrayList<>());
            }

            // 如果没有指定渠道ID，使用用户授权的渠道
            if (channelId == null) {
                channelId = userChannel.getId();
                log.info("未指定渠道ID，使用用户所属渠道ID: {}", channelId);
            } else {
                // 如果指定了渠道ID，检查用户是否有权限访问该渠道
                if (!channelId.equals(userChannel.getId())) {
                    log.info("用户尝试访问未授权的渠道，指定渠道ID: {}, 用户所属渠道ID: {}", channelId, userChannel.getId());
                    return getDataTable(new ArrayList<>());
                }
            }
        } else {
            log.info("管理员用户，可以查看所有渠道数据");
        }

        // 如果渠道ID为空且不是管理员，则不查询销售数据
        if (channelId == null && !isAdmin && !isManager) {
            log.warn("非管理员用户且渠道ID为空，无法查询销售明细数据");
            return getDataTable(new ArrayList<>());
        }

        // 如果是管理员且渠道ID为空，则查询所有渠道的数据
        if ((isAdmin || isManager) && channelId == null) {
            log.info("管理员用户，查询所有渠道的销售明细数据");
        }

        // 开启分页
        startPage();

        // 构建查询参数
        Map<String, Object> params = new HashMap<>();
        params.put("channelId", channelId);
        params.put("storeId", storeId);
        params.put("beginTime", beginTime);
        params.put("endTime", endTime);

        log.info("获取渠道产品销售明细，渠道ID: {}, 门店ID: {}, 开始时间: {}, 结束时间: {}", channelId, storeId, beginTime, endTime);

        // 获取产品销售明细数据
        List<Map<String, Object>> productDetails = getChannelProductSalesDetails(params);

        log.info("查询到的产品销售明细数据条数: {}", productDetails.size());

        return getDataTable(productDetails);
    }

    /**
     * 获取渠道商品库存在所有门店的分布情况
     */
    private List<Map<String, Object>> getStoreInventoryDistribution(Long channelId) {
        log.info("开始获取渠道商品库存在所有门店的分布情况，渠道ID: {}", channelId);

        // 查询条件
        InventoryStock stockQuery = new InventoryStock();
        if (channelId != null) {
            stockQuery.setChannelId(channelId);
            log.info("设置查询条件：渠道ID = {}", channelId);
        } else {
            log.info("未设置渠道ID，查询所有渠道的库存");
        }

        // 查询库存
        List<InventoryStock> stocks = inventoryStockService.selectInventoryStockList(stockQuery);
        log.info("查询到的库存记录数量: {}", stocks.size());

        // 按门店分组统计库存数量
        Map<Long, Integer> quantityByStore = new HashMap<>();
        Map<Long, String> storeNames = new HashMap<>();

        // 获取所有门店
        List<RetailStore> stores = retailStoreService.selectRetailStoreList(new RetailStore());
        log.info("获取到的门店数量: {}", stores.size());
        for (RetailStore store : stores) {
            storeNames.put(store.getId(), store.getStoreName());
        }

        // 统计每个门店的库存数量
        for (InventoryStock stock : stocks) {
            Long storeId = stock.getStoreId();
            if (storeId != null) {
                quantityByStore.put(storeId, quantityByStore.getOrDefault(storeId, 0) + stock.getQuantity());
            }
        }
        log.info("统计到的门店库存数量: {}", quantityByStore.size());

        // 构建返回数据
        List<Map<String, Object>> result = new ArrayList<>();
        for (Map.Entry<Long, Integer> entry : quantityByStore.entrySet()) {
            if (entry.getValue() > 0) { // 只显示有库存的门店
                Map<String, Object> storeData = new HashMap<>();
                storeData.put("storeId", entry.getKey());
                storeData.put("storeName", storeNames.getOrDefault(entry.getKey(), "未知门店"));
                storeData.put("quantity", entry.getValue());
                result.add(storeData);
            }
        }

        // 按库存数量排序
        result.sort((a, b) -> ((Integer) b.get("quantity")).compareTo((Integer) a.get("quantity")));

        return result;
    }

    /**
     * 获取渠道商品销售情况
     */
    private List<Map<String, Object>> getProductSalesData(Map<String, Object> params) {
        log.info("开始获取渠道商品销售情况，参数: {}", params);

        try {
            // 使用Mapper查询按商品分组统计销售数量（支持筛选条件）
            List<Map<String, Object>> productSales = inventoryOutDetailMapper.selectProductSalesQuantityWithFilter(params);
            log.info("查询到的商品销售数据数量: {}", productSales.size());

            // 构建返回数据
            List<Map<String, Object>> result = new ArrayList<>();
            for (Map<String, Object> row : productSales) {
                Map<String, Object> productData = new HashMap<>();
                productData.put("productId", row.get("productId"));
                productData.put("productName", row.get("productName"));
                productData.put("productNumber", row.get("productNumber"));
                productData.put("productImage", row.get("productImage"));
                productData.put("salesQuantity", row.get("totalQuantity"));
                result.add(productData);
                log.info("商品ID: {}, 商品名称: {}, 货号: {}, 销售数量: {}",
                        row.get("productId"), row.get("productName"), row.get("productNumber"), row.get("totalQuantity"));
            }

            // 只返回前10名
            if (result.size() > 10) {
                result = result.subList(0, 10);
            }

            return result;
        } catch (Exception e) {
            log.error("查询商品销售数据时发生错误", e);
            return new ArrayList<>();
        }
    }

    /**
     * 获取渠道不同商品库存统计
     */
    private List<Map<String, Object>> getProductInventoryData(Long channelId) {
        log.info("开始获取渠道不同商品库存统计，渠道ID: {}", channelId);

        // 查询条件
        InventoryStock stockQuery = new InventoryStock();
        if (channelId != null) {
            stockQuery.setChannelId(channelId);
            log.info("设置查询条件：渠道ID = {}", channelId);
        } else {
            log.info("未设置渠道ID，查询所有渠道的库存");
        }

        // 查询库存
        List<InventoryStock> stocks = inventoryStockService.selectInventoryStockList(stockQuery);
        log.info("查询到的库存记录数量: {}", stocks.size());

        // 按商品分组统计库存数量
        Map<Long, Integer> quantityByProduct = new HashMap<>();
        Map<Long, String> productNames = new HashMap<>();
        Map<Long, String> productNumbers = new HashMap<>();
        Map<Long, String> productImages = new HashMap<>();

        // 获取所有商品
        List<RetailProducts> products = retailProductsService.selectRetailProductsList(new RetailProducts());
        log.info("获取到的商品数量: {}", products.size());
        for (RetailProducts product : products) {
            productNames.put(product.getId(), product.getProductName());
            productNumbers.put(product.getId(), product.getProductNumber());
            productImages.put(product.getId(), product.getProductImage());
        }

        // 统计每个商品的库存数量
        for (InventoryStock stock : stocks) {
            Long productId = stock.getProductId();
            if (productId != null) {
                quantityByProduct.put(productId, quantityByProduct.getOrDefault(productId, 0) + stock.getQuantity());
            }
        }
        log.info("统计到的商品库存数量: {}", quantityByProduct.size());

        // 构建返回数据
        List<Map<String, Object>> result = new ArrayList<>();
        for (Map.Entry<Long, Integer> entry : quantityByProduct.entrySet()) {
            if (entry.getValue() > 0) { // 只显示有库存的商品
                Map<String, Object> productData = new HashMap<>();
                productData.put("productId", entry.getKey());
                productData.put("productName", productNames.getOrDefault(entry.getKey(), "未知商品"));
                productData.put("productNumber", productNumbers.getOrDefault(entry.getKey(), ""));
                productData.put("productImage", productImages.getOrDefault(entry.getKey(), ""));
                productData.put("inventoryQuantity", entry.getValue());
                result.add(productData);
            }
        }

        // 按库存数量排序
        result.sort((a, b) -> ((Integer) b.get("inventoryQuantity")).compareTo((Integer) a.get("inventoryQuantity")));

        // 只返回前10名
        if (result.size() > 10) {
            result = result.subList(0, 10);
        }

        return result;
    }

    /**
     * 获取渠道时间销售数据
     */
    private List<Map<String, Object>> getTimeSalesData(Map<String, Object> params) {
        String timeUnit = (String) params.get("timeUnit");
        log.info("开始获取渠道时间销售数据，参数: {}", params);

        // 如果timeUnit为空，默认使用月份
        if (timeUnit == null || timeUnit.isEmpty()) {
            timeUnit = "month";
            params.put("timeUnit", timeUnit);
        }

        // 验证timeUnit是否有效
        if (!Arrays.asList("day", "month", "year").contains(timeUnit)) {
            timeUnit = "month";
            params.put("timeUnit", timeUnit);
        }

        // 获取时间标签列表
        List<String> timeLabels = getTimeLabels(timeUnit);

        // 按时间标签分组统计销售数量
        Map<String, Integer> quantityByTime = new HashMap<>();

        // 初始化每个时间标签的销售数量为0
        for (String label : timeLabels) {
            quantityByTime.put(label, 0);
        }

        try {
            // 使用Mapper查询按时间维度分组统计销售数量（支持筛选条件）
            List<Map<String, Object>> timeSales = inventoryOutDetailMapper.selectTimeSalesQuantityWithFilter(params);
            log.info("查询到的时间销售数据数量: {}", timeSales.size());

            // 将查询结果填充到quantityByTime中
            for (Map<String, Object> row : timeSales) {
                String timeLabel = (String) row.get("timeLabel");
                Number totalQuantity = (Number) row.get("totalQuantity");
                log.info("时间标签: {}, 销售数量: {}", timeLabel, totalQuantity);

                if (quantityByTime.containsKey(timeLabel)) {
                    quantityByTime.put(timeLabel, totalQuantity.intValue());
                }
            }
        } catch (Exception e) {
            log.error("查询时间销售数据时发生错误", e);
        }

        // 打印统计结果
        log.info("时间销售数量统计结果:");
        for (Map.Entry<String, Integer> entry : quantityByTime.entrySet()) {
            log.info("时间标签: {}, 销售数量: {}", entry.getKey(), entry.getValue());
        }

        // 构建返回数据
        List<Map<String, Object>> result = new ArrayList<>();
        for (String label : timeLabels) {
            Map<String, Object> timeData = new HashMap<>();
            timeData.put("timeLabel", label);
            timeData.put("salesQuantity", quantityByTime.get(label));
            result.add(timeData);
        }

        return result;
    }

    /**
     * 获取时间标签列表
     */
    private List<String> getTimeLabels(String timeUnit) {
        List<String> labels = new ArrayList<>();
        Calendar cal = Calendar.getInstance();

        switch (timeUnit) {
            case "day":
                // 获取最近30天的日期列表
                for (int i = 29; i >= 0; i--) {
                    Calendar tempCal = Calendar.getInstance();
                    tempCal.add(Calendar.DAY_OF_MONTH, -i);
                    String dayLabel = String.format("%d-%02d-%02d",
                            tempCal.get(Calendar.YEAR),
                            tempCal.get(Calendar.MONTH) + 1,
                            tempCal.get(Calendar.DAY_OF_MONTH));
                    labels.add(dayLabel);
                }
                break;
            case "year":
                // 获取最近5年的年份列表
                int currentYear = cal.get(Calendar.YEAR);
                for (int i = 4; i >= 0; i--) {
                    labels.add(String.valueOf(currentYear - i));
                }
                break;
            case "month":
            default:
                // 获取最近12个月的月份列表
                int currentMonth = cal.get(Calendar.MONTH);
                int year = cal.get(Calendar.YEAR);

                for (int i = 11; i >= 0; i--) {
                    int month = currentMonth - i;
                    int yearOffset = 0;

                    // 处理月份小于0的情况
                    while (month < 0) {
                        month += 12;
                        yearOffset -= 1;
                    }

                    String monthLabel = String.format("%d-%02d", year + yearOffset, month + 1);
                    labels.add(monthLabel);
                }
                break;
        }

        return labels;
    }

    /**
     * 获取渠道产品销售明细
     */
    private List<Map<String, Object>> getChannelProductSalesDetails(Map<String, Object> params) {
        log.info("开始获取渠道产品销售明细，参数: {}", params);

        try {
            // 使用Mapper查询产品销售明细
            List<Map<String, Object>> productDetails = inventoryOutDetailMapper.selectChannelProductSalesDetails(params);
            log.info("查询到的产品销售明细数据数量: {}", productDetails.size());

            return productDetails;
        } catch (Exception e) {
            log.error("查询产品销售明细时发生错误", e);
            return new ArrayList<>();
        }
    }

    /**
     * 获取用户的渠道ID
     */
    private Long getUserChannelId(Long userId) {
        // 这里可以根据实际业务逻辑获取用户的渠道ID
        // 暂时返回null，表示需要管理员权限
        return null;
    }

    /**
     * 获取渠道名称
     */
    private String getChannelName(Long channelId) {
        if (channelId == null) {
            return "全部渠道";
        }

        try {
            RetailChannel channel = retailChannelService.selectRetailChannelById(channelId);
            return channel != null ? channel.getChannelName() : "未知渠道";
        } catch (Exception e) {
            log.error("获取渠道名称时发生错误", e);
            return "未知渠道";
        }
    }
}
