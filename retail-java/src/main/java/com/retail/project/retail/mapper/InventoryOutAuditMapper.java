package com.retail.project.retail.mapper;

import java.util.List;
import com.retail.project.retail.domain.InventoryOutAudit;

/**
 * 出库单审核记录Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-04-27
 */
public interface InventoryOutAuditMapper 
{
    /**
     * 查询出库单审核记录
     * 
     * @param id 出库单审核记录主键
     * @return 出库单审核记录
     */
    public InventoryOutAudit selectInventoryOutAuditById(Long id);

    /**
     * 查询出库单审核记录列表
     * 
     * @param inventoryOutAudit 出库单审核记录
     * @return 出库单审核记录集合
     */
    public List<InventoryOutAudit> selectInventoryOutAuditList(InventoryOutAudit inventoryOutAudit);

    /**
     * 根据出库单ID查询审核记录列表
     * 
     * @param orderId 出库单ID
     * @return 出库单审核记录集合
     */
    public List<InventoryOutAudit> selectInventoryOutAuditByOrderId(Long orderId);

    /**
     * 新增出库单审核记录
     * 
     * @param inventoryOutAudit 出库单审核记录
     * @return 结果
     */
    public int insertInventoryOutAudit(InventoryOutAudit inventoryOutAudit);

    /**
     * 修改出库单审核记录
     * 
     * @param inventoryOutAudit 出库单审核记录
     * @return 结果
     */
    public int updateInventoryOutAudit(InventoryOutAudit inventoryOutAudit);

    /**
     * 删除出库单审核记录
     * 
     * @param id 出库单审核记录主键
     * @return 结果
     */
    public int deleteInventoryOutAuditById(Long id);

    /**
     * 批量删除出库单审核记录
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteInventoryOutAuditByIds(Long[] ids);
} 