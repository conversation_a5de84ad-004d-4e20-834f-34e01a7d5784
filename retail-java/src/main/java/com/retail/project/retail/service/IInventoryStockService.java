package com.retail.project.retail.service;

import java.util.List;
import com.retail.project.retail.domain.InventoryStock;
import com.retail.project.retail.domain.BatchStockRequest;

/**
 * 库存Service接口
 *
 * <AUTHOR>
 * @date 2025-04-17
 */
public interface IInventoryStockService
{
    /**
     * 查询库存
     *
     * @param id 库存主键
     * @return 库存
     */
    public InventoryStock selectInventoryStockById(Long id);

    /**
     * 查询库存列表
     *
     * @param inventoryStock 库存
     * @return 库存集合
     */
    public List<InventoryStock> selectInventoryStockList(InventoryStock inventoryStock);

    /**
     * 查询商品在指定门店柜台渠道的库存列表
     *
     * @param productId 商品ID
     * @param storeId 门店ID
     * @param counterId 柜台ID
     * @param channelId 渠道ID
     * @return 库存集合
     */
    public List<InventoryStock> selectInventoryStockByLocation(Long productId, Long storeId, Long counterId, Long channelId);

    /**
     * 查询商品最早入库的非期货库存记录，按入库时间排序
     *
     * @param inventoryStock 库存查询条件
     * @return 库存集合
     */
    public List<InventoryStock> selectNonFuturesInventoryStockByProductId(InventoryStock inventoryStock);

    /**
     * 新增库存
     *
     * @param inventoryStock 库存
     * @return 结果
     */
    public int insertInventoryStock(InventoryStock inventoryStock);

    /**
     * 修改库存
     *
     * @param inventoryStock 库存
     * @return 结果
     */
    public int updateInventoryStock(InventoryStock inventoryStock);

    /**
     * 批量删除库存
     *
     * @param ids 需要删除的库存主键集合
     * @return 结果
     */
    public int deleteInventoryStockByIds(Long[] ids);

    /**
     * 删除库存信息
     *
     * @param id 库存主键
     * @return 结果
     */
    public int deleteInventoryStockById(Long id);

    /**
     * 批量删除库存
     *
     * @param request 批量删除请求
     * @return 结果
     */
    public int batchDeleteStock(BatchStockRequest request);

    /**
     * 批量迁移库存
     *
     * @param request 批量迁移请求
     * @return 结果
     */
    public int batchMigrateStock(BatchStockRequest request);
}