package com.retail.project.retail.controller;

import java.util.List;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.retail.framework.aspectj.lang.annotation.Log;
import com.retail.framework.aspectj.lang.enums.BusinessType;
import com.retail.project.retail.domain.InventoryOutFlow;
import com.retail.project.retail.service.IInventoryOutFlowService;
import com.retail.framework.web.controller.BaseController;
import com.retail.framework.web.domain.AjaxResult;
import com.retail.common.utils.poi.ExcelUtil;
import com.retail.framework.web.page.TableDataInfo;

/**
 * 出库审核流程Controller
 * 
 * <AUTHOR>
 * @date 2025-04-27
 */
@RestController
@RequestMapping("/retail/outflow")
public class InventoryOutFlowController extends BaseController
{
    @Autowired
    private IInventoryOutFlowService inventoryOutFlowService;

    /**
     * 查询出库审核流程列表
     */
    @PreAuthorize("@ss.hasPermi('retail:outflow:list')")
    @GetMapping("/list")
    public TableDataInfo list(InventoryOutFlow inventoryOutFlow)
    {
        startPage();
        List<InventoryOutFlow> list = inventoryOutFlowService.selectInventoryOutFlowList(inventoryOutFlow);
        return getDataTable(list);
    }

    /**
     * 导出出库审核流程列表
     */
    @PreAuthorize("@ss.hasPermi('retail:outflow:export')")
    @Log(title = "出库审核流程", businessType = BusinessType.EXPORT)
    @GetMapping("/export")
    public AjaxResult export(InventoryOutFlow inventoryOutFlow)
    {
        List<InventoryOutFlow> list = inventoryOutFlowService.selectInventoryOutFlowList(inventoryOutFlow);
        ExcelUtil<InventoryOutFlow> util = new ExcelUtil<InventoryOutFlow>(InventoryOutFlow.class);
        return util.exportExcel(list, "出库审核流程数据");
    }

    /**
     * 获取出库审核流程详细信息
     */
    @PreAuthorize("@ss.hasPermi('retail:outflow:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(inventoryOutFlowService.selectInventoryOutFlowById(id));
    }

    /**
     * 新增出库审核流程
     */
    @PreAuthorize("@ss.hasPermi('retail:outflow:add')")
    @Log(title = "出库审核流程", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody InventoryOutFlow inventoryOutFlow)
    {
        return toAjax(inventoryOutFlowService.insertInventoryOutFlow(inventoryOutFlow));
    }

    /**
     * 修改出库审核流程
     */
    @PreAuthorize("@ss.hasPermi('retail:outflow:edit')")
    @Log(title = "出库审核流程", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody InventoryOutFlow inventoryOutFlow)
    {
        return toAjax(inventoryOutFlowService.updateInventoryOutFlow(inventoryOutFlow));
    }

    /**
     * 删除出库审核流程
     */
    @PreAuthorize("@ss.hasPermi('retail:outflow:remove')")
    @Log(title = "出库审核流程", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(inventoryOutFlowService.deleteInventoryOutFlowByIds(ids));
    }
    
    /**
     * 修改出库审核流程状态
     */
    @PreAuthorize("@ss.hasPermi('retail:outflow:edit')")
    @Log(title = "出库审核流程", businessType = BusinessType.UPDATE)
    @PutMapping("/changeStatus")
    public AjaxResult changeStatus(@RequestBody InventoryOutFlow inventoryOutFlow)
    {
        return toAjax(inventoryOutFlowService.updateInventoryOutFlow(inventoryOutFlow));
    }
} 