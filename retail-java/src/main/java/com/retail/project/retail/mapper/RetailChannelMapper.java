package com.retail.project.retail.mapper;

import java.util.List;
import com.retail.project.retail.domain.RetailChannel;

/**
 * 渠道管理Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-04-17
 */
public interface RetailChannelMapper 
{
    /**
     * 查询渠道管理
     * 
     * @param id 渠道管理主键
     * @return 渠道管理
     */
    public RetailChannel selectRetailChannelById(Long id);

    /**
     * 查询渠道管理列表
     * 
     * @param retailChannel 渠道管理
     * @return 渠道管理集合
     */
    public List<RetailChannel> selectRetailChannelList(RetailChannel retailChannel);

    /**
     * 新增渠道管理
     * 
     * @param retailChannel 渠道管理
     * @return 结果
     */
    public int insertRetailChannel(RetailChannel retailChannel);

    /**
     * 修改渠道管理
     * 
     * @param retailChannel 渠道管理
     * @return 结果
     */
    public int updateRetailChannel(RetailChannel retailChannel);

    /**
     * 删除渠道管理
     * 
     * @param id 渠道管理主键
     * @return 结果
     */
    public int deleteRetailChannelById(Long id);

    /**
     * 批量删除渠道管理
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteRetailChannelByIds(Long[] ids);
}
