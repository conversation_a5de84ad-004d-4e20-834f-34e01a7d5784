package com.retail.project.retail.mapper;

import java.util.List;
import org.apache.ibatis.annotations.Param;
import com.retail.project.retail.domain.InventoryStock;

/**
 * 库存Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-04-17
 */
public interface InventoryStockMapper 
{
    /**
     * 查询库存
     * 
     * @param id 库存主键
     * @return 库存
     */
    public InventoryStock selectInventoryStockById(Long id);

    /**
     * 查询库存列表
     * 
     * @param inventoryStock 库存
     * @return 库存集合
     */
    public List<InventoryStock> selectInventoryStockList(InventoryStock inventoryStock);

    /**
     * 查询商品在指定门店柜台渠道的库存列表
     * 
     * @param productId 商品ID
     * @param storeId 门店ID
     * @param counterId 柜台ID
     * @param channelId 渠道ID
     * @return 库存集合
     */
    public List<InventoryStock> selectInventoryStockByLocation(
        @Param("productId") Long productId, 
        @Param("storeId") Long storeId, 
        @Param("counterId") Long counterId, 
        @Param("channelId") Long channelId);

    /**
     * 新增库存
     * 
     * @param inventoryStock 库存
     * @return 结果
     */
    public int insertInventoryStock(InventoryStock inventoryStock);

    /**
     * 修改库存
     * 
     * @param inventoryStock 库存
     * @return 结果
     */
    public int updateInventoryStock(InventoryStock inventoryStock);

    /**
     * 批量更新指定批次号的库存记录的期货状态
     * 
     * @param batchNumber 批次号
     * @param isFutures 期货状态
     * @return 更新的记录数
     */
    public int updateInventoryStockFuturesStatus(@Param("batchNumber") String batchNumber, @Param("isFutures") Boolean isFutures);

    /**
     * 删除库存
     * 
     * @param id 库存主键
     * @return 结果
     */
    public int deleteInventoryStockById(Long id);

    /**
     * 批量删除库存
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteInventoryStockByIds(Long[] ids);

    /**
     * 查询商品的库存记录，按入库时间排序
     * 
     * @param inventoryStock 库存查询条件
     * @return 库存集合
     */
    public List<InventoryStock> selectStockByProductIdOrderByTime(InventoryStock inventoryStock);
} 