package com.retail.project.retail.domain;

import java.util.List;

/**
 * 合并后的产品库存对象
 *
 * <AUTHOR>
 */
public class MergedProductStock
{
    /** 产品ID */
    private Long productId;

    /** 产品货号 */
    private String productNumber;

    /** 产品名称 */
    private String productName;

    /** 产品图片 */
    private String productImage;

    /** 品牌 */
    private String brand;

    /** 品类 */
    private String category;

    /** 总库存数量 */
    private Integer totalQuantity;

    /** 门店数量 */
    private Integer storeCount;

    /** 是否含有期货 */
    private Boolean hasFutures;

    /** 是否有非期货库存 */
    private Boolean hasNonFuturesStock;

    /** 门店列表 */
    private List<String> stores;

    public Long getProductId() {
        return productId;
    }

    public void setProductId(Long productId) {
        this.productId = productId;
    }

    public String getProductNumber() {
        return productNumber;
    }

    public void setProductNumber(String productNumber) {
        this.productNumber = productNumber;
    }

    public String getProductName() {
        return productName;
    }

    public void setProductName(String productName) {
        this.productName = productName;
    }

    public String getProductImage() {
        return productImage;
    }

    public void setProductImage(String productImage) {
        this.productImage = productImage;
    }

    public String getBrand() {
        return brand;
    }

    public void setBrand(String brand) {
        this.brand = brand;
    }

    public String getCategory() {
        return category;
    }

    public void setCategory(String category) {
        this.category = category;
    }

    public Integer getTotalQuantity() {
        return totalQuantity;
    }

    public void setTotalQuantity(Integer totalQuantity) {
        this.totalQuantity = totalQuantity;
    }

    public Integer getStoreCount() {
        return storeCount;
    }

    public void setStoreCount(Integer storeCount) {
        this.storeCount = storeCount;
    }

    public Boolean getHasFutures() {
        return hasFutures;
    }

    public void setHasFutures(Boolean hasFutures) {
        this.hasFutures = hasFutures;
    }

    public Boolean getHasNonFuturesStock() {
        return hasNonFuturesStock;
    }

    public void setHasNonFuturesStock(Boolean hasNonFuturesStock) {
        this.hasNonFuturesStock = hasNonFuturesStock;
    }

    public List<String> getStores() {
        return stores;
    }

    public void setStores(List<String> stores) {
        this.stores = stores;
    }
}