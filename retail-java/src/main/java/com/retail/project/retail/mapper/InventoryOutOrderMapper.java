package com.retail.project.retail.mapper;

import java.util.List;
import com.retail.project.retail.domain.InventoryOutOrder;

/**
 * 出库单Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-04-17
 */
public interface InventoryOutOrderMapper 
{
    /**
     * 查询出库单
     * 
     * @param id 出库单主键
     * @return 出库单
     */
    public InventoryOutOrder selectInventoryOutOrderById(Long id);

    /**
     * 查询出库单列表
     * 
     * @param inventoryOutOrder 出库单
     * @return 出库单集合
     */
    public List<InventoryOutOrder> selectInventoryOutOrderList(InventoryOutOrder inventoryOutOrder);

    /**
     * 新增出库单
     * 
     * @param inventoryOutOrder 出库单
     * @return 结果
     */
    public int insertInventoryOutOrder(InventoryOutOrder inventoryOutOrder);

    /**
     * 修改出库单
     * 
     * @param inventoryOutOrder 出库单
     * @return 结果
     */
    public int updateInventoryOutOrder(InventoryOutOrder inventoryOutOrder);

    /**
     * 删除出库单
     * 
     * @param id 出库单主键
     * @return 结果
     */
    public int deleteInventoryOutOrderById(Long id);

    /**
     * 批量删除出库单
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteInventoryOutOrderByIds(Long[] ids);
} 