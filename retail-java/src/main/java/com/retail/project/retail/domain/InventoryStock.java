package com.retail.project.retail.domain;

import java.math.BigDecimal;
import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.retail.framework.aspectj.lang.annotation.Excel;
import com.retail.framework.web.domain.BaseEntity;

/**
 * 库存对象 inventory_stock
 * 
 * <AUTHOR>
 * @date 2023-04-25
 */
public class InventoryStock extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 库存ID */
    private Long id;

    /** 商品ID */
    @Excel(name = "商品ID")
    private Long productId;

    /** 商品货号 */
    @Excel(name = "商品货号")
    private String productNumber;

    /** 商品名称 */
    @Excel(name = "商品名称")
    private String productName;

    /** 商品图片 */
    private String productImage;

    /** 品牌 */
    @Excel(name = "品牌")
    private String brand;

    /** 品类 */
    @Excel(name = "品类")
    private String category;

    /** 规格 */
    @Excel(name = "规格")
    private String specification;

    /** 颜色 */
    @Excel(name = "颜色")
    private String color;

    /** 材质 */
    @Excel(name = "材质")
    private String material;

    /** 原产地 */
    @Excel(name = "原产地")
    private String origin;

    /** 执行标准 */
    @Excel(name = "执行标准")
    private String standard;

    /** 安全类别 */
    @Excel(name = "安全类别")
    private String safetyCategory;

    /** 产品等级 */
    @Excel(name = "产品等级")
    private String productGrade;

    /** 门店ID */
    @Excel(name = "门店ID")
    private Long storeId;

    /** 门店名称 */
    @Excel(name = "门店名称")
    private String storeName;

    /** 柜台ID */
    @Excel(name = "柜台ID")
    private Long counterId;

    /** 柜台名称 */
    @Excel(name = "柜台名称")
    private String counterName;

    /** 渠道ID */
    @Excel(name = "渠道ID")
    private Long channelId;

    /** 渠道名称 */
    @Excel(name = "渠道名称")
    private String channelName;

    /** 批次号 */
    @Excel(name = "批次号")
    private String batchNumber;

    /** 期货状态 */
    @Excel(name = "期货状态")
    private Boolean isFutures;

    /** 进货价 */
    @Excel(name = "进货价")
    private BigDecimal purchasePrice;

    /** 国内参考价 */
    @Excel(name = "国内参考价")
    private BigDecimal referencePrice;

    /** 门店零售价 */
    @Excel(name = "门店零售价")
    private BigDecimal retailPrice;

    /** 库存数量 */
    @Excel(name = "库存数量")
    private Integer quantity;

    /** 入库时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "入库时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    /** 最小数量 - 仅用于查询 */
    private Integer minQuantity;

    /** 最大创建时间 - 仅用于查询 */
    private String maxCreateTime;

    public void setId(Long id) 
    {
        this.id = id;
    }

    public Long getId() 
    {
        return id;
    }
    
    public void setProductId(Long productId) 
    {
        this.productId = productId;
    }

    public Long getProductId() 
    {
        return productId;
    }
    
    public void setProductNumber(String productNumber) 
    {
        this.productNumber = productNumber;
    }

    public String getProductNumber() 
    {
        return productNumber;
    }
    
    public void setProductName(String productName) 
    {
        this.productName = productName;
    }

    public String getProductName() 
    {
        return productName;
    }
    
    public void setProductImage(String productImage) 
    {
        this.productImage = productImage;
    }

    public String getProductImage() 
    {
        return productImage;
    }
    
    public void setBrand(String brand) 
    {
        this.brand = brand;
    }

    public String getBrand() 
    {
        return brand;
    }
    
    public void setCategory(String category) 
    {
        this.category = category;
    }

    public String getCategory() 
    {
        return category;
    }
    
    public void setSpecification(String specification) 
    {
        this.specification = specification;
    }

    public String getSpecification() 
    {
        return specification;
    }
    
    public void setColor(String color) 
    {
        this.color = color;
    }

    public String getColor() 
    {
        return color;
    }
    
    public void setMaterial(String material) 
    {
        this.material = material;
    }

    public String getMaterial() 
    {
        return material;
    }
    
    public void setOrigin(String origin) 
    {
        this.origin = origin;
    }

    public String getOrigin() 
    {
        return origin;
    }
    
    public void setStandard(String standard) 
    {
        this.standard = standard;
    }

    public String getStandard() 
    {
        return standard;
    }
    
    public void setSafetyCategory(String safetyCategory) 
    {
        this.safetyCategory = safetyCategory;
    }

    public String getSafetyCategory() 
    {
        return safetyCategory;
    }
    
    public void setProductGrade(String productGrade) 
    {
        this.productGrade = productGrade;
    }

    public String getProductGrade() 
    {
        return productGrade;
    }
    
    public void setStoreId(Long storeId) 
    {
        this.storeId = storeId;
    }

    public Long getStoreId() 
    {
        return storeId;
    }
    
    public void setStoreName(String storeName) 
    {
        this.storeName = storeName;
    }

    public String getStoreName() 
    {
        return storeName;
    }
    
    public void setCounterId(Long counterId) 
    {
        this.counterId = counterId;
    }

    public Long getCounterId() 
    {
        return counterId;
    }
    
    public void setCounterName(String counterName) 
    {
        this.counterName = counterName;
    }

    public String getCounterName() 
    {
        return counterName;
    }
    
    public void setChannelId(Long channelId) 
    {
        this.channelId = channelId;
    }

    public Long getChannelId() 
    {
        return channelId;
    }
    
    public void setChannelName(String channelName) 
    {
        this.channelName = channelName;
    }

    public String getChannelName() 
    {
        return channelName;
    }
    
    public void setBatchNumber(String batchNumber) 
    {
        this.batchNumber = batchNumber;
    }

    public String getBatchNumber() 
    {
        return batchNumber;
    }
    
    public void setIsFutures(Boolean isFutures) 
    {
        this.isFutures = isFutures;
    }

    public Boolean getIsFutures() 
    {
        return isFutures;
    }
    
    public void setPurchasePrice(BigDecimal purchasePrice) 
    {
        this.purchasePrice = purchasePrice;
    }

    public BigDecimal getPurchasePrice() 
    {
        return purchasePrice;
    }
    
    public void setReferencePrice(BigDecimal referencePrice) 
    {
        this.referencePrice = referencePrice;
    }

    public BigDecimal getReferencePrice() 
    {
        return referencePrice;
    }
    
    public void setRetailPrice(BigDecimal retailPrice) 
    {
        this.retailPrice = retailPrice;
    }

    public BigDecimal getRetailPrice() 
    {
        return retailPrice;
    }
    
    public void setQuantity(Integer quantity) 
    {
        this.quantity = quantity;
    }

    public Integer getQuantity() 
    {
        return quantity;
    }
    
    public void setCreateTime(Date createTime) 
    {
        this.createTime = createTime;
    }

    public Date getCreateTime()
    {
        return createTime;
    }

    public void setMinQuantity(Integer minQuantity)
    {
        this.minQuantity = minQuantity;
    }

    public Integer getMinQuantity()
    {
        return minQuantity;
    }

    public void setMaxCreateTime(String maxCreateTime)
    {
        this.maxCreateTime = maxCreateTime;
    }

    public String getMaxCreateTime()
    {
        return maxCreateTime;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("productId", getProductId())
            .append("productNumber", getProductNumber())
            .append("productName", getProductName())
            .append("productImage", getProductImage())
            .append("brand", getBrand())
            .append("category", getCategory())
            .append("specification", getSpecification())
            .append("color", getColor())
            .append("material", getMaterial())
            .append("origin", getOrigin())
            .append("standard", getStandard())
            .append("safetyCategory", getSafetyCategory())
            .append("productGrade", getProductGrade())
            .append("storeId", getStoreId())
            .append("storeName", getStoreName())
            .append("counterId", getCounterId())
            .append("counterName", getCounterName())
            .append("channelId", getChannelId())
            .append("channelName", getChannelName())
            .append("batchNumber", getBatchNumber())
            .append("isFutures", getIsFutures())
            .append("purchasePrice", getPurchasePrice())
            .append("referencePrice", getReferencePrice())
            .append("retailPrice", getRetailPrice())
            .append("quantity", getQuantity())
            .append("createTime", getCreateTime())
            .append("createBy", getCreateBy())
            .append("updateBy", getUpdateBy())
            .append("updateTime", getUpdateTime())
            .append("remark", getRemark())
            .toString();
    }
} 