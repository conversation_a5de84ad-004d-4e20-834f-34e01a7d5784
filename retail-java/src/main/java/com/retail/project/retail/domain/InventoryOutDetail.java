package com.retail.project.retail.domain;

import java.math.BigDecimal;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.retail.framework.aspectj.lang.annotation.Excel;
import com.retail.framework.web.domain.BaseEntity;

/**
 * 出库明细对象 inventory_out_detail
 * 
 * <AUTHOR>
 * @date 2025-04-17
 */
public class InventoryOutDetail extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 明细ID */
    private Long id;

    /** 出库单ID */
    @Excel(name = "出库单ID")
    private Long orderId;

    /** 商品ID */
    @Excel(name = "商品ID")
    private Long productId;

    /** 柜台ID */
    @Excel(name = "柜台ID")
    private Long counterId;

    /** 渠道ID */
    @Excel(name = "渠道ID")
    private Long channelId;

    /** 出库数量 */
    @Excel(name = "出库数量")
    private Integer quantity;

    /** 销售价 */
    @Excel(name = "销售价")
    private BigDecimal salePrice;

    /** 系统自动分配的批次号 */
    @Excel(name = "批次号")
    private String batchNumber;

    /** 备注 */
    @Excel(name = "备注")
    private String remark;

    /** 商品名称 */
    @Excel(name = "商品名称")
    private String productName;

    /** 商品货号 */
    @Excel(name = "商品货号")
    private String productNumber;
    
    /** 商品图片 */
    private String productImage;

    /** 渠道名称 */
    @Excel(name = "渠道名称")
    private String channelName;

    /** 柜台名称 */
    @Excel(name = "柜台名称")
    private String counterName;

    public void setId(Long id) 
    {
        this.id = id;
    }

    public Long getId() 
    {
        return id;
    }
    
    public void setOrderId(Long orderId) 
    {
        this.orderId = orderId;
    }

    public Long getOrderId() 
    {
        return orderId;
    }
    
    public void setProductId(Long productId) 
    {
        this.productId = productId;
    }

    public Long getProductId() 
    {
        return productId;
    }
    
    public void setCounterId(Long counterId) 
    {
        this.counterId = counterId;
    }

    public Long getCounterId() 
    {
        return counterId;
    }
    
    public void setChannelId(Long channelId) 
    {
        this.channelId = channelId;
    }

    public Long getChannelId() 
    {
        return channelId;
    }
    
    public void setQuantity(Integer quantity) 
    {
        this.quantity = quantity;
    }

    public Integer getQuantity() 
    {
        return quantity;
    }
    
    public void setSalePrice(BigDecimal salePrice) 
    {
        this.salePrice = salePrice;
    }

    public BigDecimal getSalePrice() 
    {
        return salePrice;
    }
    
    public void setBatchNumber(String batchNumber) 
    {
        this.batchNumber = batchNumber;
    }

    public String getBatchNumber() 
    {
        return batchNumber;
    }
    
    public void setRemark(String remark) 
    {
        this.remark = remark;
    }

    public String getRemark() 
    {
        return remark;
    }
    
    public String getProductName() 
    {
        return productName;
    }

    public void setProductName(String productName) 
    {
        this.productName = productName;
    }
    
    public String getProductNumber() 
    {
        return productNumber;
    }

    public void setProductNumber(String productNumber) 
    {
        this.productNumber = productNumber;
    }
    
    public String getProductImage() 
    {
        return productImage;
    }

    public void setProductImage(String productImage) 
    {
        this.productImage = productImage;
    }
    
    public String getChannelName() 
    {
        return channelName;
    }

    public void setChannelName(String channelName) 
    {
        this.channelName = channelName;
    }
    
    public String getCounterName() 
    {
        return counterName;
    }

    public void setCounterName(String counterName) 
    {
        this.counterName = counterName;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("orderId", getOrderId())
            .append("productId", getProductId())
            .append("counterId", getCounterId())
            .append("channelId", getChannelId())
            .append("quantity", getQuantity())
            .append("salePrice", getSalePrice())
            .append("batchNumber", getBatchNumber())
            .append("productImage", getProductImage())
            .append("createBy", getCreateBy())
            .append("createTime", getCreateTime())
            .append("updateBy", getUpdateBy())
            .append("updateTime", getUpdateTime())
            .append("remark", getRemark())
            .toString();
    }
} 