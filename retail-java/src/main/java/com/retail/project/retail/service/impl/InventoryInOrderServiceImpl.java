package com.retail.project.retail.service.impl;

import java.util.Date;
import java.util.List;
import java.util.UUID;
import java.util.Random;
import java.util.Map;
import java.util.HashMap;
import java.util.Set;
import java.util.HashSet;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import com.retail.project.retail.mapper.InventoryInOrderMapper;
import com.retail.project.retail.mapper.InventoryInDetailMapper;
import com.retail.project.retail.mapper.InventoryStockMapper;
import com.retail.project.retail.domain.InventoryInOrder;
import com.retail.project.retail.domain.InventoryInDetail;
import com.retail.project.retail.domain.InventoryStock;
import com.retail.project.retail.service.IInventoryInOrderService;
import com.retail.project.retail.domain.RetailProducts;
import com.retail.project.retail.service.IRetailProductsService;

/**
 * 入库单Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-04-17
 */
@Service
public class InventoryInOrderServiceImpl implements IInventoryInOrderService 
{
    @Autowired
    private InventoryInOrderMapper inventoryInOrderMapper;
    
    @Autowired
    private InventoryInDetailMapper inventoryInDetailMapper;
    
    @Autowired
    private InventoryStockMapper inventoryStockMapper;

    @Autowired
    private IRetailProductsService retailProductsService;

    /**
     * 查询入库单
     * 
     * @param id 入库单主键
     * @return 入库单
     */
    @Override
    public InventoryInOrder selectInventoryInOrderById(Long id)
    {
        InventoryInOrder order = inventoryInOrderMapper.selectInventoryInOrderById(id);
        if (order != null) {
            // 查询入库明细
            List<InventoryInDetail> detailList = inventoryInDetailMapper.selectInventoryInDetailByOrderId(id);
            order.setDetailList(detailList);
        }
        return order;
    }

    /**
     * 查询入库单列表
     * 
     * @param inventoryInOrder 入库单
     * @return 入库单
     */
    @Override
    public List<InventoryInOrder> selectInventoryInOrderList(InventoryInOrder inventoryInOrder)
    {
        return inventoryInOrderMapper.selectInventoryInOrderList(inventoryInOrder);
    }

    /**
     * 新增入库单
     * 
     * @param inventoryInOrder 入库单
     * @return 结果
     */
    @Override
    public int insertInventoryInOrder(InventoryInOrder inventoryInOrder)
    {
        // 处理BaseEntity的字段
        inventoryInOrder.setCreateTime(new Date());
        
        // 始终生成新的入库单号，忽略前端传入的值
        // 生成10位数的随机数并加上前缀
        String orderNumber = generateOrderNumber();
        inventoryInOrder.setOrderNumber(orderNumber);
        
        return inventoryInOrderMapper.insertInventoryInOrder(inventoryInOrder);
    }
    
    /**
     * 生成入库单号
     * 格式：IN + 10位随机数
     * @return 入库单号
     */
    private String generateOrderNumber() {
        StringBuilder sb = new StringBuilder("IN");
        Random random = new Random();
        // 生成10位随机数
        for (int i = 0; i < 10; i++) {
            sb.append(random.nextInt(10));
        }
        return sb.toString();
    }

    /**
     * 修改入库单
     * 
     * @param inventoryInOrder 入库单
     * @return 结果
     */
    @Override
    @Transactional
    public int updateInventoryInOrder(InventoryInOrder inventoryInOrder)
    {
        // 防止修改入库单号
        inventoryInOrder.setOrderNumber(null);
        
        // 自动填充更新时间
        inventoryInOrder.setUpdateTime(new Date());
        
        // 查询原入库单信息
        InventoryInOrder originalOrder = inventoryInOrderMapper.selectInventoryInOrderById(inventoryInOrder.getId());
        if (originalOrder != null) {
            // 检查期货状态是否变更
            boolean isFuturesChanged = originalOrder.getIsFutures() != null && inventoryInOrder.getIsFutures() != null && 
                !originalOrder.getIsFutures().equals(inventoryInOrder.getIsFutures());
            
            // 查询该入库单对应的所有原入库明细
            List<InventoryInDetail> originalDetailList = inventoryInDetailMapper.selectInventoryInDetailByOrderId(inventoryInOrder.getId());
            
            // 当期货状态变更时，强制更新所有相关库存记录的期货状态
            if (isFuturesChanged && originalDetailList != null && !originalDetailList.isEmpty()) {
                System.out.println("期货状态变更: 从 " + originalOrder.getIsFutures() + " 到 " + inventoryInOrder.getIsFutures());
                
                // 收集所有涉及的批次号
                Set<String> batchNumbers = new HashSet<>();
                for (InventoryInDetail detail : originalDetailList) {
                    if (detail.getBatchNumber() != null && !detail.getBatchNumber().isEmpty()) {
                        batchNumbers.add(detail.getBatchNumber());
                    }
                }
                
                // 对每个批次号，直接使用SQL更新期货状态，绕过MyBatis的对象映射
                for (String batchNumber : batchNumbers) {
                    System.out.println("批量更新批次 " + batchNumber + " 的期货状态为 " + inventoryInOrder.getIsFutures());
                    int updatedCount = inventoryStockMapper.updateInventoryStockFuturesStatus(batchNumber, inventoryInOrder.getIsFutures());
                    System.out.println("批量更新结果: 更新了 " + updatedCount + " 条记录");
                }
            }
            
            // 处理明细数据
            if (inventoryInOrder.getDetailList() != null && !inventoryInOrder.getDetailList().isEmpty()) {
                // 查询原有的明细记录
                List<InventoryInDetail> newDetailList = inventoryInOrder.getDetailList();
                
                // 构建原明细和库存的映射关系
                Map<Long, InventoryStock> stockMap = new HashMap<>();
                if (originalDetailList != null && !originalDetailList.isEmpty()) {
                    for (InventoryInDetail originalDetail : originalDetailList) {
                        // 通过批次号和商品ID查询库存记录
                        if (originalDetail.getBatchNumber() != null && originalDetail.getProductId() != null) {
                            InventoryStock stockParam = new InventoryStock();
                            stockParam.setBatchNumber(originalDetail.getBatchNumber());
                            stockParam.setProductId(originalDetail.getProductId());
                            
                            List<InventoryStock> stockList = inventoryStockMapper.selectInventoryStockList(stockParam);
                            if (stockList != null && !stockList.isEmpty()) {
                                // 取第一条库存记录
                                stockMap.put(originalDetail.getId(), stockList.get(0));
                            }
                        }
                    }
                }
                
                // 处理明细列表
                if (newDetailList != null && !newDetailList.isEmpty()) {
                    for (InventoryInDetail detail : newDetailList) {
                        // 设置updateBy
                        detail.setUpdateBy(inventoryInOrder.getUpdateBy());
                        
                        // 如果明细已存在ID，说明是更新现有记录
                        if (detail.getId() != null) {
                            // 查询原明细记录
                            InventoryInDetail originalDetail = null;
                            for (InventoryInDetail od : originalDetailList) {
                                if (od.getId().equals(detail.getId())) {
                                    originalDetail = od;
                                    break;
                                }
                            }
                            
                            if (originalDetail != null) {
                                // 保留原始批次号
                                detail.setBatchNumber(originalDetail.getBatchNumber());
                                
                                // 查询商品信息，获取商品图片
                                if (detail.getProductId() != null) {
                                    RetailProducts product = retailProductsService.selectRetailProductsById(detail.getProductId());
                                    if (product != null) {
                                        if (product.getProductImage() != null) {
                                            detail.setProductImage(product.getProductImage());
                                        }
                                        // 设置商品名称
                                        if (product.getProductName() != null) {
                                            detail.setProductName(product.getProductName());
                                        } else {
                                            detail.setProductName(product.getProductNumber());
                                        }
                                    }
                                }
                                
                                // 更新明细记录
                                inventoryInDetailMapper.updateInventoryInDetail(detail);
                                
                                // 更新对应的库存记录
                                InventoryStock stock = stockMap.get(detail.getId());
                                if (stock != null) {
                                    // 更新明细对应的库存
                                    System.out.println("更新明细对应的库存: ID=" + stock.getId() + ", 设置期货状态=" + inventoryInOrder.getIsFutures());
                                    
                                    // 更新库存记录
                                    InventoryStock updateStock = new InventoryStock();
                                    updateStock.setId(stock.getId());
                                    updateStock.setProductId(detail.getProductId());
                                    updateStock.setChannelId(inventoryInOrder.getChannelId());
                                    updateStock.setStoreId(detail.getStoreId());
                                    updateStock.setCounterId(detail.getCounterId());
                                    updateStock.setPurchasePrice(detail.getPurchasePrice());
                                    updateStock.setQuantity(detail.getQuantity());
                                    // 设置更新人
                                    updateStock.setUpdateBy(inventoryInOrder.getUpdateBy());
                                    // 确保每次更新都设置期货状态
                                    updateStock.setIsFutures(inventoryInOrder.getIsFutures());
                                    
                                    int result = inventoryStockMapper.updateInventoryStock(updateStock);
                                    System.out.println("更新库存结果: " + result);
                                }
                            }
                        } else {
                            // 新增明细，创建新的批次号
                            String newBatchNumber = UUID.randomUUID().toString().replace("-", "").substring(0, 10);
                            detail.setOrderId(inventoryInOrder.getId());
                            detail.setBatchNumber(newBatchNumber);
                            // 设置创建人
                            detail.setCreateBy(inventoryInOrder.getUpdateBy());
                            
                            // 查询商品信息，获取商品图片
                            if (detail.getProductId() != null) {
                                RetailProducts product = retailProductsService.selectRetailProductsById(detail.getProductId());
                                if (product != null) {
                                    if (product.getProductImage() != null) {
                                        detail.setProductImage(product.getProductImage());
                                    }
                                    // 设置商品名称
                                    if (product.getProductName() != null) {
                                        detail.setProductName(product.getProductName());
                                    } else {
                                        detail.setProductName(product.getProductNumber());
                                    }
                                }
                            }
                            
                            // 插入新明细记录
                            inventoryInDetailMapper.insertInventoryInDetail(detail);
                            
                            // 插入新库存记录
                            InventoryStock newStock = new InventoryStock();
                            newStock.setProductId(detail.getProductId());
                            newStock.setChannelId(inventoryInOrder.getChannelId());
                            newStock.setStoreId(detail.getStoreId());
                            newStock.setCounterId(detail.getCounterId());
                            newStock.setBatchNumber(newBatchNumber);
                            newStock.setPurchasePrice(detail.getPurchasePrice());
                            newStock.setQuantity(detail.getQuantity());
                            newStock.setProductImage(detail.getProductImage());
                            // 设置创建人
                            newStock.setCreateBy(inventoryInOrder.getUpdateBy());
                            // 设置期货状态
                            newStock.setIsFutures(inventoryInOrder.getIsFutures());
                            System.out.println("新增库存记录: 设置期货状态=" + inventoryInOrder.getIsFutures());
                            
                            inventoryStockMapper.insertInventoryStock(newStock);
                        }
                    }
                    
                    // 处理已删除的明细
                    for (InventoryInDetail originalDetail : originalDetailList) {
                        boolean found = false;
                        for (InventoryInDetail newDetail : newDetailList) {
                            if (newDetail.getId() != null && newDetail.getId().equals(originalDetail.getId())) {
                                found = true;
                                break;
                            }
                        }
                        
                        if (!found) {
                            // 删除明细记录
                            inventoryInDetailMapper.deleteInventoryInDetailById(originalDetail.getId());
                            
                            // 删除对应的库存记录
                            InventoryStock stock = stockMap.get(originalDetail.getId());
                            if (stock != null) {
                                inventoryStockMapper.deleteInventoryStockById(stock.getId());
                            }
                        }
                    }
                } else {
                    // 如果新的明细列表为空，删除所有原有明细和对应的库存记录
                    for (InventoryInDetail originalDetail : originalDetailList) {
                        InventoryStock stockParam = new InventoryStock();
                        stockParam.setBatchNumber(originalDetail.getBatchNumber());
                        stockParam.setProductId(originalDetail.getProductId());
                        List<InventoryStock> stocks = inventoryStockMapper.selectInventoryStockList(stockParam);
                        for (InventoryStock stock : stocks) {
                            inventoryStockMapper.deleteInventoryStockById(stock.getId());
                        }
                    }
                    
                    // 删除所有原有明细
                    inventoryInDetailMapper.deleteInventoryInDetailByOrderId(inventoryInOrder.getId());
                }
            }
        }
        
        return inventoryInOrderMapper.updateInventoryInOrder(inventoryInOrder);
    }

    /**
     * 批量删除入库单
     * 
     * @param ids 需要删除的入库单主键
     * @return 结果
     */
    @Override
    @Transactional
    public int deleteInventoryInOrderByIds(Long[] ids)
    {
        for (Long id : ids) {
            deleteInventoryInOrderById(id);
        }
        return ids.length;
    }

    /**
     * 删除入库单信息
     * 
     * @param id 入库单主键
     * @return 结果
     */
    @Override
    @Transactional
    public int deleteInventoryInOrderById(Long id)
    {
        // 查询入库单对应的明细
        List<InventoryInDetail> detailList = inventoryInDetailMapper.selectInventoryInDetailByOrderId(id);
        
        if (detailList != null && !detailList.isEmpty()) {
            // 删除对应的库存记录
            for (InventoryInDetail detail : detailList) {
                // 通过批次号和商品ID查询库存记录
                if (detail.getBatchNumber() != null && detail.getProductId() != null) {
                    InventoryStock stockParam = new InventoryStock();
                    stockParam.setBatchNumber(detail.getBatchNumber());
                    stockParam.setProductId(detail.getProductId());
                    
                    List<InventoryStock> stockList = inventoryStockMapper.selectInventoryStockList(stockParam);
                    if (stockList != null && !stockList.isEmpty()) {
                        for (InventoryStock stock : stockList) {
                            // 删除库存记录
                            inventoryStockMapper.deleteInventoryStockById(stock.getId());
                        }
                    }
                }
            }
        }
        
        // 删除明细
        inventoryInDetailMapper.deleteInventoryInDetailByOrderId(id);
        // 删除入库单
        return inventoryInOrderMapper.deleteInventoryInOrderById(id);
    }
    
    /**
     * 保存入库单及明细
     * 
     * @param inventoryInOrder 入库单
     * @param detailList 入库明细列表
     * @return 结果
     */
    @Override
    @Transactional
    public int saveInventoryInOrderWithDetails(InventoryInOrder inventoryInOrder, List<InventoryInDetail> detailList)
    {
        // 保存入库单
        int rows = insertInventoryInOrder(inventoryInOrder);
        if (rows > 0 && detailList != null && !detailList.isEmpty()) {
            // 设置批次号
            String batchNumber = UUID.randomUUID().toString().replace("-", "").substring(0, 10);
            
            // 保存入库明细
            for (InventoryInDetail detail : detailList) {
                detail.setOrderId(inventoryInOrder.getId());
                detail.setBatchNumber(batchNumber);
                // 设置创建人
                detail.setCreateBy(inventoryInOrder.getCreateBy());
                
                // 查询商品信息，获取商品图片
                if (detail.getProductId() != null) {
                    // 假设有一个通过商品ID获取商品详情的方法
                    RetailProducts product = retailProductsService.selectRetailProductsById(detail.getProductId());
                    if (product != null) {
                        if (product.getProductImage() != null) {
                            detail.setProductImage(product.getProductImage());
                        }
                        // 设置商品名称
                        if (product.getProductName() != null) {
                            detail.setProductName(product.getProductName());
                        } else {
                            detail.setProductName(product.getProductNumber());
                        }
                    }
                }
                
                inventoryInDetailMapper.insertInventoryInDetail(detail);
                
                // 更新库存
                InventoryStock stock = new InventoryStock();
                stock.setProductId(detail.getProductId());
                stock.setChannelId(inventoryInOrder.getChannelId());
                stock.setStoreId(detail.getStoreId());
                stock.setCounterId(detail.getCounterId());
                stock.setBatchNumber(batchNumber);
                stock.setPurchasePrice(detail.getPurchasePrice());
                stock.setQuantity(detail.getQuantity());
                stock.setProductImage(detail.getProductImage());
                // 设置创建人
                stock.setCreateBy(inventoryInOrder.getCreateBy());
                // 设置期货状态
                stock.setIsFutures(inventoryInOrder.getIsFutures());
                
                inventoryStockMapper.insertInventoryStock(stock);
            }
        }
        
        return rows;
    }
}