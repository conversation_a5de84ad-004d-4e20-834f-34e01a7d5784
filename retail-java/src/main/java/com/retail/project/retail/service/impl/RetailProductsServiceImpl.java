package com.retail.project.retail.service.impl;

import java.util.Date;
import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.retail.project.retail.mapper.RetailProductsMapper;
import com.retail.project.retail.domain.RetailProducts;
import com.retail.project.retail.service.IRetailProductsService;

/**
 * 商品Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-04-17
 */
@Service
public class RetailProductsServiceImpl implements IRetailProductsService 
{
    @Autowired
    private RetailProductsMapper retailProductsMapper;

    /**
     * 查询商品
     * 
     * @param id 商品主键
     * @return 商品
     */
    @Override
    public RetailProducts selectRetailProductsById(Long id)
    {
        return retailProductsMapper.selectRetailProductsById(id);
    }

    /**
     * 查询商品列表
     * 
     * @param retailProducts 商品
     * @return 商品
     */
    @Override
    public List<RetailProducts> selectRetailProductsList(RetailProducts retailProducts)
    {
        return retailProductsMapper.selectRetailProductsList(retailProducts);
    }

    /**
     * 新增商品
     * 
     * @param retailProducts 商品
     * @return 结果
     */
    @Override
    public int insertRetailProducts(RetailProducts retailProducts)
    {
        retailProducts.setCreateTime(new Date());
        return retailProductsMapper.insertRetailProducts(retailProducts);
    }

    /**
     * 修改商品
     * 
     * @param retailProducts 商品
     * @return 结果
     */
    @Override
    public int updateRetailProducts(RetailProducts retailProducts)
    {
        retailProducts.setUpdateTime(new Date());
        return retailProductsMapper.updateRetailProducts(retailProducts);
    }

    /**
     * 批量删除商品
     * 
     * @param ids 需要删除的商品主键
     * @return 结果
     */
    @Override
    public int deleteRetailProductsByIds(Long[] ids)
    {
        return retailProductsMapper.deleteRetailProductsByIds(ids);
    }

    /**
     * 删除商品信息
     * 
     * @param id 商品主键
     * @return 结果
     */
    @Override
    public int deleteRetailProductsById(Long id)
    {
        return retailProductsMapper.deleteRetailProductsById(id);
    }
}
