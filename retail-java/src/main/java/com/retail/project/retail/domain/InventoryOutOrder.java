package com.retail.project.retail.domain;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.retail.framework.aspectj.lang.annotation.Excel;
import com.retail.framework.web.domain.BaseEntity;

/**
 * 出库单对象 inventory_out_order
 *
 * <AUTHOR>
 * @date 2025-04-17
 */
public class InventoryOutOrder extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 出库单ID */
    private Long id;

    /** 出库单号 */
    @Excel(name = "出库单号")
    private String orderNumber;

    /** 订单类型（0售出单 1预订单） */
    @Excel(name = "订单类型", readConverterExp = "0=售出单,1=预订单")
    private Integer orderType;

    /** 会员ID */
    @Excel(name = "会员ID")
    private Long memberId;

    /** 会员姓名 - 仅用于查询 */
    @Excel(name = "会员姓名")
    private String memberName;

    /** 会员手机号 - 仅用于查询 */
    @Excel(name = "会员手机号")
    private String phoneNumber;

    /** 会员性别 - 仅用于查询 */
    @Excel(name = "会员性别")
    private String gender;

    /** 会员等级 - 仅用于查询 */
    @Excel(name = "会员等级", readConverterExp = "1=普通会员,2=银卡会员,3=金卡会员,4=VIP会员,5=钻石会员")
    private Integer memberLevel;

    /** 门店ID */
    @Excel(name = "门店ID")
    private Long storeId;

    /** 门店名称 */
    @Excel(name = "门店名称")
    private String storeName;

    /** 商品货号 - 仅用于查询 */
    private String productNumber;

    /** 出库总金额 */
    @Excel(name = "出库总金额")
    private BigDecimal totalAmount;

    /** 支付方式 */
    @Excel(name = "支付方式", readConverterExp = "")
    private Integer payType;

    /** 预订支付类型（0定金 1全款） */
    @Excel(name = "预订支付类型", readConverterExp = "0=定金,1=全款")
    private Integer depositType;

    /** 预订定金金额 */
    @Excel(name = "预订定金金额")
    private BigDecimal depositAmount;

    /** 预订尾款金额 */
    @Excel(name = "预订尾款金额")
    private BigDecimal balanceAmount;

    /** 尾款支付方式 */
    @Excel(name = "尾款支付方式", readConverterExp = "")
    private Integer balancePayType;

    /** 业务场景（0售出 1预订 2退货 3取消 4交货 5库存删除 6库存迁移） */
    @Excel(name = "业务场景", readConverterExp = "0=售出,1=预订,2=退货,3=取消,4=交货,5=库存删除,6=库存迁移")
    private Integer businessType;

    /** 实际售出时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "实际售出时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date actualSaleTime;

    /** 业务场景列表 - 用于查询条件 */
    private List<Integer> businessTypeList;

    /** 业务场景列表 - 用于查询条件 */
    private List<Integer> excludeBusinessTypes;

    /** 流程节点（0待审核 1已审核 2已驳回 3已撤销） */
    @Excel(name = "流程节点", readConverterExp = "0待审核 1已审核 2已驳回 3已撤销")
    private Integer processStatus;

    /** 出库明细列表 */
    private List<InventoryOutDetail> detailList;

    /** 审核记录列表 */
    private List<InventoryOutAudit> auditList;

    public void setId(Long id)
    {
        this.id = id;
    }

    public Long getId()
    {
        return id;
    }

    public void setOrderNumber(String orderNumber)
    {
        this.orderNumber = orderNumber;
    }

    public String getOrderNumber()
    {
        return orderNumber;
    }



    public Long getMemberId()
    {
        return memberId;
    }

    public void setMemberId(Long memberId)
    {
        this.memberId = memberId;
    }

    public String getMemberName()
    {
        return memberName;
    }

    public void setMemberName(String memberName)
    {
        this.memberName = memberName;
    }

    public String getPhoneNumber()
    {
        return phoneNumber;
    }

    public void setPhoneNumber(String phoneNumber)
    {
        this.phoneNumber = phoneNumber;
    }

    public String getGender()
    {
        return gender;
    }

    public void setGender(String gender)
    {
        this.gender = gender;
    }

    public Integer getMemberLevel()
    {
        return memberLevel;
    }

    public void setMemberLevel(Integer memberLevel)
    {
        this.memberLevel = memberLevel;
    }

    public void setStoreId(Long storeId)
    {
        this.storeId = storeId;
    }

    public Long getStoreId()
    {
        return storeId;
    }

    public String getStoreName()
    {
        return storeName;
    }

    public void setStoreName(String storeName)
    {
        this.storeName = storeName;
    }

    public String getProductNumber()
    {
        return productNumber;
    }

    public void setProductNumber(String productNumber)
    {
        this.productNumber = productNumber;
    }

    public BigDecimal getTotalAmount()
    {
        return totalAmount;
    }

    public void setTotalAmount(BigDecimal totalAmount)
    {
        this.totalAmount = totalAmount;
    }

    public Integer getPayType()
    {
        return payType;
    }

    public void setPayType(Integer payType)
    {
        this.payType = payType;
    }



    public Integer getBusinessType()
    {
        return businessType;
    }

    public void setBusinessType(Integer businessType)
    {
        this.businessType = businessType;
    }

    public Date getActualSaleTime()
    {
        return actualSaleTime;
    }

    public void setActualSaleTime(Date actualSaleTime)
    {
        this.actualSaleTime = actualSaleTime;
    }

    public List<Integer> getBusinessTypeList()
    {
        return businessTypeList;
    }

    public void setBusinessTypeList(List<Integer> businessTypeList)
    {
        this.businessTypeList = businessTypeList;
    }

    public List<Integer> getExcludeBusinessTypes()
    {
        return excludeBusinessTypes;
    }

    public void setExcludeBusinessTypes(List<Integer> excludeBusinessTypes)
    {
        this.excludeBusinessTypes = excludeBusinessTypes;
    }

    public Integer getProcessStatus()
    {
        return processStatus;
    }

    public void setProcessStatus(Integer processStatus)
    {
        this.processStatus = processStatus;
    }

    public Integer getOrderType()
    {
        return orderType;
    }

    public void setOrderType(Integer orderType)
    {
        this.orderType = orderType;
    }

    public Integer getDepositType()
    {
        return depositType;
    }

    public void setDepositType(Integer depositType)
    {
        this.depositType = depositType;
    }

    public BigDecimal getDepositAmount()
    {
        return depositAmount;
    }

    public void setDepositAmount(BigDecimal depositAmount)
    {
        this.depositAmount = depositAmount;
    }

    public BigDecimal getBalanceAmount()
    {
        return balanceAmount;
    }

    public void setBalanceAmount(BigDecimal balanceAmount)
    {
        this.balanceAmount = balanceAmount;
    }

    public Integer getBalancePayType()
    {
        return balancePayType;
    }

    public void setBalancePayType(Integer balancePayType)
    {
        this.balancePayType = balancePayType;
    }

    public List<InventoryOutDetail> getDetailList()
    {
        return detailList;
    }

    public void setDetailList(List<InventoryOutDetail> detailList)
    {
        this.detailList = detailList;
    }

    public List<InventoryOutAudit> getAuditList()
    {
        return auditList;
    }

    public void setAuditList(List<InventoryOutAudit> auditList)
    {
        this.auditList = auditList;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("orderNumber", getOrderNumber())
            .append("orderType", getOrderType())
            .append("memberId", getMemberId())
            .append("memberName", getMemberName())
            .append("phoneNumber", getPhoneNumber())
            .append("gender", getGender())
            .append("memberLevel", getMemberLevel())
            .append("storeId", getStoreId())
            .append("totalAmount", getTotalAmount())
            .append("payType", getPayType())
            .append("depositType", getDepositType())
            .append("depositAmount", getDepositAmount())
            .append("balanceAmount", getBalanceAmount())
            .append("balancePayType", getBalancePayType())
            .append("businessType", getBusinessType())
            .append("actualSaleTime", getActualSaleTime())
            .append("processStatus", getProcessStatus())
            .append("createBy", getCreateBy())
            .append("createTime", getCreateTime())
            .append("updateBy", getUpdateBy())
            .append("updateTime", getUpdateTime())
            .append("remark", getRemark())
            .append("storeName", getStoreName())
            .append("detailList", getDetailList())
            .append("auditList", getAuditList())
            .toString();
    }
}