package com.retail.project.retail.mapper;

import java.util.List;
import com.retail.project.retail.domain.InventoryOutFlow;

/**
 * 出库审核流程Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-04-27
 */
public interface InventoryOutFlowMapper 
{
    /**
     * 查询出库审核流程
     * 
     * @param id 出库审核流程主键
     * @return 出库审核流程
     */
    public InventoryOutFlow selectInventoryOutFlowById(Long id);

    /**
     * 查询出库审核流程列表
     * 
     * @param inventoryOutFlow 出库审核流程
     * @return 出库审核流程集合
     */
    public List<InventoryOutFlow> selectInventoryOutFlowList(InventoryOutFlow inventoryOutFlow);

    /**
     * 新增出库审核流程
     * 
     * @param inventoryOutFlow 出库审核流程
     * @return 结果
     */
    public int insertInventoryOutFlow(InventoryOutFlow inventoryOutFlow);

    /**
     * 修改出库审核流程
     * 
     * @param inventoryOutFlow 出库审核流程
     * @return 结果
     */
    public int updateInventoryOutFlow(InventoryOutFlow inventoryOutFlow);

    /**
     * 删除出库审核流程
     * 
     * @param id 出库审核流程主键
     * @return 结果
     */
    public int deleteInventoryOutFlowById(Long id);

    /**
     * 批量删除出库审核流程
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteInventoryOutFlowByIds(Long[] ids);
} 