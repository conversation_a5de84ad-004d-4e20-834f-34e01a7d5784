package com.retail.project.retail.controller;

import java.math.BigDecimal;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.retail.framework.aspectj.lang.annotation.Log;
import com.retail.framework.aspectj.lang.enums.BusinessType;
import com.retail.framework.web.controller.BaseController;
import com.retail.framework.web.domain.AjaxResult;
import com.retail.common.utils.SecurityUtils;
import com.retail.project.retail.domain.InventoryOutDetail;
import com.retail.project.retail.domain.InventoryOutOrder;
import com.retail.project.retail.domain.InventoryStock;
import com.retail.project.retail.domain.enums.ProcessStatus;
import com.retail.project.retail.service.IInventoryOutOrderService;
import com.retail.project.retail.service.IInventoryStockService;
import com.retail.project.retail.mapper.RetailStoreMapper;
import com.retail.project.retail.utils.StorePermissionUtils;
import com.retail.project.member.domain.Member;
import com.retail.project.member.service.IMemberService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * 售出操作控制器
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/retail/outorder")
public class OutOrderController extends BaseController
{
    private static final Logger log = LoggerFactory.getLogger(OutOrderController.class);

    @Autowired
    private IInventoryOutOrderService inventoryOutOrderService;

    @Autowired
    private IInventoryStockService inventoryStockService;

    @Autowired
    private RetailStoreMapper retailStoreMapper;

    @Autowired
    private IMemberService memberService;

    /**
     * 根据手机号码搜索会员
     */
    @PreAuthorize("@ss.hasPermi('retail:storestock:sale')")
    @GetMapping("/search-member")
    public AjaxResult searchMemberByPhone(String phoneNumber)
    {
        if (phoneNumber == null || phoneNumber.isEmpty()) {
            return AjaxResult.error("手机号码不能为空");
        }
        Member member = memberService.selectMemberByPhoneNumber(phoneNumber);
        return AjaxResult.success(member);
    }

    /**
     * 商品售出创建出库单
     */
    @PreAuthorize("@ss.hasPermi('retail:storestock:sale')")
    @Log(title = "商品售出", businessType = BusinessType.INSERT)
    @PostMapping("/sale")
    public AjaxResult sale(@RequestBody SaleRequest saleRequest)
    {
        // 验证必要参数
        if (saleRequest.getStockId() == null) {
            return error("库存ID不能为空");
        }

        if (saleRequest.getSalePrice() == null || saleRequest.getSalePrice().compareTo(BigDecimal.ZERO) <= 0) {
            return error("售价必须大于0");
        }

        // 验证会员ID是否提供
        if (saleRequest.getMemberId() == null) {
            return error("会员ID不能为空");
        }

        // 获取库存记录
        InventoryStock stock = inventoryStockService.selectInventoryStockById(saleRequest.getStockId());
        if (stock == null) {
            return error("库存记录不存在");
        }

        // 获取请求的数量，默认为1
        int requestQuantity = saleRequest.getQuantity() != null ? saleRequest.getQuantity() : 1;

        // 验证库存是否充足
        if (stock.getQuantity() == null || stock.getQuantity() < requestQuantity) {
            return error("库存不足，当前库存: " + stock.getQuantity() + ", 请求数量: " + requestQuantity);
        }

        // 验证是否是期货
        if (stock.getIsFutures() != null && stock.getIsFutures()) {
            return error("期货商品不可售出");
        }

        // 验证当前用户是否有权限操作该库存所属门店
        Long userId = SecurityUtils.getUserId();

        // 检查用户是否有该门店的权限
        boolean hasAuth = StorePermissionUtils.checkUserStorePermission(stock.getStoreId(), userId, retailStoreMapper);
        if (!hasAuth) {
            return error("您没有权限操作此门店的库存");
        }

        try {
            // 创建出库单
            InventoryOutOrder outOrder = new InventoryOutOrder();
            outOrder.setPayType(saleRequest.getPayType());
            outOrder.setStoreId(stock.getStoreId());  // 使用库存所属的门店ID
            outOrder.setCreateBy(SecurityUtils.getUsername());
            outOrder.setRemark(saleRequest.getRemark()); // 设置备注信息
            outOrder.setBusinessType(0); // 设置业务场景为售出 (0=售出)
            outOrder.setProcessStatus(0); // 设置流程状态为待审核 (0=待审核)
            // 设置实际售出时间，如果前端传入了则使用前端的时间，否则使用当前时间
            if (saleRequest.getActualSaleTime() != null && !saleRequest.getActualSaleTime().isEmpty()) {
                try {
                    SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                    Date actualSaleTime = sdf.parse(saleRequest.getActualSaleTime());
                    outOrder.setActualSaleTime(actualSaleTime);
                } catch (ParseException e) {
                    log.warn("解析实际售出时间失败，使用当前时间: " + saleRequest.getActualSaleTime(), e);
                    outOrder.setActualSaleTime(new Date());
                }
            } else {
                outOrder.setActualSaleTime(new Date());
            }

            // 直接使用传入的会员ID
            if (saleRequest.getMemberId() != null) {
                outOrder.setMemberId(saleRequest.getMemberId());
            }

            // 创建出库明细
            List<InventoryOutDetail> detailList = new ArrayList<>();
            InventoryOutDetail detail = new InventoryOutDetail();
            detail.setProductId(stock.getProductId());
            detail.setCounterId(stock.getCounterId());
            detail.setChannelId(stock.getChannelId());
            detail.setQuantity(saleRequest.getQuantity() != null ? saleRequest.getQuantity() : 1); // 使用请求中的数量，默认为1
            detail.setSalePrice(saleRequest.getSalePrice());
            detail.setBatchNumber(stock.getBatchNumber());

            // 从库存记录获取商品名称和货号设置到明细中
            if (stock.getProductName() != null) {
                detail.setProductName(stock.getProductName());
            }
            if (stock.getProductNumber() != null) {
                detail.setProductNumber(stock.getProductNumber());
            }

            detailList.add(detail);

            // 计算总金额
            BigDecimal totalAmount = saleRequest.getSalePrice().multiply(new BigDecimal(detail.getQuantity()));
            outOrder.setTotalAmount(totalAmount);

            // 保存出库单及明细，同时减少库存
            int result = inventoryOutOrderService.saveInventoryOutOrderWithDetails(outOrder, detailList);
            if (result > 0) {
                return success("商品售出成功");
            }
            return error("商品售出失败，请重试");
        } catch (Exception e) {
            log.error("商品售出异常", e);
            return error("商品售出失败：" + e.getMessage());
        }
    }

    /**
     * 批量商品售出创建出库单
     */
    @PreAuthorize("@ss.hasPermi('retail:storestock:sale')")
    @Log(title = "批量商品售出", businessType = BusinessType.INSERT)
    @PostMapping("/batch-sale")
    public AjaxResult batchSale(@RequestBody BatchSaleRequest batchSaleRequest)
    {
        // 验证必要参数
        if (batchSaleRequest.getStocks() == null || batchSaleRequest.getStocks().isEmpty()) {
            return error("商品列表不能为空");
        }

        // 验证会员ID是否提供
        if (batchSaleRequest.getMemberId() == null) {
            return error("会员ID不能为空");
        }

        try {
            // 创建一个出库单
            InventoryOutOrder outOrder = new InventoryOutOrder();
            outOrder.setPayType(batchSaleRequest.getPayType());
            outOrder.setCreateBy(SecurityUtils.getUsername());
            outOrder.setRemark(batchSaleRequest.getRemark());
            outOrder.setBusinessType(0); // 设置业务场景为售出 (0=售出)
            outOrder.setProcessStatus(0); // 设置流程状态为待审核 (0=待审核)
            // 设置实际售出时间，如果前端传入了则使用前端的时间，否则使用当前时间
            if (batchSaleRequest.getActualSaleTime() != null && !batchSaleRequest.getActualSaleTime().isEmpty()) {
                try {
                    SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                    Date actualSaleTime = sdf.parse(batchSaleRequest.getActualSaleTime());
                    outOrder.setActualSaleTime(actualSaleTime);
                } catch (ParseException e) {
                    log.warn("解析实际售出时间失败，使用当前时间: " + batchSaleRequest.getActualSaleTime(), e);
                    outOrder.setActualSaleTime(new Date());
                }
            } else {
                outOrder.setActualSaleTime(new Date());
            }

            // 直接使用传入的会员ID
            if (batchSaleRequest.getMemberId() != null) {
                outOrder.setMemberId(batchSaleRequest.getMemberId());
            }

            // 获取第一个库存的门店ID作为订单的门店ID
            Long firstStockId = batchSaleRequest.getStocks().get(0).getStockId();
            InventoryStock firstStock = inventoryStockService.selectInventoryStockById(firstStockId);
            if (firstStock == null) {
                return error("库存记录不存在");
            }
            outOrder.setStoreId(firstStock.getStoreId());

            // 创建出库明细列表
            List<InventoryOutDetail> detailList = new ArrayList<>();

            for (StockItem stockItem : batchSaleRequest.getStocks()) {
                // 获取库存记录
                InventoryStock stock = inventoryStockService.selectInventoryStockById(stockItem.getStockId());
                if (stock == null) {
                    return error("库存记录不存在");
                }

                // 验证库存是否充足
                if (stock.getQuantity() == null || stock.getQuantity() < stockItem.getQuantity()) {
                    return error("商品库存不足");
                }

                // 验证是否是期货
                if (stock.getIsFutures() != null && stock.getIsFutures()) {
                    return error("期货商品不可售出");
                }

                // 创建出库明细
                InventoryOutDetail detail = new InventoryOutDetail();
                detail.setProductId(stock.getProductId());
                detail.setCounterId(stock.getCounterId());
                detail.setChannelId(stock.getChannelId());
                detail.setQuantity(stockItem.getQuantity()); // 使用用户指定的数量
                detail.setSalePrice(stockItem.getSalePrice());
                detail.setBatchNumber(stock.getBatchNumber());

                // 从库存记录获取商品名称和货号设置到明细中
                if (stock.getProductName() != null) {
                    detail.setProductName(stock.getProductName());
                }
                if (stock.getProductNumber() != null) {
                    detail.setProductNumber(stock.getProductNumber());
                }

                detailList.add(detail);
            }

            // 使用前端传入的总实际售价，并验证价格一致性
            if (batchSaleRequest.getTotalSalePrice() != null && batchSaleRequest.getTotalSalePrice().compareTo(BigDecimal.ZERO) > 0) {
                outOrder.setTotalAmount(batchSaleRequest.getTotalSalePrice());

                // 验证明细价格总和是否与订单总金额一致
                BigDecimal detailTotalAmount = BigDecimal.ZERO;
                for (InventoryOutDetail detail : detailList) {
                    BigDecimal itemAmount = detail.getSalePrice().multiply(new BigDecimal(detail.getQuantity()));
                    detailTotalAmount = detailTotalAmount.add(itemAmount);
                }

                // 允许1分钱的误差（由于精度问题）
                BigDecimal difference = batchSaleRequest.getTotalSalePrice().subtract(detailTotalAmount).abs();
                if (difference.compareTo(new BigDecimal("0.01")) > 0) {
                    log.warn("批量售出价格不一致：订单总金额={}, 明细总金额={}, 差额={}",
                            batchSaleRequest.getTotalSalePrice(), detailTotalAmount, difference);
                    // 不返回错误，而是记录警告并继续处理
                }
            } else {
                // 如果没有传入总实际售价，则计算总和作为备选
                BigDecimal totalAmount = BigDecimal.ZERO;
                for (InventoryOutDetail detail : detailList) {
                    BigDecimal itemAmount = detail.getSalePrice().multiply(new BigDecimal(detail.getQuantity()));
                    totalAmount = totalAmount.add(itemAmount);
                }
                outOrder.setTotalAmount(totalAmount);
            }

            // 保存出库单及明细，同时减少库存
            int result = inventoryOutOrderService.saveInventoryOutOrderWithDetails(outOrder, detailList);
            if (result > 0) {
                return success("批量售出成功");
            }
            return error("批量售出失败，请重试");
        } catch (Exception e) {
            log.error("批量售出异常", e);
            return error("批量售出失败：" + e.getMessage());
        }
    }

    /**
     * 售出请求对象
     */
    public static class SaleRequest {
        private Long stockId;
        private BigDecimal salePrice;
        private Integer payType;
        private String remark; // 添加备注字段
        private Integer quantity = 1; // 添加数量字段，默认为1
        private Long memberId; // 会员ID字段
        private String actualSaleTime; // 实际售出时间字段（字符串格式）

        public Long getStockId() {
            return stockId;
        }
        public void setStockId(Long stockId) {
            this.stockId = stockId;
        }
        public BigDecimal getSalePrice() {
            return salePrice;
        }
        public void setSalePrice(BigDecimal salePrice) {
            this.salePrice = salePrice;
        }
        // 已移除客户信息相关的getter和setter，改为使用会员ID
        public Integer getPayType() {
            return payType;
        }
        public void setPayType(Integer payType) {
            this.payType = payType;
        }
        public String getRemark() {
            return remark;
        }
        public void setRemark(String remark) {
            this.remark = remark;
        }
        public Integer getQuantity() {
            return quantity;
        }
        public void setQuantity(Integer quantity) {
            this.quantity = quantity;
        }
        public Long getMemberId() {
            return memberId;
        }
        public void setMemberId(Long memberId) {
            this.memberId = memberId;
        }
        public String getActualSaleTime() {
            return actualSaleTime;
        }
        public void setActualSaleTime(String actualSaleTime) {
            this.actualSaleTime = actualSaleTime;
        }
    }

    /**
     * 批量售出请求对象
     */
    public static class BatchSaleRequest {
        private List<StockItem> stocks;
        private Integer payType;
        private String remark;
        private Long memberId; // 会员ID字段
        private BigDecimal totalSalePrice; // 总实际售价字段
        private String actualSaleTime; // 实际售出时间字段（字符串格式）

        public List<StockItem> getStocks() {
            return stocks;
        }
        public void setStocks(List<StockItem> stocks) {
            this.stocks = stocks;
        }
        // 已移除客户信息相关的getter和setter，改为使用会员ID
        public Integer getPayType() {
            return payType;
        }
        public void setPayType(Integer payType) {
            this.payType = payType;
        }
        public String getRemark() {
            return remark;
        }
        public void setRemark(String remark) {
            this.remark = remark;
        }
        public Long getMemberId() {
            return memberId;
        }
        public void setMemberId(Long memberId) {
            this.memberId = memberId;
        }
        public BigDecimal getTotalSalePrice() {
            return totalSalePrice;
        }
        public void setTotalSalePrice(BigDecimal totalSalePrice) {
            this.totalSalePrice = totalSalePrice;
        }
        public String getActualSaleTime() {
            return actualSaleTime;
        }
        public void setActualSaleTime(String actualSaleTime) {
            this.actualSaleTime = actualSaleTime;
        }
    }


    /**
     * 库存项对象
     */
    public static class StockItem {
        private Long stockId;
        private Long productId;
        private BigDecimal salePrice;
        private Integer quantity = 1;

        public Long getStockId() {
            return stockId;
        }
        public void setStockId(Long stockId) {
            this.stockId = stockId;
        }
        public Long getProductId() {
            return productId;
        }
        public void setProductId(Long productId) {
            this.productId = productId;
        }
        public BigDecimal getSalePrice() {
            return salePrice;
        }
        public void setSalePrice(BigDecimal salePrice) {
            this.salePrice = salePrice;
        }
        public Integer getQuantity() {
            return quantity;
        }
        public void setQuantity(Integer quantity) {
            this.quantity = quantity;
        }
    }
}