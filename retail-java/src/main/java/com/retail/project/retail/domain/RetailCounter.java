package com.retail.project.retail.domain;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.retail.framework.aspectj.lang.annotation.Excel;
import com.retail.framework.web.domain.BaseEntity;

/**
 * 柜台管理对象 retail_counter
 * 
 * <AUTHOR>
 * @date 2025-04-18
 */
public class RetailCounter extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 柜台ID */
    private Long id;

    /** 门店ID */
    @Excel(name = "门店ID")
    private Long storeId;

    /** 柜台名称 */
    @Excel(name = "柜台名称")
    private String counterName;
    
    /** 门店名称 */
    @Excel(name = "门店名称")
    private String storeName;

    public void setId(Long id) 
    {
        this.id = id;
    }

    public Long getId() 
    {
        return id;
    }

    public void setStoreId(Long storeId) 
    {
        this.storeId = storeId;
    }

    public Long getStoreId() 
    {
        return storeId;
    }

    public void setCounterName(String counterName) 
    {
        this.counterName = counterName;
    }

    public String getCounterName() 
    {
        return counterName;
    }
    
    public String getStoreName() 
    {
        return storeName;
    }

    public void setStoreName(String storeName) 
    {
        this.storeName = storeName;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("storeId", getStoreId())
            .append("counterName", getCounterName())
            .append("storeName", getStoreName())
            .toString();
    }
}
