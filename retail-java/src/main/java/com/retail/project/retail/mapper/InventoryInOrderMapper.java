package com.retail.project.retail.mapper;

import java.util.List;
import com.retail.project.retail.domain.InventoryInOrder;

/**
 * 入库单Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-04-17
 */
public interface InventoryInOrderMapper 
{
    /**
     * 查询入库单
     * 
     * @param id 入库单主键
     * @return 入库单
     */
    public InventoryInOrder selectInventoryInOrderById(Long id);

    /**
     * 查询入库单列表
     * 
     * @param inventoryInOrder 入库单
     * @return 入库单集合
     */
    public List<InventoryInOrder> selectInventoryInOrderList(InventoryInOrder inventoryInOrder);

    /**
     * 新增入库单
     * 
     * @param inventoryInOrder 入库单
     * @return 结果
     */
    public int insertInventoryInOrder(InventoryInOrder inventoryInOrder);

    /**
     * 修改入库单
     * 
     * @param inventoryInOrder 入库单
     * @return 结果
     */
    public int updateInventoryInOrder(InventoryInOrder inventoryInOrder);

    /**
     * 删除入库单
     * 
     * @param id 入库单主键
     * @return 结果
     */
    public int deleteInventoryInOrderById(Long id);

    /**
     * 批量删除入库单
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteInventoryInOrderByIds(Long[] ids);
} 