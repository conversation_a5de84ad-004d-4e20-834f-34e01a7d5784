package com.retail.project.retail.domain;

import java.util.List;

/**
 * 批量库存操作请求对象
 *
 * <AUTHOR>
 * @date 2025-01-20
 */
public class BatchStockRequest
{
    /** 操作项目列表 */
    private List<BatchStockItem> items;

    /** 操作备注 */
    private String remark;

    public List<BatchStockItem> getItems() {
        return items;
    }

    public void setItems(List<BatchStockItem> items) {
        this.items = items;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    /**
     * 批量库存操作项目
     */
    public static class BatchStockItem
    {
        /** 库存ID */
        private Long stockId;

        /** 操作数量 */
        private Integer quantity;

        /** 目标门店ID（迁移时使用） */
        private Long targetStoreId;

        /** 目标柜台ID（迁移时使用） */
        private Long targetCounterId;

        /** 目标渠道ID（迁移时使用） */
        private Long targetChannelId;

        /** 备注 */
        private String remark;

        public Long getStockId() {
            return stockId;
        }

        public void setStockId(Long stockId) {
            this.stockId = stockId;
        }

        public Integer getQuantity() {
            return quantity;
        }

        public void setQuantity(Integer quantity) {
            this.quantity = quantity;
        }

        public Long getTargetStoreId() {
            return targetStoreId;
        }

        public void setTargetStoreId(Long targetStoreId) {
            this.targetStoreId = targetStoreId;
        }

        public Long getTargetCounterId() {
            return targetCounterId;
        }

        public void setTargetCounterId(Long targetCounterId) {
            this.targetCounterId = targetCounterId;
        }

        public Long getTargetChannelId() {
            return targetChannelId;
        }

        public void setTargetChannelId(Long targetChannelId) {
            this.targetChannelId = targetChannelId;
        }

        public String getRemark() {
            return remark;
        }

        public void setRemark(String remark) {
            this.remark = remark;
        }
    }
}
