package com.retail.project.retail.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.retail.framework.aspectj.lang.annotation.Log;
import com.retail.framework.aspectj.lang.enums.BusinessType;
import com.retail.project.retail.domain.RetailStore;
import com.retail.project.retail.service.IRetailStoreService;
import com.retail.framework.web.controller.BaseController;
import com.retail.framework.web.domain.AjaxResult;
import com.retail.common.utils.poi.ExcelUtil;
import com.retail.framework.web.page.TableDataInfo;
import com.retail.project.system.domain.SysUser;
import com.retail.project.system.mapper.SysUserMapper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import com.retail.common.utils.SecurityUtils;

/**
 * 门店管理Controller
 *
 * <AUTHOR>
 * @date 2025-04-18
 */
@RestController
@RequestMapping("/retail/store")
public class RetailStoreController extends BaseController
{
    private static final Logger log = LoggerFactory.getLogger(RetailStoreController.class);

    @Autowired
    private IRetailStoreService retailStoreService;

    @Autowired
    private SysUserMapper userMapper;

    /**
     * 查询门店管理列表
     */
    @PreAuthorize("@ss.hasPermi('retail:store:list')")
    @GetMapping("/list")
    public TableDataInfo list(RetailStore retailStore)
    {
        startPage();
        List<RetailStore> list = retailStoreService.selectRetailStoreList(retailStore);
        return getDataTable(list);
    }

    /**
     * 导出门店管理列表
     */
    @PreAuthorize("@ss.hasPermi('retail:store:export')")
    @Log(title = "门店管理", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, RetailStore retailStore)
    {
        List<RetailStore> list = retailStoreService.selectRetailStoreList(retailStore);
        ExcelUtil<RetailStore> util = new ExcelUtil<RetailStore>(RetailStore.class);
        util.exportExcel(response, list, "门店管理数据");
    }

    /**
     * 获取门店管理详细信息
     */
    @PreAuthorize("@ss.hasPermi('retail:store:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(retailStoreService.selectRetailStoreById(id));
    }

    /**
     * 新增门店管理
     */
    @PreAuthorize("@ss.hasPermi('retail:store:add')")
    @Log(title = "门店管理", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody RetailStore retailStore)
    {
        retailStore.setCreateBy(getUsername());
        return toAjax(retailStoreService.insertRetailStoreWithUsers(retailStore));
    }

    /**
     * 修改门店管理
     */
    @PreAuthorize("@ss.hasPermi('retail:store:edit')")
    @Log(title = "门店管理", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody RetailStore retailStore)
    {
        retailStore.setUpdateBy(getUsername());
        return toAjax(retailStoreService.updateRetailStoreWithUsers(retailStore));
    }

    /**
     * 删除门店管理
     */
    @PreAuthorize("@ss.hasPermi('retail:store:remove')")
    @Log(title = "门店管理", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(retailStoreService.deleteRetailStoreByIds(ids));
    }

    /**
     * 查询所有门店列表（不分页）
     */
    @PreAuthorize("@ss.hasPermi('retail:store:list')")
    @GetMapping("/all")
    public AjaxResult listAll(RetailStore retailStore)
    {
        List<RetailStore> list = retailStoreService.selectRetailStoreList(retailStore);
        return success(list);
    }

    /**
     * 获取门店管理员用户列表
     */
    @PreAuthorize("@ss.hasPermi('retail:store:list')")
    @GetMapping("/storeUsers")
    public AjaxResult listStoreUsers()
    {
        try {
            List<SysUser> users = userMapper.selectUserListByRoleKey("store");
            log.info("查询门店管理员用户列表成功，找到{}个用户", users.size());
            return success(users);
        } catch (Exception e) {
            log.error("查询门店管理员用户列表失败", e);
            return error("查询用户列表失败：" + e.getMessage());
        }
    }

    /**
     * 获取当前用户所属门店
     */
    @PreAuthorize("@ss.hasPermi('retail:storestock:query')")
    @GetMapping("/current")
    public AjaxResult getCurrentUserStore()
    {
        try {
            Long userId = SecurityUtils.getUserId();

            // 如果是管理类角色，不限制门店
            if (SecurityUtils.hasManagementRole()) {
                return success(null);
            }

            // 查询当前用户所属的门店
            RetailStore retailStore = new RetailStore();
            List<RetailStore> storeList = retailStoreService.selectRetailStoreList(retailStore);

            // 查找包含当前用户ID的门店
            for (RetailStore store : storeList) {
                String userIds = store.getUserId();
                if (userIds != null && !userIds.isEmpty() && userIds.contains(userId.toString())) {
                    return success(store);
                }
            }

            return success(null);
        } catch (Exception e) {
            log.error("获取当前用户所属门店失败", e);
            return error("获取当前用户所属门店失败：" + e.getMessage());
        }
    }
}
