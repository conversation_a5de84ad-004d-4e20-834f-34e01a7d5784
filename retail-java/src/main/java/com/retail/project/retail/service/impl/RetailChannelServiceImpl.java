package com.retail.project.retail.service.impl;

import java.util.Date;
import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.retail.project.retail.mapper.RetailChannelMapper;
import com.retail.project.retail.domain.RetailChannel;
import com.retail.project.retail.service.IRetailChannelService;

/**
 * 渠道管理Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-04-17
 */
@Service
public class RetailChannelServiceImpl implements IRetailChannelService
{
    @Autowired
    private RetailChannelMapper retailChannelMapper;

    /**
     * 查询渠道管理
     *
     * @param id 渠道管理主键
     * @return 渠道管理
     */
    @Override
    public RetailChannel selectRetailChannelById(Long id)
    {
        return retailChannelMapper.selectRetailChannelById(id);
    }

    /**
     * 查询渠道管理列表
     *
     * @param retailChannel 渠道管理
     * @return 渠道管理
     */
    @Override
    public List<RetailChannel> selectRetailChannelList(RetailChannel retailChannel)
    {
        return retailChannelMapper.selectRetailChannelList(retailChannel);
    }

    /**
     * 新增渠道管理
     *
     * @param retailChannel 渠道管理
     * @return 结果
     */
    @Override
    public int insertRetailChannel(RetailChannel retailChannel)
    {
        retailChannel.setCreateTime(new Date());
        return retailChannelMapper.insertRetailChannel(retailChannel);
    }

    /**
     * 修改渠道管理
     *
     * @param retailChannel 渠道管理
     * @return 结果
     */
    @Override
    public int updateRetailChannel(RetailChannel retailChannel)
    {
        retailChannel.setUpdateTime(new Date());
        return retailChannelMapper.updateRetailChannel(retailChannel);
    }

    /**
     * 批量删除渠道管理
     *
     * @param ids 需要删除的渠道管理主键
     * @return 结果
     */
    @Override
    public int deleteRetailChannelByIds(Long[] ids)
    {
        return retailChannelMapper.deleteRetailChannelByIds(ids);
    }

    /**
     * 删除渠道管理信息
     *
     * @param id 渠道管理主键
     * @return 结果
     */
    @Override
    public int deleteRetailChannelById(Long id)
    {
        return retailChannelMapper.deleteRetailChannelById(id);
    }

    /**
     * 根据用户ID查询渠道
     *
     * @param userId 用户ID
     * @return 渠道
     */
    @Override
    public RetailChannel selectRetailChannelByUserId(Long userId)
    {
        // 查询所有渠道
        List<RetailChannel> channels = retailChannelMapper.selectRetailChannelList(new RetailChannel());

        // 过滤出包含当前用户ID的渠道
        for (RetailChannel channel : channels) {
            if (channel.getUserId() != null) {
                // 用户ID是以逗号分隔的字符串，需要拆分并检查
                String[] userIds = channel.getUserId().split(",");
                for (String id : userIds) {
                    if (userId.toString().equals(id.trim())) {
                        return channel;
                    }
                }
            }
        }

        return null;
    }
}
