package com.retail.project.retail.domain;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.retail.framework.aspectj.lang.annotation.Excel;
import com.retail.framework.web.domain.BaseEntity;

/**
 * 出库审核流程对象 inventory_out_flow
 * 
 * <AUTHOR>
 * @date 2025-04-27
 */
public class InventoryOutFlow extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 流程ID */
    private Long id;

    /** 流程名称 */
    @Excel(name = "流程名称")
    private String name;

    /** 流程描述 */
    @Excel(name = "流程描述")
    private String description;

    /** 门店ID */
    @Excel(name = "门店ID")
    private Long storeId;

    /** 审批角色，多个用逗号分隔 */
    @Excel(name = "审批角色")
    private String approverRoles;

    /** 状态（0停用 1启用） */
    @Excel(name = "状态", readConverterExp = "0=停用,1=启用")
    private Integer status;

    /** 门店名称 */
    private String storeName;

    public void setId(Long id) 
    {
        this.id = id;
    }

    public Long getId() 
    {
        return id;
    }
    
    public void setName(String name) 
    {
        this.name = name;
    }

    public String getName() 
    {
        return name;
    }
    
    public void setDescription(String description) 
    {
        this.description = description;
    }

    public String getDescription() 
    {
        return description;
    }
    
    public void setStoreId(Long storeId) 
    {
        this.storeId = storeId;
    }

    public Long getStoreId() 
    {
        return storeId;
    }
    
    public void setApproverRoles(String approverRoles) 
    {
        this.approverRoles = approverRoles;
    }

    public String getApproverRoles() 
    {
        return approverRoles;
    }
    
    public void setStatus(Integer status) 
    {
        this.status = status;
    }

    public Integer getStatus() 
    {
        return status;
    }
    
    public String getStoreName() 
    {
        return storeName;
    }

    public void setStoreName(String storeName) 
    {
        this.storeName = storeName;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("name", getName())
            .append("description", getDescription())
            .append("storeId", getStoreId())
            .append("approverRoles", getApproverRoles())
            .append("createBy", getCreateBy())
            .append("createTime", getCreateTime())
            .append("updateBy", getUpdateBy())
            .append("updateTime", getUpdateTime())
            .append("status", getStatus())
            .append("storeName", getStoreName())
            .toString();
    }
} 