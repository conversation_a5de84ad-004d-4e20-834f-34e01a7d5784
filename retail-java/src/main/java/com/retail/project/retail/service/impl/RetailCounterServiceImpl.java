package com.retail.project.retail.service.impl;

import java.util.Date;
import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.retail.project.retail.mapper.RetailCounterMapper;
import com.retail.project.retail.domain.RetailCounter;
import com.retail.project.retail.service.IRetailCounterService;

/**
 * 柜台管理Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-04-18
 */
@Service
public class RetailCounterServiceImpl implements IRetailCounterService 
{
    @Autowired
    private RetailCounterMapper retailCounterMapper;

    /**
     * 查询柜台管理
     * 
     * @param id 柜台管理主键
     * @return 柜台管理
     */
    @Override
    public RetailCounter selectRetailCounterById(Long id)
    {
        return retailCounterMapper.selectRetailCounterById(id);
    }

    /**
     * 查询柜台管理列表
     * 
     * @param retailCounter 柜台管理
     * @return 柜台管理
     */
    @Override
    public List<RetailCounter> selectRetailCounterList(RetailCounter retailCounter)
    {
        return retailCounterMapper.selectRetailCounterList(retailCounter);
    }

    /**
     * 新增柜台管理
     * 
     * @param retailCounter 柜台管理
     * @return 结果
     */
    @Override
    public int insertRetailCounter(RetailCounter retailCounter)
    {
        retailCounter.setCreateTime(new Date());
        return retailCounterMapper.insertRetailCounter(retailCounter);
    }

    /**
     * 修改柜台管理
     * 
     * @param retailCounter 柜台管理
     * @return 结果
     */
    @Override
    public int updateRetailCounter(RetailCounter retailCounter)
    {
        retailCounter.setUpdateTime(new Date());
        return retailCounterMapper.updateRetailCounter(retailCounter);
    }

    /**
     * 批量删除柜台管理
     * 
     * @param ids 需要删除的柜台管理主键
     * @return 结果
     */
    @Override
    public int deleteRetailCounterByIds(Long[] ids)
    {
        return retailCounterMapper.deleteRetailCounterByIds(ids);
    }

    /**
     * 删除柜台管理信息
     * 
     * @param id 柜台管理主键
     * @return 结果
     */
    @Override
    public int deleteRetailCounterById(Long id)
    {
        return retailCounterMapper.deleteRetailCounterById(id);
    }
}
