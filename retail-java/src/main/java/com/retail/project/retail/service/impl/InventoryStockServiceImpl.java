package com.retail.project.retail.service.impl;

import java.util.List;
import java.util.Date;
import java.math.BigDecimal;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import com.retail.project.retail.mapper.InventoryStockMapper;
import com.retail.project.retail.mapper.InventoryOutOrderMapper;
import com.retail.project.retail.mapper.InventoryOutDetailMapper;
import com.retail.project.retail.mapper.RetailStoreMapper;
import com.retail.project.retail.mapper.RetailCounterMapper;
import com.retail.project.retail.domain.InventoryStock;
import com.retail.project.retail.domain.InventoryOutOrder;
import com.retail.project.retail.domain.InventoryOutDetail;
import com.retail.project.retail.domain.RetailStore;
import com.retail.project.retail.domain.RetailCounter;
import com.retail.project.retail.domain.BatchStockRequest;
import com.retail.project.retail.service.IInventoryStockService;
import com.retail.framework.aspectj.lang.annotation.DataScope;
import com.retail.common.utils.SecurityUtils;

/**
 * 库存Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-04-17
 */
@Service
public class InventoryStockServiceImpl implements IInventoryStockService
{
    @Autowired
    private InventoryStockMapper inventoryStockMapper;

    @Autowired
    private InventoryOutOrderMapper inventoryOutOrderMapper;

    @Autowired
    private InventoryOutDetailMapper inventoryOutDetailMapper;

    @Autowired
    private RetailStoreMapper retailStoreMapper;

    @Autowired
    private RetailCounterMapper retailCounterMapper;

    /**
     * 查询库存
     *
     * @param id 库存主键
     * @return 库存
     */
    @Override
    public InventoryStock selectInventoryStockById(Long id)
    {
        return inventoryStockMapper.selectInventoryStockById(id);
    }

    /**
     * 查询库存列表
     *
     * @param inventoryStock 库存
     * @return 库存
     */
    @Override
    @DataScope(deptAlias = "d", userAlias = "u")
    public List<InventoryStock> selectInventoryStockList(InventoryStock inventoryStock)
    {
        return inventoryStockMapper.selectInventoryStockList(inventoryStock);
    }

    /**
     * 查询商品在指定门店柜台渠道的库存列表
     *
     * @param productId 商品ID
     * @param storeId 门店ID
     * @param counterId 柜台ID
     * @param channelId 渠道ID
     * @return 库存集合
     */
    @Override
    public List<InventoryStock> selectInventoryStockByLocation(Long productId, Long storeId, Long counterId, Long channelId)
    {
        return inventoryStockMapper.selectInventoryStockByLocation(productId, storeId, counterId, channelId);
    }

    /**
     * 查询商品最早入库的非期货库存记录，按入库时间排序
     *
     * @param inventoryStock 库存查询条件
     * @return 库存集合
     */
    @Override
    @DataScope(deptAlias = "d", userAlias = "u")
    public List<InventoryStock> selectNonFuturesInventoryStockByProductId(InventoryStock inventoryStock)
    {
        // 确保只查询非期货商品
        inventoryStock.setIsFutures(false);
        // 确保只查询库存大于0的商品
        if (inventoryStock.getQuantity() == null) {
            inventoryStock.setQuantity(0);
        }
        // 按照入库时间排序，最早入库的排在前面
        return inventoryStockMapper.selectStockByProductIdOrderByTime(inventoryStock);
    }

    /**
     * 新增库存
     *
     * @param inventoryStock 库存
     * @return 结果
     */
    @Override
    public int insertInventoryStock(InventoryStock inventoryStock)
    {
        inventoryStock.setCreateTime(new Date());
        return inventoryStockMapper.insertInventoryStock(inventoryStock);
    }

    /**
     * 修改库存
     *
     * @param inventoryStock 库存
     * @return 结果
     */
    @Override
    public int updateInventoryStock(InventoryStock inventoryStock)
    {
        inventoryStock.setUpdateTime(new Date());
        return inventoryStockMapper.updateInventoryStock(inventoryStock);
    }

    /**
     * 批量删除库存
     *
     * @param ids 需要删除的库存主键
     * @return 结果
     */
    @Override
    public int deleteInventoryStockByIds(Long[] ids)
    {
        return inventoryStockMapper.deleteInventoryStockByIds(ids);
    }

    /**
     * 删除库存信息
     *
     * @param id 库存主键
     * @return 结果
     */
    @Override
    public int deleteInventoryStockById(Long id)
    {
        return inventoryStockMapper.deleteInventoryStockById(id);
    }

    /**
     * 批量删除库存
     *
     * @param request 批量删除请求
     * @return 结果
     */
    @Override
    @Transactional
    public int batchDeleteStock(BatchStockRequest request)
    {
        if (request.getItems() == null || request.getItems().isEmpty()) {
            return 0;
        }

        String currentUser = SecurityUtils.getUsername();
        Date currentTime = new Date();

        // 创建一个出库单
        InventoryOutOrder outOrder = new InventoryOutOrder();
        outOrder.setOrderNumber(generateOrderNumber());
        outOrder.setOrderType(0); // 出库
        outOrder.setBusinessType(5); // 库存删除
        outOrder.setProcessStatus(1); // 已完成
        outOrder.setRemark(request.getRemark()); // 使用请求中的删除原因
        outOrder.setCreateBy(currentUser);
        outOrder.setCreateTime(currentTime);

        // 计算总金额，并设置门店信息（使用第一个商品的门店信息）
        BigDecimal totalAmount = BigDecimal.ZERO;
        Long storeId = null;
        String storeName = null;
        for (BatchStockRequest.BatchStockItem item : request.getItems()) {
            InventoryStock stock = inventoryStockMapper.selectInventoryStockById(item.getStockId());
            if (stock != null) {
                totalAmount = totalAmount.add(stock.getPurchasePrice().multiply(BigDecimal.valueOf(item.getQuantity())));
                // 使用第一个商品的门店信息作为出库单的门店信息
                if (storeId == null) {
                    storeId = stock.getStoreId();
                    storeName = stock.getStoreName();
                }
            }
        }
        outOrder.setTotalAmount(totalAmount);
        outOrder.setStoreId(storeId);
        outOrder.setStoreName(storeName);

        // 插入出库单
        inventoryOutOrderMapper.insertInventoryOutOrder(outOrder);

        int totalResult = 0;
        for (BatchStockRequest.BatchStockItem item : request.getItems()) {
            // 获取库存信息
            InventoryStock stock = inventoryStockMapper.selectInventoryStockById(item.getStockId());
            if (stock == null) {
                continue;
            }

            // 检查库存数量是否足够
            if (stock.getQuantity() < item.getQuantity()) {
                throw new RuntimeException("库存数量不足，商品：" + stock.getProductName());
            }

            // 创建出库明细
            InventoryOutDetail outDetail = new InventoryOutDetail();
            outDetail.setOrderId(outOrder.getId());
            outDetail.setProductId(stock.getProductId());
            outDetail.setProductName(stock.getProductName());
            outDetail.setCounterId(stock.getCounterId());
            outDetail.setChannelId(stock.getChannelId());
            outDetail.setQuantity(item.getQuantity());
            outDetail.setSalePrice(BigDecimal.ZERO); // 库存删除操作，销售价设为0
            outDetail.setBatchNumber(stock.getBatchNumber());
            // 在备注中记录门店-柜台信息
            String deleteRemark = String.format("库存删除 - %s、%s",
                stock.getStoreName(), stock.getCounterName());
            outDetail.setRemark(deleteRemark);
            outDetail.setCreateBy(currentUser);
            outDetail.setCreateTime(currentTime);

            // 插入出库明细
            inventoryOutDetailMapper.insertInventoryOutDetail(outDetail);

            // 减少库存数量
            if (stock.getQuantity().equals(item.getQuantity())) {
                // 如果删除全部库存，直接删除库存记录
                inventoryStockMapper.deleteInventoryStockById(item.getStockId());
            } else {
                // 否则减少库存数量
                stock.setQuantity(stock.getQuantity() - item.getQuantity());
                stock.setUpdateTime(currentTime);
                inventoryStockMapper.updateInventoryStock(stock);
            }

            totalResult++;
        }

        return totalResult;
    }

    /**
     * 批量迁移库存
     *
     * @param request 批量迁移请求
     * @return 结果
     */
    @Override
    @Transactional
    public int batchMigrateStock(BatchStockRequest request)
    {
        if (request.getItems() == null || request.getItems().isEmpty()) {
            return 0;
        }

        String currentUser = SecurityUtils.getUsername();
        Date currentTime = new Date();

        // 创建一个出库单
        InventoryOutOrder outOrder = new InventoryOutOrder();
        outOrder.setOrderNumber(generateOrderNumber());
        outOrder.setOrderType(0); // 出库
        outOrder.setBusinessType(6); // 库存迁移
        outOrder.setProcessStatus(1); // 已完成
        outOrder.setRemark(request.getRemark()); // 使用请求中的迁移备注
        outOrder.setCreateBy(currentUser);
        outOrder.setCreateTime(currentTime);

        // 计算总金额，并设置门店信息（使用第一个商品的门店信息）
        BigDecimal totalAmount = BigDecimal.ZERO;
        Long storeId = null;
        String storeName = null;
        for (BatchStockRequest.BatchStockItem item : request.getItems()) {
            InventoryStock stock = inventoryStockMapper.selectInventoryStockById(item.getStockId());
            if (stock != null) {
                totalAmount = totalAmount.add(stock.getPurchasePrice().multiply(BigDecimal.valueOf(item.getQuantity())));
                // 使用第一个商品的门店信息作为出库单的门店信息
                if (storeId == null) {
                    storeId = stock.getStoreId();
                    storeName = stock.getStoreName();
                }
            }
        }
        outOrder.setTotalAmount(totalAmount);
        outOrder.setStoreId(storeId);
        outOrder.setStoreName(storeName);

        // 插入出库单
        inventoryOutOrderMapper.insertInventoryOutOrder(outOrder);

        int totalResult = 0;
        for (BatchStockRequest.BatchStockItem item : request.getItems()) {
            // 获取原库存信息
            InventoryStock originalStock = inventoryStockMapper.selectInventoryStockById(item.getStockId());
            if (originalStock == null) {
                continue;
            }

            // 检查库存数量是否足够
            if (originalStock.getQuantity() < item.getQuantity()) {
                throw new RuntimeException("库存数量不足，商品：" + originalStock.getProductName());
            }

            // 创建出库明细
            InventoryOutDetail outDetail = new InventoryOutDetail();
            outDetail.setOrderId(outOrder.getId());
            outDetail.setProductId(originalStock.getProductId());
            outDetail.setProductName(originalStock.getProductName());
            outDetail.setCounterId(originalStock.getCounterId());
            outDetail.setChannelId(originalStock.getChannelId());
            outDetail.setQuantity(item.getQuantity());
            outDetail.setSalePrice(BigDecimal.ZERO); // 库存迁移操作，销售价设为0
            outDetail.setBatchNumber(originalStock.getBatchNumber());
            // 生成迁移备注：原门店、原柜台 -> 目标门店、目标柜台
            String targetStoreName = getStoreNameById(item.getTargetStoreId());
            String targetCounterName = getCounterNameById(item.getTargetCounterId());
            String migrateRemark = String.format("%s、%s -> %s、%s",
                originalStock.getStoreName(), originalStock.getCounterName(),
                targetStoreName, targetCounterName);
            outDetail.setRemark(migrateRemark);
            outDetail.setCreateBy(currentUser);
            outDetail.setCreateTime(currentTime);

            // 插入出库明细
            inventoryOutDetailMapper.insertInventoryOutDetail(outDetail);

            // 减少原库存数量
            if (originalStock.getQuantity().equals(item.getQuantity())) {
                // 如果迁移全部库存，直接删除原库存记录
                inventoryStockMapper.deleteInventoryStockById(item.getStockId());
            } else {
                // 否则减少原库存数量
                originalStock.setQuantity(originalStock.getQuantity() - item.getQuantity());
                originalStock.setUpdateTime(currentTime);
                inventoryStockMapper.updateInventoryStock(originalStock);
            }

            // 在目标位置创建新库存或增加现有库存
            List<InventoryStock> targetStocks = inventoryStockMapper.selectInventoryStockByLocation(
                originalStock.getProductId(), item.getTargetStoreId(), item.getTargetCounterId(), item.getTargetChannelId());

            if (targetStocks.isEmpty()) {
                // 创建新库存记录
                InventoryStock newStock = new InventoryStock();
                newStock.setProductId(originalStock.getProductId());
                newStock.setProductNumber(originalStock.getProductNumber());
                newStock.setProductName(originalStock.getProductName());
                newStock.setProductImage(originalStock.getProductImage());
                newStock.setBrand(originalStock.getBrand());
                newStock.setCategory(originalStock.getCategory());
                newStock.setStoreId(item.getTargetStoreId());
                newStock.setCounterId(item.getTargetCounterId());
                newStock.setChannelId(item.getTargetChannelId());
                newStock.setQuantity(item.getQuantity());
                newStock.setPurchasePrice(originalStock.getPurchasePrice());
                newStock.setRetailPrice(originalStock.getRetailPrice());
                newStock.setBatchNumber(originalStock.getBatchNumber());
                newStock.setIsFutures(originalStock.getIsFutures());
                newStock.setCreateBy(currentUser);
                newStock.setCreateTime(currentTime);

                inventoryStockMapper.insertInventoryStock(newStock);
            } else {
                // 增加现有库存数量
                InventoryStock targetStock = targetStocks.get(0);
                targetStock.setQuantity(targetStock.getQuantity() + item.getQuantity());
                targetStock.setUpdateTime(currentTime);
                inventoryStockMapper.updateInventoryStock(targetStock);
            }

            totalResult++;
        }

        return totalResult;
    }

    /**
     * 生成出库单号
     */
    private String generateOrderNumber() {
        return "OUT" + System.currentTimeMillis();
    }

    /**
     * 根据门店ID获取门店名称
     */
    private String getStoreNameById(Long storeId) {
        if (storeId == null) {
            return "未知门店";
        }
        RetailStore store = retailStoreMapper.selectRetailStoreById(storeId);
        return store != null ? store.getStoreName() : "未知门店";
    }

    /**
     * 根据柜台ID获取柜台名称
     */
    private String getCounterNameById(Long counterId) {
        if (counterId == null) {
            return "未知柜台";
        }
        RetailCounter counter = retailCounterMapper.selectRetailCounterById(counterId);
        return counter != null ? counter.getCounterName() : "未知柜台";
    }
}