package com.retail.project.retail.mapper;

import java.util.List;
import com.retail.project.retail.domain.RetailProducts;

/**
 * 商品Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-04-17
 */
public interface RetailProductsMapper 
{
    /**
     * 查询商品
     * 
     * @param id 商品主键
     * @return 商品
     */
    public RetailProducts selectRetailProductsById(Long id);

    /**
     * 查询商品列表
     * 
     * @param retailProducts 商品
     * @return 商品集合
     */
    public List<RetailProducts> selectRetailProductsList(RetailProducts retailProducts);

    /**
     * 新增商品
     * 
     * @param retailProducts 商品
     * @return 结果
     */
    public int insertRetailProducts(RetailProducts retailProducts);

    /**
     * 修改商品
     * 
     * @param retailProducts 商品
     * @return 结果
     */
    public int updateRetailProducts(RetailProducts retailProducts);

    /**
     * 删除商品
     * 
     * @param id 商品主键
     * @return 结果
     */
    public int deleteRetailProductsById(Long id);

    /**
     * 批量删除商品
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteRetailProductsByIds(Long[] ids);
}
