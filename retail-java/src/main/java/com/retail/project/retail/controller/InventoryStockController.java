package com.retail.project.retail.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.retail.framework.aspectj.lang.annotation.Log;
import com.retail.framework.aspectj.lang.enums.BusinessType;
import com.retail.project.retail.domain.InventoryStock;
import com.retail.project.retail.domain.BatchStockRequest;
import com.retail.project.retail.service.IInventoryStockService;
import com.retail.framework.web.controller.BaseController;
import com.retail.framework.web.domain.AjaxResult;
import com.retail.common.utils.poi.ExcelUtil;
import com.retail.framework.web.page.TableDataInfo;

/**
 * 库存Controller
 *
 * <AUTHOR>
 * @date 2025-04-17
 */
@RestController
@RequestMapping("/retail/stock")
public class InventoryStockController extends BaseController
{
    @Autowired
    private IInventoryStockService inventoryStockService;

    /**
     * 查询库存列表
     */
    @PreAuthorize("@ss.hasPermi('retail:stock:list')")
    @GetMapping("/list")
    public TableDataInfo list(InventoryStock inventoryStock)
    {
        startPage();
        List<InventoryStock> list = inventoryStockService.selectInventoryStockList(inventoryStock);
        return getDataTable(list);
    }

    /**
     * 导出库存列表
     */
    @PreAuthorize("@ss.hasPermi('retail:stock:export')")
    @Log(title = "库存", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, InventoryStock inventoryStock)
    {
        List<InventoryStock> list = inventoryStockService.selectInventoryStockList(inventoryStock);
        ExcelUtil<InventoryStock> util = new ExcelUtil<InventoryStock>(InventoryStock.class);
        util.exportExcel(response, list, "库存数据");
    }

    /**
     * 获取库存详细信息
     */
    @PreAuthorize("@ss.hasPermi('retail:stock:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(inventoryStockService.selectInventoryStockById(id));
    }

    /**
     * 查询商品在指定位置的库存
     */
    @PreAuthorize("@ss.hasPermi('retail:stock:query')")
    @GetMapping(value = "/location/{productId}/{storeId}/{counterId}/{channelId}")
    public AjaxResult getStockByLocation(
            @PathVariable("productId") Long productId,
            @PathVariable("storeId") Long storeId,
            @PathVariable("counterId") Long counterId,
            @PathVariable("channelId") Long channelId)
    {
        List<InventoryStock> list = inventoryStockService.selectInventoryStockByLocation(productId, storeId, counterId, channelId);
        return success(list);
    }

    /**
     * 批量删除库存
     */
    @PreAuthorize("@ss.hasPermi('retail:stock:remove')")
    @Log(title = "批量删除库存", businessType = BusinessType.DELETE)
    @PostMapping("/batch-delete")
    public AjaxResult batchDeleteStock(@RequestBody BatchStockRequest request)
    {
        return toAjax(inventoryStockService.batchDeleteStock(request));
    }

    /**
     * 批量迁移库存
     */
    @PreAuthorize("@ss.hasPermi('retail:stock:edit')")
    @Log(title = "批量迁移库存", businessType = BusinessType.UPDATE)
    @PostMapping("/batch-migrate")
    public AjaxResult batchMigrateStock(@RequestBody BatchStockRequest request)
    {
        return toAjax(inventoryStockService.batchMigrateStock(request));
    }
}