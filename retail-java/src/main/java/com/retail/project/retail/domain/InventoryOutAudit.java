package com.retail.project.retail.domain;

import java.util.Date;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.retail.framework.aspectj.lang.annotation.Excel;
import com.retail.framework.web.domain.BaseEntity;

/**
 * 出库单审核记录对象 inventory_out_audit
 *
 * <AUTHOR>
 * @date 2025-04-27
 */
public class InventoryOutAudit extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 审核记录ID */
    private Long id;

    /** 出库单ID */
    @Excel(name = "出库单ID")
    private Long orderId;

    /** 已废弃，请使用 businessType 和 processStatus 字段 */
    @Deprecated
    private Integer action;

    /** 业务场景（0售出 1预订 2退货 3取消 4交货 5库存删除 6库存迁移） */
    @Excel(name = "业务场景", readConverterExp = "0=售出,1=预订,2=退货,3=取消,4=交货,5=库存删除,6=库存迁移")
    private Integer businessType;

    /** 流程节点（0待审核 1已审核 2已驳回 3已撤销） */
    @Excel(name = "流程节点", readConverterExp = "0=待审核,1=已审核,2=已驳回,3=已撤销")
    private Integer processStatus;

    /** 操作理由/意见 */
    @Excel(name = "操作理由/意见")
    private String reason;

    /** 操作人 */
    @Excel(name = "操作人")
    private String operator;

    /** 操作时间 */
    @Excel(name = "操作时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date operateTime;

    /** 订单号 */
    private String orderNumber;

    public void setId(Long id)
    {
        this.id = id;
    }

    public Long getId()
    {
        return id;
    }

    public void setOrderId(Long orderId)
    {
        this.orderId = orderId;
    }

    public Long getOrderId()
    {
        return orderId;
    }

    /**
     * @deprecated 已废弃，请使用 setBusinessType 和 setProcessStatus 方法
     */
    @Deprecated
    public void setAction(Integer action)
    {
        this.action = action;
    }

    /**
     * @deprecated 已废弃，请使用 getBusinessType 和 getProcessStatus 方法
     */
    @Deprecated
    public Integer getAction()
    {
        return action;
    }

    public void setReason(String reason)
    {
        this.reason = reason;
    }

    public String getReason()
    {
        return reason;
    }

    public void setOperator(String operator)
    {
        this.operator = operator;
    }

    public String getOperator()
    {
        return operator;
    }

    public void setOperateTime(Date operateTime)
    {
        this.operateTime = operateTime;
    }

    public Date getOperateTime()
    {
        return operateTime;
    }

    public String getOrderNumber()
    {
        return orderNumber;
    }

    public void setOrderNumber(String orderNumber)
    {
        this.orderNumber = orderNumber;
    }

    public Integer getBusinessType()
    {
        return businessType;
    }

    public void setBusinessType(Integer businessType)
    {
        this.businessType = businessType;
        // 自动设置 action 字段，以便与现有代码兼容
        updateActionFromBusinessTypeAndProcessStatus();
    }

    public Integer getProcessStatus()
    {
        return processStatus;
    }

    public void setProcessStatus(Integer processStatus)
    {
        this.processStatus = processStatus;
        // 自动设置 action 字段，以便与现有代码兼容
        updateActionFromBusinessTypeAndProcessStatus();
    }

    /**
     * 根据 businessType 和 processStatus 自动设置 action 字段
     * 这是为了与现有代码兼容，因为 action 字段已经从数据库表中删除了
     */
    private void updateActionFromBusinessTypeAndProcessStatus() {
        if (this.businessType == null || this.processStatus == null) {
            return;
        }

        if (this.businessType == 0) { // 售出业务
            if (this.processStatus == 0) this.action = 0; // 提交
            else if (this.processStatus == 1) this.action = 1; // 通过
            else if (this.processStatus == 2) this.action = 2; // 驳回
            else if (this.processStatus == 3) this.action = 3; // 撤销
        } else if (this.businessType == 1) { // 预订业务
            if (this.processStatus == 0) this.action = 0; // 提交
            else if (this.processStatus == 1) this.action = 1; // 通过
            else if (this.processStatus == 2) this.action = 2; // 驳回
            else if (this.processStatus == 3) this.action = 3; // 撤销
        } else if (this.businessType == 2) { // 退货业务
            if (this.processStatus == 0) this.action = 5; // 申请退货
            else if (this.processStatus == 1) this.action = 6; // 退货审核通过
            else if (this.processStatus == 2) this.action = 7; // 退货审核驳回
            else if (this.processStatus == 3) this.action = 8; // 退货申请撤销
        } else if (this.businessType == 3) { // 取消业务
            if (this.processStatus == 0) this.action = 9; // 申请取消
            else if (this.processStatus == 1) this.action = 10; // 取消通过
            else if (this.processStatus == 2) this.action = 11; // 取消驳回
            else if (this.processStatus == 3) this.action = 3; // 撤销
        } else if (this.businessType == 4) { // 交货业务
            if (this.processStatus == 0) this.action = 0; // 提交
            else if (this.processStatus == 1) this.action = 8; // 交货
        }
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("orderId", getOrderId())
            .append("action", getAction())
            .append("businessType", getBusinessType())
            .append("processStatus", getProcessStatus())
            .append("reason", getReason())
            .append("operator", getOperator())
            .append("operateTime", getOperateTime())
            .append("orderNumber", getOrderNumber())
            .toString();
    }
}