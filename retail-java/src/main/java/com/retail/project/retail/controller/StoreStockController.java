package com.retail.project.retail.controller;

import java.util.List;
import java.util.Map;
import java.util.HashMap;
import java.util.ArrayList;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.retail.framework.aspectj.lang.annotation.Log;
import com.retail.framework.aspectj.lang.enums.BusinessType;
import com.retail.project.retail.domain.InventoryStock;
import com.retail.project.retail.domain.MergedProductStock;
import com.retail.project.retail.service.IInventoryStockService;
import com.retail.framework.web.controller.BaseController;
import com.retail.framework.web.domain.AjaxResult;
import com.retail.common.utils.poi.ExcelUtil;
import com.retail.framework.web.page.TableDataInfo;
import com.retail.common.utils.SecurityUtils;
import com.retail.project.retail.mapper.RetailStoreMapper;
import com.retail.project.retail.utils.StorePermissionUtils;
import com.retail.framework.web.page.PageDomain;
import com.retail.framework.web.page.TableSupport;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * 门店库存Controller
 *
 * <AUTHOR>
 * @date 2025-04-17
 */
@RestController
@RequestMapping("/retail/storestock")
public class StoreStockController extends BaseController
{
    private static final Logger log = LoggerFactory.getLogger(StoreStockController.class);

    @Autowired
    private IInventoryStockService inventoryStockService;

    @Autowired
    private RetailStoreMapper retailStoreMapper;

    /**
     * 查询门店库存列表
     */
    @PreAuthorize("@ss.hasPermi('retail:storestock:list')")
    @GetMapping("/list")
    public TableDataInfo list(InventoryStock inventoryStock)
    {
        startPage();

        Long userId = SecurityUtils.getUserId();
        // 设置当前用户ID，用于数据权限过滤
        inventoryStock.getParams().put("userId", userId);
        // 跳过默认的数据范围过滤
        inventoryStock.getParams().put("isSkipDataScope", true);

        // 判断用户是否拥有管理类角色
        boolean hasManagementRole = SecurityUtils.hasManagementRole();

        inventoryStock.getParams().put("isAdmin", hasManagementRole);

        List<InventoryStock> list = inventoryStockService.selectInventoryStockList(inventoryStock);

        // 隐藏进货价信息
        list.forEach(stock -> stock.setPurchasePrice(null));

        return getDataTable(list);
    }

    /**
     * 查询合并后的门店库存列表（按产品合并）
     */
    @PreAuthorize("@ss.hasPermi('retail:storestock:list')")
    @GetMapping("/mergedList")
    public TableDataInfo mergedList(InventoryStock inventoryStock)
    {
        // 获取分页参数
        PageDomain pageDomain = TableSupport.buildPageRequest();
        Integer pageNum = pageDomain.getPageNum();
        Integer pageSize = pageDomain.getPageSize();

        Long userId = SecurityUtils.getUserId();
        // 设置当前用户ID，用于数据权限过滤
        inventoryStock.getParams().put("userId", userId);
        // 跳过默认的数据范围过滤
        inventoryStock.getParams().put("isSkipDataScope", true);

        // 判断用户是否拥有管理类角色
        boolean hasManagementRole = SecurityUtils.hasManagementRole();

        inventoryStock.getParams().put("isAdmin", hasManagementRole);

        // 不使用分页查询，获取所有数据进行合并
        List<InventoryStock> list = inventoryStockService.selectInventoryStockList(inventoryStock);

        // 隐藏进货价信息
        list.forEach(stock -> stock.setPurchasePrice(null));

        // 按产品ID分组合并
        Map<Long, MergedProductStock> mergedProductMap = new HashMap<>();
        for (InventoryStock stock : list) {
            if (stock.getProductId() == null) {
                continue;
            }

            Long productId = stock.getProductId();
            if (!mergedProductMap.containsKey(productId)) {
                MergedProductStock mergedStock = new MergedProductStock();
                mergedStock.setProductId(productId);
                mergedStock.setProductNumber(stock.getProductNumber());
                mergedStock.setProductName(stock.getProductName());
                mergedStock.setProductImage(stock.getProductImage());
                mergedStock.setBrand(stock.getBrand());
                mergedStock.setCategory(stock.getCategory());
                mergedStock.setTotalQuantity(0);
                mergedStock.setStoreCount(0);
                mergedStock.setHasFutures(false);
                mergedStock.setHasNonFuturesStock(false);
                mergedStock.setStores(new ArrayList<>());

                mergedProductMap.put(productId, mergedStock);
            }

            MergedProductStock mergedStock = mergedProductMap.get(productId);
            mergedStock.setTotalQuantity(mergedStock.getTotalQuantity() + stock.getQuantity());

            // 记录门店信息
            if (stock.getStoreId() != null && stock.getStoreName() != null) {
                if (!mergedStock.getStores().contains(stock.getStoreName())) {
                    mergedStock.getStores().add(stock.getStoreName());
                    mergedStock.setStoreCount(mergedStock.getStoreCount() + 1);
                }
            }

            // 检查是否有期货
            if (stock.getIsFutures() != null && stock.getIsFutures()) {
                mergedStock.setHasFutures(true);
            }

            // 检查是否有非期货库存且数量大于0
            if (stock.getIsFutures() != null && !stock.getIsFutures() && stock.getQuantity() != null && stock.getQuantity() > 0) {
                mergedStock.setHasNonFuturesStock(true);
            }
        }

        List<MergedProductStock> allMergedList = new ArrayList<>(mergedProductMap.values());

        // 手动进行分页
        int total = allMergedList.size();
        int startIndex = (pageNum - 1) * pageSize;
        int endIndex = Math.min(startIndex + pageSize, total);

        List<MergedProductStock> pagedMergedList;
        if (startIndex >= total) {
            pagedMergedList = new ArrayList<>();
        } else {
            pagedMergedList = allMergedList.subList(startIndex, endIndex);
        }

        // 创建分页数据对象
        TableDataInfo rspData = new TableDataInfo();
        rspData.setCode(0);
        rspData.setRows(pagedMergedList);
        rspData.setTotal(total);
        return rspData;
    }

    /**
     * 获取产品的详细库存
     */
    @PreAuthorize("@ss.hasPermi('retail:storestock:query')")
    @GetMapping("/product/{productId}")
    public AjaxResult getProductDetails(@PathVariable("productId") Long productId, InventoryStock inventoryStock)
    {
        Long userId = SecurityUtils.getUserId();
        // 设置产品ID
        inventoryStock.setProductId(productId);
        // 设置当前用户ID，用于数据权限过滤
        inventoryStock.getParams().put("userId", userId);
        // 跳过默认的数据范围过滤
        inventoryStock.getParams().put("isSkipDataScope", true);

        // 判断用户是否拥有admin或manager角色
        boolean hasManagementRole = SecurityUtils.hasManagementRole();

        inventoryStock.getParams().put("isAdmin", hasManagementRole);

        List<InventoryStock> list = inventoryStockService.selectInventoryStockList(inventoryStock);

        // 隐藏进货价信息
        list.forEach(stock -> stock.setPurchasePrice(null));

        return success(list);
    }

    /**
     * 导出门店库存列表
     */
    @PreAuthorize("@ss.hasPermi('retail:storestock:export')")
    @Log(title = "门店库存", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, InventoryStock inventoryStock)
    {
        Long userId = SecurityUtils.getUserId();
        // 设置当前用户ID，用于数据权限过滤
        inventoryStock.getParams().put("userId", userId);
        // 跳过默认的数据范围过滤
        inventoryStock.getParams().put("isSkipDataScope", true);

        // 判断用户是否拥有管理类角色
        boolean hasManagementRole = SecurityUtils.hasManagementRole();

        inventoryStock.getParams().put("isAdmin", hasManagementRole);

        List<InventoryStock> list = inventoryStockService.selectInventoryStockList(inventoryStock);

        // 隐藏进货价信息
        list.forEach(stock -> stock.setPurchasePrice(null));

        ExcelUtil<InventoryStock> util = new ExcelUtil<InventoryStock>(InventoryStock.class);
        util.exportExcel(response, list, "门店库存数据");
    }

    /**
     * 获取门店库存详细信息
     */
    @PreAuthorize("@ss.hasPermi('retail:storestock:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        InventoryStock stock = inventoryStockService.selectInventoryStockById(id);

        // 检查当前用户是否有权限查看该门店库存
        if(stock != null) {
            Long userId = SecurityUtils.getUserId();
            // 检查用户是否有该门店的权限
            boolean hasAuth = checkUserStorePermission(stock.getStoreId(), userId);
            if (!hasAuth) {
                return error("您没有权限查看此门店库存");
            }

            // 隐藏进货价信息
            stock.setPurchasePrice(null);
        }

        return success(stock);
    }

    /**
     * 检查用户是否有权限访问指定门店
     * @param storeId 门店ID
     * @param userId 用户ID
     * @return 是否有权限
     */
    private boolean checkUserStorePermission(Long storeId, Long userId) {
        return StorePermissionUtils.checkUserStorePermission(storeId, userId, retailStoreMapper);
    }

    /**
     * 获取产品最早入库的非期货库存
     */
    @PreAuthorize("@ss.hasPermi('retail:storestock:query')")
    @GetMapping("/earliest/{productId}")
    public AjaxResult getEarliestNonFuturesStock(@PathVariable("productId") Long productId, Long storeId)
    {
        Long userId = SecurityUtils.getUserId();
        // 判断用户是否拥有管理类角色
        boolean hasManagementRole = SecurityUtils.hasManagementRole();

        // 构建查询条件
        InventoryStock queryParam = new InventoryStock();
        queryParam.setProductId(productId);
        queryParam.setIsFutures(false); // 非期货

        // 如果提供了门店ID，则按门店ID查询
        if (storeId != null) {
            queryParam.setStoreId(storeId);

            // 检查用户是否有该门店权限
            boolean hasAuth = StorePermissionUtils.checkUserStorePermission(storeId, userId, retailStoreMapper);
            if (!hasAuth) {
                return error("您没有权限查看此门店库存");
            }
        }

        // 添加权限过滤
        queryParam.getParams().put("userId", userId);
        queryParam.getParams().put("isSkipDataScope", true);
        queryParam.getParams().put("isAdmin", hasManagementRole);

        // 获取符合条件的库存记录，按入库时间排序
        List<InventoryStock> list = inventoryStockService.selectNonFuturesInventoryStockByProductId(queryParam);

        if (list != null && !list.isEmpty()) {
            // 找到数量大于0的第一条记录
            for (InventoryStock stock : list) {
                if (stock.getQuantity() != null && stock.getQuantity() > 0) {
                    // 隐藏进货价信息
                    stock.setPurchasePrice(null);
                    return success(stock);
                }
            }
        }

        return error("没有可售出的库存，所有库存都是期货或数量为0");
    }
}