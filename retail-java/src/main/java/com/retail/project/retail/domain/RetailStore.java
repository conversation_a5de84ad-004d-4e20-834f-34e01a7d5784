package com.retail.project.retail.domain;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.retail.framework.aspectj.lang.annotation.Excel;
import com.retail.framework.web.domain.BaseEntity;

/**
 * 门店管理对象 retail_store
 * 
 * <AUTHOR>
 * @date 2025-04-18
 */
public class RetailStore extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 门店ID */
    private Long id;

    /** 门店名称 */
    @Excel(name = "门店名称")
    private String storeName;

    /** 门店地址 */
    @Excel(name = "门店地址")
    private String storeAddress;

    /** 联系电话 */
    @Excel(name = "联系电话")
    private String storeContact;
    
    /** 授权用户ID，多个用逗号分隔 */
    private String userId;
    
    /** 授权用户名称，多个用逗号分隔 */
    @Excel(name = "授权用户")
    private String userName;

    public void setId(Long id) 
    {
        this.id = id;
    }

    public Long getId() 
    {
        return id;
    }

    public void setStoreName(String storeName) 
    {
        this.storeName = storeName;
    }

    public String getStoreName() 
    {
        return storeName;
    }

    public void setStoreAddress(String storeAddress) 
    {
        this.storeAddress = storeAddress;
    }

    public String getStoreAddress() 
    {
        return storeAddress;
    }

    public void setStoreContact(String storeContact) 
    {
        this.storeContact = storeContact;
    }

    public String getStoreContact() 
    {
        return storeContact;
    }
    
    public String getUserId() 
    {
        return userId;
    }

    public void setUserId(String userId) 
    {
        this.userId = userId;
    }
    
    public String getUserName() 
    {
        return userName;
    }

    public void setUserName(String userName) 
    {
        this.userName = userName;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("storeName", getStoreName())
            .append("storeAddress", getStoreAddress())
            .append("storeContact", getStoreContact())
            .append("userId", getUserId())
            .append("userName", getUserName())
            .toString();
    }
}
