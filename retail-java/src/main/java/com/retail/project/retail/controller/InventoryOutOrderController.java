package com.retail.project.retail.controller;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.retail.framework.aspectj.lang.annotation.Log;
import com.retail.framework.aspectj.lang.enums.BusinessType;
import com.retail.project.retail.domain.InventoryOutOrder;
import com.retail.project.retail.service.IInventoryOutOrderService;
import com.retail.framework.web.controller.BaseController;
import com.retail.framework.web.domain.AjaxResult;
import com.retail.common.utils.poi.ExcelUtil;
import com.retail.framework.web.page.TableDataInfo;
import com.retail.common.utils.SecurityUtils;
import com.retail.project.retail.mapper.RetailStoreMapper;
import com.retail.project.retail.utils.StorePermissionUtils;

/**
 * 出库单Controller
 *
 * <AUTHOR>
 * @date 2025-04-17
 */
@RestController
@RequestMapping("/retail/outorder")
public class InventoryOutOrderController extends BaseController
{
    @Autowired
    private IInventoryOutOrderService inventoryOutOrderService;

    @Autowired
    private RetailStoreMapper retailStoreMapper;

    /**
     * 查询出库单列表
     */
    @PreAuthorize("@ss.hasPermi('retail:outorder:list')")
    @GetMapping("/list")
    public TableDataInfo list(InventoryOutOrder inventoryOutOrder)
    {
        startPage();
        List<InventoryOutOrder> list = inventoryOutOrderService.selectInventoryOutOrderList(inventoryOutOrder);
        return getDataTable(list);
    }

    /**
     * 查询待审核出库单列表（只查询待审核状态的订单）
     */
    @PreAuthorize("@ss.hasPermi('retail:outorder:list')")
    @GetMapping("/approval-list")
    public TableDataInfo approvalList(InventoryOutOrder inventoryOutOrder)
    {
        startPage();

        // 设置流程状态条件，只查询待审核状态的订单
        inventoryOutOrder.setProcessStatus(0); // 0表示待审核

        List<InventoryOutOrder> list = inventoryOutOrderService.selectInventoryOutOrderList(inventoryOutOrder);
        return getDataTable(list);
    }

    /**
     * 导出出库单列表
     */
    @PreAuthorize("@ss.hasPermi('retail:outorder:export')")
    @Log(title = "出库单", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, InventoryOutOrder inventoryOutOrder)
    {
        List<InventoryOutOrder> list = inventoryOutOrderService.selectInventoryOutOrderList(inventoryOutOrder);
        ExcelUtil<InventoryOutOrder> util = new ExcelUtil<InventoryOutOrder>(InventoryOutOrder.class);
        util.exportExcel(response, list, "出库单数据");
    }

    /**
     * 获取出库单详细信息
     */
    @PreAuthorize("@ss.hasPermi('retail:outorder:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(inventoryOutOrderService.selectInventoryOutOrderById(id));
    }

    /**
     * 新增出库单
     */
    @PreAuthorize("@ss.hasPermi('retail:outorder:add')")
    @Log(title = "出库单", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody InventoryOutOrder inventoryOutOrder)
    {
        inventoryOutOrder.setCreateBy(getUsername());
        return toAjax(inventoryOutOrderService.insertInventoryOutOrder(inventoryOutOrder));
    }

    /**
     * 新增出库单及明细
     */
    @PreAuthorize("@ss.hasPermi('retail:outorder:add')")
    @Log(title = "出库单", businessType = BusinessType.INSERT)
    @PostMapping("/save")
    public AjaxResult addWithDetails(@RequestBody Map<String, Object> params)
    {
        return AjaxResult.success();
    }

    /**
     * 审核通过出库单
     */
    @PreAuthorize("@ss.hasPermi('retail:outorder:approve')")
    @Log(title = "审核出库单", businessType = BusinessType.UPDATE)
    @PostMapping("/approve")
    public AjaxResult approve(@RequestBody Map<String, Object> params)
    {
        if (params.get("id") == null) {
            return AjaxResult.error("出库单ID不能为空");
        }
        Long id = Long.valueOf(params.get("id").toString());
        String remark = params.get("remark") != null ? params.get("remark").toString() : "";

        try {
            // 获取订单信息
            InventoryOutOrder order = inventoryOutOrderService.selectInventoryOutOrderById(id);
            if (order == null) {
                return AjaxResult.error("出库单不存在");
            }

            // 验证当前用户是否有权限操作该门店
            Long userId = SecurityUtils.getUserId();

            // 检查用户是否有该门店的权限
            boolean hasAuth = StorePermissionUtils.checkUserStorePermission(order.getStoreId(), userId, retailStoreMapper);
            if (!hasAuth) {
                return AjaxResult.error("您没有权限操作此门店的订单");
            }

            int rows = inventoryOutOrderService.approveOutOrder(id, getUsername(), remark);
            return toAjax(rows);
        } catch (Exception e) {
            return AjaxResult.error(e.getMessage());
        }
    }

    /**
     * 驳回出库单
     */
    @PreAuthorize("@ss.hasPermi('retail:outorder:reject')")
    @Log(title = "驳回出库单", businessType = BusinessType.UPDATE)
    @PostMapping("/reject")
    public AjaxResult reject(@RequestBody Map<String, Object> params)
    {
        if (params.get("id") == null) {
            return AjaxResult.error("出库单ID不能为空");
        }
        Long id = Long.valueOf(params.get("id").toString());
        String rejectReason = params.get("rejectReason") != null ? params.get("rejectReason").toString() : "";

        if (rejectReason.isEmpty()) {
            return AjaxResult.error("驳回原因不能为空");
        }

        try {
            // 获取订单信息
            InventoryOutOrder order = inventoryOutOrderService.selectInventoryOutOrderById(id);
            if (order == null) {
                return AjaxResult.error("出库单不存在");
            }

            // 验证当前用户是否有权限操作该门店
            Long userId = SecurityUtils.getUserId();

            // 检查用户是否有该门店的权限
            boolean hasAuth = StorePermissionUtils.checkUserStorePermission(order.getStoreId(), userId, retailStoreMapper);
            if (!hasAuth) {
                return AjaxResult.error("您没有权限操作此门店的订单");
            }

            int rows = inventoryOutOrderService.rejectOutOrder(id, getUsername(), rejectReason);
            return toAjax(rows);
        } catch (Exception e) {
            return AjaxResult.error(e.getMessage());
        }
    }

    /**
     * 撤销出库单
     */
    @PreAuthorize("@ss.hasPermi('retail:outorder:cancel')")
    @Log(title = "撤销出库单", businessType = BusinessType.UPDATE)
    @PostMapping("/cancel")
    public AjaxResult cancel(@RequestBody Map<String, Object> params)
    {
        if (params.get("id") == null) {
            return AjaxResult.error("出库单ID不能为空");
        }
        Long id = Long.valueOf(params.get("id").toString());
        String cancelReason = params.get("cancelReason") != null ? params.get("cancelReason").toString() : "";

        if (cancelReason.isEmpty()) {
            return AjaxResult.error("撤销原因不能为空");
        }

        try {
            // 获取订单信息
            InventoryOutOrder order = inventoryOutOrderService.selectInventoryOutOrderById(id);
            if (order == null) {
                return AjaxResult.error("出库单不存在");
            }

            // 验证当前用户是否有权限操作该门店
            Long userId = SecurityUtils.getUserId();

            // 检查用户是否有该门店的权限
            boolean hasAuth = StorePermissionUtils.checkUserStorePermission(order.getStoreId(), userId, retailStoreMapper);
            if (!hasAuth) {
                return AjaxResult.error("您没有权限操作此门店的订单");
            }

            int rows = inventoryOutOrderService.cancelOutOrder(id, getUsername(), cancelReason);
            return toAjax(rows);
        } catch (Exception e) {
            return AjaxResult.error(e.getMessage());
        }
    }

    /**
     * 查询出库单审核记录
     */
    @PreAuthorize("@ss.hasPermi('retail:outorder:query')")
    @GetMapping("/audit/{id}")
    public AjaxResult getAuditRecords(@PathVariable("id") Long id)
    {
        return AjaxResult.success(inventoryOutOrderService.selectInventoryOutAuditByOrderId(id));
    }

    /**
     * 申请退货
     */
    @PreAuthorize("@ss.hasPermi('retail:outorder:return')")
    @Log(title = "申请退货", businessType = BusinessType.UPDATE)
    @PostMapping("/apply-return")
    public AjaxResult applyReturn(@RequestBody Map<String, Object> params)
    {
        if (params.get("id") == null) {
            return AjaxResult.error("出库单ID不能为空");
        }
        Long id = Long.valueOf(params.get("id").toString());
        String returnReason = params.get("returnReason") != null ? params.get("returnReason").toString() : "";

        if (returnReason.isEmpty()) {
            return AjaxResult.error("退货原因不能为空");
        }

        try {
            // 获取订单信息
            InventoryOutOrder order = inventoryOutOrderService.selectInventoryOutOrderById(id);
            if (order == null) {
                return AjaxResult.error("出库单不存在");
            }

            // 验证当前用户是否有权限操作该门店
            Long userId = SecurityUtils.getUserId();

            // 检查用户是否有该门店的权限
            boolean hasAuth = StorePermissionUtils.checkUserStorePermission(order.getStoreId(), userId, retailStoreMapper);
            if (!hasAuth) {
                return AjaxResult.error("您没有权限操作此门店的订单");
            }

            int rows = inventoryOutOrderService.applyReturnOutOrder(id, getUsername(), returnReason);
            return toAjax(rows);
        } catch (Exception e) {
            return AjaxResult.error(e.getMessage());
        }
    }

    /**
     * 审核通过退货申请
     */
    @PreAuthorize("@ss.hasPermi('retail:outorder:approve')")
    @Log(title = "审核退货申请", businessType = BusinessType.UPDATE)
    @PostMapping("/approve-return")
    public AjaxResult approveReturn(@RequestBody Map<String, Object> params)
    {
        if (params.get("id") == null) {
            return AjaxResult.error("出库单ID不能为空");
        }
        Long id = Long.valueOf(params.get("id").toString());
        String remark = params.get("remark") != null ? params.get("remark").toString() : "";

        try {
            // 获取订单信息
            InventoryOutOrder order = inventoryOutOrderService.selectInventoryOutOrderById(id);
            if (order == null) {
                return AjaxResult.error("出库单不存在");
            }

            // 验证当前用户是否有权限操作该门店
            Long userId = SecurityUtils.getUserId();

            // 检查用户是否有该门店的权限
            boolean hasAuth = StorePermissionUtils.checkUserStorePermission(order.getStoreId(), userId, retailStoreMapper);
            if (!hasAuth) {
                return AjaxResult.error("您没有权限操作此门店的订单");
            }

            int rows = inventoryOutOrderService.approveReturnOutOrder(id, getUsername(), remark);
            return toAjax(rows);
        } catch (Exception e) {
            return AjaxResult.error(e.getMessage());
        }
    }

    /**
     * 驳回退货申请
     */
    @PreAuthorize("@ss.hasPermi('retail:outorder:reject')")
    @Log(title = "驳回退货申请", businessType = BusinessType.UPDATE)
    @PostMapping("/reject-return")
    public AjaxResult rejectReturn(@RequestBody Map<String, Object> params)
    {
        if (params.get("id") == null) {
            return AjaxResult.error("出库单ID不能为空");
        }
        Long id = Long.valueOf(params.get("id").toString());
        String rejectReason = params.get("rejectReason") != null ? params.get("rejectReason").toString() : "";

        if (rejectReason.isEmpty()) {
            return AjaxResult.error("驳回原因不能为空");
        }

        try {
            // 获取订单信息
            InventoryOutOrder order = inventoryOutOrderService.selectInventoryOutOrderById(id);
            if (order == null) {
                return AjaxResult.error("出库单不存在");
            }

            // 验证当前用户是否有权限操作该门店
            Long userId = SecurityUtils.getUserId();

            // 检查用户是否有该门店的权限
            boolean hasAuth = StorePermissionUtils.checkUserStorePermission(order.getStoreId(), userId, retailStoreMapper);
            if (!hasAuth) {
                return AjaxResult.error("您没有权限操作此门店的订单");
            }

            int rows = inventoryOutOrderService.rejectReturnOutOrder(id, getUsername(), rejectReason);
            return toAjax(rows);
        } catch (Exception e) {
            return AjaxResult.error(e.getMessage());
        }
    }

    /**
     * 撤销退货申请
     */
    @PreAuthorize("@ss.hasPermi('retail:outorder:cancel')")
    @Log(title = "撤销退货申请", businessType = BusinessType.UPDATE)
    @PostMapping("/cancel-return")
    public AjaxResult cancelReturn(@RequestBody Map<String, Object> params)
    {
        if (params.get("id") == null) {
            return AjaxResult.error("出库单ID不能为空");
        }
        Long id = Long.valueOf(params.get("id").toString());
        String cancelReason = params.get("cancelReason") != null ? params.get("cancelReason").toString() : "";

        if (cancelReason.isEmpty()) {
            return AjaxResult.error("撤销原因不能为空");
        }

        try {
            // 获取订单信息
            InventoryOutOrder order = inventoryOutOrderService.selectInventoryOutOrderById(id);
            if (order == null) {
                return AjaxResult.error("出库单不存在");
            }

            // 验证当前用户是否有权限操作该门店
            Long userId = SecurityUtils.getUserId();

            // 检查用户是否有该门店的权限
            boolean hasAuth = StorePermissionUtils.checkUserStorePermission(order.getStoreId(), userId, retailStoreMapper);
            if (!hasAuth) {
                return AjaxResult.error("您没有权限操作此门店的订单");
            }

            int rows = inventoryOutOrderService.cancelReturnOutOrder(id, getUsername(), cancelReason);
            return toAjax(rows);
        } catch (Exception e) {
            return AjaxResult.error(e.getMessage());
        }
    }

    /**
     * 审核通过取消预订单申请
     */
    @PreAuthorize("@ss.hasPermi('retail:outorder:approve')")
    @Log(title = "审核取消预订单", businessType = BusinessType.UPDATE)
    @PostMapping("/approve-cancel-reserve")
    public AjaxResult approveCancelReserve(@RequestBody Map<String, Object> params)
    {
        if (params.get("id") == null) {
            return AjaxResult.error("预订单ID不能为空");
        }
        Long id = Long.valueOf(params.get("id").toString());
        String remark = params.get("remark") != null ? params.get("remark").toString() : "";

        try {
            // 获取订单信息
            InventoryOutOrder order = inventoryOutOrderService.selectInventoryOutOrderById(id);
            if (order == null) {
                return AjaxResult.error("预订单不存在");
            }

            // 验证当前用户是否有权限操作该门店
            Long userId = SecurityUtils.getUserId();

            // 检查用户是否有该门店的权限
            boolean hasAuth = StorePermissionUtils.checkUserStorePermission(order.getStoreId(), userId, retailStoreMapper);
            if (!hasAuth) {
                return AjaxResult.error("您没有权限操作此门店的订单");
            }

            int rows = inventoryOutOrderService.approveCancelReserveOrder(id, getUsername(), remark);
            return toAjax(rows);
        } catch (Exception e) {
            return AjaxResult.error(e.getMessage());
        }
    }

    /**
     * 驳回取消预订单申请
     */
    @PreAuthorize("@ss.hasPermi('retail:outorder:reject')")
    @Log(title = "驳回取消预订单", businessType = BusinessType.UPDATE)
    @PostMapping("/reject-cancel-reserve")
    public AjaxResult rejectCancelReserve(@RequestBody Map<String, Object> params)
    {
        if (params.get("id") == null) {
            return AjaxResult.error("预订单ID不能为空");
        }
        Long id = Long.valueOf(params.get("id").toString());
        String rejectReason = params.get("rejectReason") != null ? params.get("rejectReason").toString() : "";

        if (rejectReason.isEmpty()) {
            return AjaxResult.error("驳回原因不能为空");
        }

        try {
            // 获取订单信息
            InventoryOutOrder order = inventoryOutOrderService.selectInventoryOutOrderById(id);
            if (order == null) {
                return AjaxResult.error("预订单不存在");
            }

            // 验证当前用户是否有权限操作该门店
            Long userId = SecurityUtils.getUserId();

            // 检查用户是否有该门店的权限
            boolean hasAuth = StorePermissionUtils.checkUserStorePermission(order.getStoreId(), userId, retailStoreMapper);
            if (!hasAuth) {
                return AjaxResult.error("您没有权限操作此门店的订单");
            }

            int rows = inventoryOutOrderService.rejectCancelReserveOrder(id, getUsername(), rejectReason);
            return toAjax(rows);
        } catch (Exception e) {
            return AjaxResult.error(e.getMessage());
        }
    }

    /**
     * 审核通过交货申请
     */
    @PreAuthorize("@ss.hasPermi('retail:outorder:approve')")
    @Log(title = "审核交货申请", businessType = BusinessType.UPDATE)
    @PostMapping("/approve-deliver-reserve")
    public AjaxResult approveDeliverReserve(@RequestBody Map<String, Object> params)
    {
        if (params.get("id") == null) {
            return AjaxResult.error("预订单ID不能为空");
        }
        Long id = Long.valueOf(params.get("id").toString());
        String remark = params.get("remark") != null ? params.get("remark").toString() : "";

        try {
            // 获取订单信息
            InventoryOutOrder order = inventoryOutOrderService.selectInventoryOutOrderById(id);
            if (order == null) {
                return AjaxResult.error("预订单不存在");
            }

            // 验证当前用户是否有权限操作该门店
            Long userId = SecurityUtils.getUserId();

            // 检查用户是否有该门店的权限
            boolean hasAuth = StorePermissionUtils.checkUserStorePermission(order.getStoreId(), userId, retailStoreMapper);
            if (!hasAuth) {
                return AjaxResult.error("您没有权限操作此门店的订单");
            }

            int rows = inventoryOutOrderService.approveDeliverReserveOrder(id, getUsername(), remark);
            return toAjax(rows);
        } catch (Exception e) {
            return AjaxResult.error(e.getMessage());
        }
    }

    /**
     * 驳回交货申请
     */
    @PreAuthorize("@ss.hasPermi('retail:outorder:reject')")
    @Log(title = "驳回交货申请", businessType = BusinessType.UPDATE)
    @PostMapping("/reject-deliver-reserve")
    public AjaxResult rejectDeliverReserve(@RequestBody Map<String, Object> params)
    {
        if (params.get("id") == null) {
            return AjaxResult.error("预订单ID不能为空");
        }
        Long id = Long.valueOf(params.get("id").toString());
        String rejectReason = params.get("rejectReason") != null ? params.get("rejectReason").toString() : "";

        if (rejectReason.isEmpty()) {
            return AjaxResult.error("驳回原因不能为空");
        }

        try {
            // 获取订单信息
            InventoryOutOrder order = inventoryOutOrderService.selectInventoryOutOrderById(id);
            if (order == null) {
                return AjaxResult.error("预订单不存在");
            }

            // 验证当前用户是否有权限操作该门店
            Long userId = SecurityUtils.getUserId();

            // 检查用户是否有该门店的权限
            boolean hasAuth = StorePermissionUtils.checkUserStorePermission(order.getStoreId(), userId, retailStoreMapper);
            if (!hasAuth) {
                return AjaxResult.error("您没有权限操作此门店的订单");
            }

            int rows = inventoryOutOrderService.rejectDeliverReserveOrder(id, getUsername(), rejectReason);
            return toAjax(rows);
        } catch (Exception e) {
            return AjaxResult.error(e.getMessage());
        }
    }

    /**
     * 修改出库单
     */
    @PreAuthorize("@ss.hasPermi('retail:outorder:edit')")
    @Log(title = "出库单", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody InventoryOutOrder inventoryOutOrder)
    {
        inventoryOutOrder.setUpdateBy(getUsername());
        return toAjax(inventoryOutOrderService.updateInventoryOutOrder(inventoryOutOrder));
    }

    /**
     * 删除出库单
     */
    @PreAuthorize("@ss.hasPermi('retail:outorder:remove')")
    @Log(title = "出库单", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(inventoryOutOrderService.deleteInventoryOutOrderByIds(ids));
    }
}