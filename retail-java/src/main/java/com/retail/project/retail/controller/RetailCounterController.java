package com.retail.project.retail.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.retail.framework.aspectj.lang.annotation.Log;
import com.retail.framework.aspectj.lang.enums.BusinessType;
import com.retail.project.retail.domain.RetailCounter;
import com.retail.project.retail.service.IRetailCounterService;
import com.retail.framework.web.controller.BaseController;
import com.retail.framework.web.domain.AjaxResult;
import com.retail.common.utils.poi.ExcelUtil;
import com.retail.framework.web.page.TableDataInfo;

/**
 * 柜台管理Controller
 * 
 * <AUTHOR>
 * @date 2025-04-18
 */
@RestController
@RequestMapping("/retail/counter")
public class RetailCounterController extends BaseController
{
    @Autowired
    private IRetailCounterService retailCounterService;

    /**
     * 查询柜台管理列表
     */
    @PreAuthorize("@ss.hasPermi('retail:counter:list')")
    @GetMapping("/list")
    public TableDataInfo list(RetailCounter retailCounter)
    {
        startPage();
        List<RetailCounter> list = retailCounterService.selectRetailCounterList(retailCounter);
        return getDataTable(list);
    }
    
    /**
     * 根据门店ID查询柜台列表
     */
    @PreAuthorize("@ss.hasPermi('retail:counter:list')")
    @GetMapping("/listByStore/{storeId}")
    public AjaxResult listByStore(@PathVariable("storeId") Long storeId)
    {
        RetailCounter counter = new RetailCounter();
        counter.setStoreId(storeId);
        List<RetailCounter> list = retailCounterService.selectRetailCounterList(counter);
        return success(list);
    }

    /**
     * 导出柜台管理列表
     */
    @PreAuthorize("@ss.hasPermi('retail:counter:export')")
    @Log(title = "柜台管理", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, RetailCounter retailCounter)
    {
        List<RetailCounter> list = retailCounterService.selectRetailCounterList(retailCounter);
        ExcelUtil<RetailCounter> util = new ExcelUtil<RetailCounter>(RetailCounter.class);
        util.exportExcel(response, list, "柜台管理数据");
    }

    /**
     * 获取柜台管理详细信息
     */
    @PreAuthorize("@ss.hasPermi('retail:counter:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(retailCounterService.selectRetailCounterById(id));
    }

    /**
     * 新增柜台管理
     */
    @PreAuthorize("@ss.hasPermi('retail:counter:add')")
    @Log(title = "柜台管理", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody RetailCounter retailCounter)
    {
        retailCounter.setCreateBy(getUsername());
        return toAjax(retailCounterService.insertRetailCounter(retailCounter));
    }

    /**
     * 修改柜台管理
     */
    @PreAuthorize("@ss.hasPermi('retail:counter:edit')")
    @Log(title = "柜台管理", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody RetailCounter retailCounter)
    {
        retailCounter.setUpdateBy(getUsername());
        return toAjax(retailCounterService.updateRetailCounter(retailCounter));
    }

    /**
     * 删除柜台管理
     */
    @PreAuthorize("@ss.hasPermi('retail:counter:remove')")
    @Log(title = "柜台管理", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(retailCounterService.deleteRetailCounterByIds(ids));
    }
}
