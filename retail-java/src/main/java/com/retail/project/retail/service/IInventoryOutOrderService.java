package com.retail.project.retail.service;

import java.util.List;
import java.util.Map;
import com.retail.project.retail.domain.InventoryOutOrder;
import com.retail.project.retail.domain.InventoryOutDetail;
import com.retail.project.retail.domain.InventoryOutAudit;

/**
 * 出库单Service接口
 *
 * <AUTHOR>
 * @date 2025-04-17
 */
public interface IInventoryOutOrderService
{
    /**
     * 查询出库单
     *
     * @param id 出库单主键
     * @return 出库单
     */
    public InventoryOutOrder selectInventoryOutOrderById(Long id);

    /**
     * 查询出库单列表
     *
     * @param inventoryOutOrder 出库单
     * @return 出库单集合
     */
    public List<InventoryOutOrder> selectInventoryOutOrderList(InventoryOutOrder inventoryOutOrder);

    /**
     * 新增出库单
     *
     * @param inventoryOutOrder 出库单
     * @return 结果
     */
    public int insertInventoryOutOrder(InventoryOutOrder inventoryOutOrder);

    /**
     * 修改出库单
     *
     * @param inventoryOutOrder 出库单
     * @return 结果
     */
    public int updateInventoryOutOrder(InventoryOutOrder inventoryOutOrder);

    /**
     * 批量删除出库单
     *
     * @param ids 需要删除的出库单主键集合
     * @return 结果
     */
    public int deleteInventoryOutOrderByIds(Long[] ids);

    /**
     * 删除出库单信息
     *
     * @param id 出库单主键
     * @return 结果
     */
    public int deleteInventoryOutOrderById(Long id);

    /**
     * 保存出库单及明细
     *
     * @param inventoryOutOrder 出库单
     * @param detailList 出库明细列表
     * @return 结果
     */
    public int saveInventoryOutOrderWithDetails(InventoryOutOrder inventoryOutOrder, List<InventoryOutDetail> detailList);

    /**
     * 审核通过出库单
     *
     * @param id 出库单ID
     * @param approveBy 审核人
     * @param remark 审核备注
     * @return 结果
     */
    public int approveOutOrder(Long id, String approveBy, String remark);

    /**
     * 驳回出库单
     *
     * @param id 出库单ID
     * @param rejectBy 驳回人
     * @param rejectReason 驳回原因
     * @return 结果
     */
    public int rejectOutOrder(Long id, String rejectBy, String rejectReason);

    /**
     * 撤销出库单
     *
     * @param id 出库单ID
     * @param cancelBy 撤销人
     * @param cancelReason 撤销原因
     * @return 结果
     */
    public int cancelOutOrder(Long id, String cancelBy, String cancelReason);

    /**
     * 根据出库单ID查询审核记录列表
     *
     * @param orderId 出库单ID
     * @return 出库单审核记录集合
     */
    public List<InventoryOutAudit> selectInventoryOutAuditByOrderId(Long orderId);

    /**
     * 申请退货
     *
     * @param id 出库单ID
     * @param applyBy 申请人
     * @param applyReason 退货原因
     * @return 结果
     */
    public int applyReturnOutOrder(Long id, String applyBy, String applyReason);

    /**
     * 审核通过退货申请
     *
     * @param id 出库单ID
     * @param approveBy 审核人
     * @param remark 审核备注
     * @return 结果
     */
    public int approveReturnOutOrder(Long id, String approveBy, String remark);

    /**
     * 驳回退货申请
     *
     * @param id 出库单ID
     * @param rejectBy 驳回人
     * @param rejectReason 驳回原因
     * @return 结果
     */
    public int rejectReturnOutOrder(Long id, String rejectBy, String rejectReason);

    /**
     * 撤销退货申请
     *
     * @param id 出库单ID
     * @param cancelBy 撤销人
     * @param cancelReason 撤销原因
     * @return 结果
     */
    public int cancelReturnOutOrder(Long id, String cancelBy, String cancelReason);

    /**
     * 查询出库单列表（用于数据分析，不应用数据范围过滤）
     *
     * @param inventoryOutOrder 出库单
     * @return 出库单集合
     */
    public List<InventoryOutOrder> selectInventoryOutOrderListForAnalysis(InventoryOutOrder inventoryOutOrder);

    /**
     * 保存预订单及明细
     *
     * @param inventoryOutOrder 预订单
     * @param detailList 预订明细列表
     * @return 结果
     */
    public int saveReserveOrderWithDetails(InventoryOutOrder inventoryOutOrder, List<InventoryOutDetail> detailList);

    /**
     * 交货预订单
     *
     * @param inventoryOutOrder 预订单
     * @param remark 交货备注
     * @return 结果
     */
    public int deliverReserveOrder(InventoryOutOrder inventoryOutOrder, String remark);

    /**
     * 从Map创建预订单
     *
     * @param params 预订单参数
     * @return 结果
     */
    public int createReserveOrderFromMap(Map<String, Object> params);

    /**
     * 取消预订单（申请取消）
     *
     * @param id 预订单ID
     * @param cancelBy 取消人
     * @param cancelReason 取消原因
     * @return 结果
     */
    public int cancelReserveOrder(Long id, String cancelBy, String cancelReason);

    /**
     * 审核通过取消预订单申请
     *
     * @param id 预订单ID
     * @param approveBy 审核人
     * @param remark 审核备注
     * @return 结果
     */
    public int approveCancelReserveOrder(Long id, String approveBy, String remark);

    /**
     * 驳回取消预订单申请
     *
     * @param id 预订单ID
     * @param rejectBy 驳回人
     * @param rejectReason 驳回原因
     * @return 结果
     */
    public int rejectCancelReserveOrder(Long id, String rejectBy, String rejectReason);

    /**
     * 审核通过交货申请
     *
     * @param id 预订单ID
     * @param approveBy 审核人
     * @param remark 审核备注
     * @return 结果
     */
    public int approveDeliverReserveOrder(Long id, String approveBy, String remark);

    /**
     * 驳回交货申请
     *
     * @param id 预订单ID
     * @param rejectBy 驳回人
     * @param rejectReason 驳回原因
     * @return 结果
     */
    public int rejectDeliverReserveOrder(Long id, String rejectBy, String rejectReason);
}