package com.retail.project.retail.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.retail.framework.aspectj.lang.annotation.Log;
import com.retail.framework.aspectj.lang.enums.BusinessType;
import com.retail.project.retail.domain.RetailProducts;
import com.retail.project.retail.service.IRetailProductsService;
import com.retail.framework.web.controller.BaseController;
import com.retail.framework.web.domain.AjaxResult;
import com.retail.common.utils.poi.ExcelUtil;
import com.retail.framework.web.page.TableDataInfo;

/**
 * 商品Controller
 *
 * <AUTHOR>
 * @date 2025-04-17
 */
@RestController
@RequestMapping("/retail/products")
public class RetailProductsController extends BaseController
{
    @Autowired
    private IRetailProductsService retailProductsService;

    /**
     * 查询商品列表
     */
    @PreAuthorize("@ss.hasPermi('retail:products:list')")
    @GetMapping("/list")
    public TableDataInfo list(RetailProducts retailProducts)
    {
        startPage();
        List<RetailProducts> list = retailProductsService.selectRetailProductsList(retailProducts);
        return getDataTable(list);
    }

    /**
     * 导出商品列表
     */
    @PreAuthorize("@ss.hasPermi('retail:products:export')")
    @Log(title = "商品", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, RetailProducts retailProducts)
    {
        List<RetailProducts> list = retailProductsService.selectRetailProductsList(retailProducts);
        ExcelUtil<RetailProducts> util = new ExcelUtil<RetailProducts>(RetailProducts.class);
        util.exportExcel(response, list, "商品数据");
    }

    /**
     * 获取商品详细信息
     */
    @PreAuthorize("@ss.hasPermi('retail:products:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(retailProductsService.selectRetailProductsById(id));
    }

    /**
     * 新增商品
     */
    @PreAuthorize("@ss.hasPermi('retail:products:add')")
    @Log(title = "商品", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody RetailProducts retailProducts)
    {
        retailProducts.setCreateBy(getUsername());
        return toAjax(retailProductsService.insertRetailProducts(retailProducts));
    }

    /**
     * 修改商品
     */
    @PreAuthorize("@ss.hasPermi('retail:products:edit')")
    @Log(title = "商品", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody RetailProducts retailProducts)
    {
        retailProducts.setUpdateBy(getUsername());
        return toAjax(retailProductsService.updateRetailProducts(retailProducts));
    }

    /**
     * 删除商品
     */
    @PreAuthorize("@ss.hasPermi('retail:products:remove')")
    @Log(title = "商品", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(retailProductsService.deleteRetailProductsByIds(ids));
    }

    /**
     * 查询所有商品列表（不分页）
     */
    @PreAuthorize("@ss.hasPermi('retail:products:list')")
    @GetMapping("/all")
    public AjaxResult listAll(RetailProducts retailProducts)
    {
        List<RetailProducts> list = retailProductsService.selectRetailProductsList(retailProducts);
        return success(list);
    }
}
