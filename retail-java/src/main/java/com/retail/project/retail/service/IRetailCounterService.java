package com.retail.project.retail.service;

import java.util.List;
import com.retail.project.retail.domain.RetailCounter;

/**
 * 柜台管理Service接口
 * 
 * <AUTHOR>
 * @date 2025-04-18
 */
public interface IRetailCounterService 
{
    /**
     * 查询柜台管理
     * 
     * @param id 柜台管理主键
     * @return 柜台管理
     */
    public RetailCounter selectRetailCounterById(Long id);

    /**
     * 查询柜台管理列表
     * 
     * @param retailCounter 柜台管理
     * @return 柜台管理集合
     */
    public List<RetailCounter> selectRetailCounterList(RetailCounter retailCounter);

    /**
     * 新增柜台管理
     * 
     * @param retailCounter 柜台管理
     * @return 结果
     */
    public int insertRetailCounter(RetailCounter retailCounter);

    /**
     * 修改柜台管理
     * 
     * @param retailCounter 柜台管理
     * @return 结果
     */
    public int updateRetailCounter(RetailCounter retailCounter);

    /**
     * 批量删除柜台管理
     * 
     * @param ids 需要删除的柜台管理主键集合
     * @return 结果
     */
    public int deleteRetailCounterByIds(Long[] ids);

    /**
     * 删除柜台管理信息
     * 
     * @param id 柜台管理主键
     * @return 结果
     */
    public int deleteRetailCounterById(Long id);
}
