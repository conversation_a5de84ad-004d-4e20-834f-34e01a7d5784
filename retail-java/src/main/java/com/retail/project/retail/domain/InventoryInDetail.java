package com.retail.project.retail.domain;

import java.math.BigDecimal;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.retail.framework.aspectj.lang.annotation.Excel;
import com.retail.framework.web.domain.BaseEntity;

/**
 * 入库明细对象 inventory_in_detail
 * 
 * <AUTHOR>
 * @date 2025-04-17
 */
public class InventoryInDetail extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 明细ID */
    private Long id;

    /** 入库单ID */
    @Excel(name = "入库单ID")
    private Long orderId;

    /** 商品ID */
    @Excel(name = "商品ID")
    private Long productId;

    /** 门店ID */
    @Excel(name = "门店ID")
    private Long storeId;

    /** 柜台ID */
    @Excel(name = "柜台ID")
    private Long counterId;

    /** 入库数量 */
    @Excel(name = "入库数量")
    private Integer quantity;

    /** 进货价 */
    @Excel(name = "进货价")
    private BigDecimal purchasePrice;

    /** 批次号 */
    @Excel(name = "批次号")
    private String batchNumber;

    /** 商品名称 */
    @Excel(name = "商品名称")
    private String productName;

    /** 商品货号 */
    @Excel(name = "商品货号")
    private String productNumber;
    
    /** 商品图片 */
    private String productImage;

    /** 门店名称 */
    @Excel(name = "门店名称")
    private String storeName;

    /** 柜台名称 */
    @Excel(name = "柜台名称")
    private String counterName;

    public void setId(Long id) 
    {
        this.id = id;
    }

    public Long getId() 
    {
        return id;
    }
    
    public void setOrderId(Long orderId) 
    {
        this.orderId = orderId;
    }

    public Long getOrderId() 
    {
        return orderId;
    }
    
    public void setProductId(Long productId) 
    {
        this.productId = productId;
    }

    public Long getProductId() 
    {
        return productId;
    }
    
    public void setStoreId(Long storeId) 
    {
        this.storeId = storeId;
    }

    public Long getStoreId() 
    {
        return storeId;
    }
    
    public void setCounterId(Long counterId) 
    {
        this.counterId = counterId;
    }

    public Long getCounterId() 
    {
        return counterId;
    }
    
    public void setQuantity(Integer quantity) 
    {
        this.quantity = quantity;
    }

    public Integer getQuantity() 
    {
        return quantity;
    }
    
    public void setPurchasePrice(BigDecimal purchasePrice) 
    {
        this.purchasePrice = purchasePrice;
    }

    public BigDecimal getPurchasePrice() 
    {
        return purchasePrice;
    }
    
    public void setBatchNumber(String batchNumber) 
    {
        this.batchNumber = batchNumber;
    }

    public String getBatchNumber() 
    {
        return batchNumber;
    }
    
    public String getProductName() 
    {
        return productName;
    }

    public void setProductName(String productName) 
    {
        this.productName = productName;
    }
    
    public String getProductNumber() 
    {
        return productNumber;
    }

    public void setProductNumber(String productNumber) 
    {
        this.productNumber = productNumber;
    }
    
    public String getProductImage() 
    {
        return productImage;
    }

    public void setProductImage(String productImage) 
    {
        this.productImage = productImage;
    }
    
    public String getStoreName() 
    {
        return storeName;
    }

    public void setStoreName(String storeName) 
    {
        this.storeName = storeName;
    }
    
    public String getCounterName() 
    {
        return counterName;
    }

    public void setCounterName(String counterName) 
    {
        this.counterName = counterName;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("orderId", getOrderId())
            .append("productId", getProductId())
            .append("storeId", getStoreId())
            .append("counterId", getCounterId())
            .append("quantity", getQuantity())
            .append("purchasePrice", getPurchasePrice())
            .append("batchNumber", getBatchNumber())
            .append("productImage", getProductImage())
            .append("createBy", getCreateBy())
            .append("createTime", getCreateTime())
            .append("updateBy", getUpdateBy())
            .append("updateTime", getUpdateTime())
            .append("remark", getRemark())
            .toString();
    }
} 