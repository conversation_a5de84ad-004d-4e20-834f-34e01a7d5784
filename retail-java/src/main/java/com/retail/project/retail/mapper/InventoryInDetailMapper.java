package com.retail.project.retail.mapper;

import java.util.List;
import com.retail.project.retail.domain.InventoryInDetail;

/**
 * 入库明细Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-04-17
 */
public interface InventoryInDetailMapper 
{
    /**
     * 查询入库明细
     * 
     * @param id 入库明细主键
     * @return 入库明细
     */
    public InventoryInDetail selectInventoryInDetailById(Long id);

    /**
     * 查询入库明细列表
     * 
     * @param inventoryInDetail 入库明细
     * @return 入库明细集合
     */
    public List<InventoryInDetail> selectInventoryInDetailList(InventoryInDetail inventoryInDetail);

    /**
     * 根据入库单ID查询入库明细列表
     * 
     * @param orderId 入库单ID
     * @return 入库明细集合
     */
    public List<InventoryInDetail> selectInventoryInDetailByOrderId(Long orderId);

    /**
     * 新增入库明细
     * 
     * @param inventoryInDetail 入库明细
     * @return 结果
     */
    public int insertInventoryInDetail(InventoryInDetail inventoryInDetail);

    /**
     * 批量新增入库明细
     * 
     * @param inventoryInDetailList 入库明细列表
     * @return 结果
     */
    public int batchInsertInventoryInDetail(List<InventoryInDetail> inventoryInDetailList);

    /**
     * 修改入库明细
     * 
     * @param inventoryInDetail 入库明细
     * @return 结果
     */
    public int updateInventoryInDetail(InventoryInDetail inventoryInDetail);

    /**
     * 删除入库明细
     * 
     * @param id 入库明细主键
     * @return 结果
     */
    public int deleteInventoryInDetailById(Long id);

    /**
     * 批量删除入库明细
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteInventoryInDetailByIds(Long[] ids);

    /**
     * 根据入库单ID删除入库明细
     * 
     * @param orderId 入库单ID
     * @return 结果
     */
    public int deleteInventoryInDetailByOrderId(Long orderId);
} 