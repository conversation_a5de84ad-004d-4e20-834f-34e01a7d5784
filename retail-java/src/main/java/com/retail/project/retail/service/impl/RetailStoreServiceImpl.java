package com.retail.project.retail.service.impl;

import java.util.Date;
import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import com.retail.project.retail.mapper.RetailStoreMapper;
import com.retail.project.retail.domain.RetailStore;
import com.retail.project.retail.service.IRetailStoreService;

/**
 * 门店管理Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-04-18
 */
@Service
public class RetailStoreServiceImpl implements IRetailStoreService 
{
    @Autowired
    private RetailStoreMapper retailStoreMapper;

    /**
     * 查询门店管理
     * 
     * @param id 门店管理主键
     * @return 门店管理
     */
    @Override
    public RetailStore selectRetailStoreById(Long id)
    {
        return retailStoreMapper.selectRetailStoreById(id);
    }

    /**
     * 查询门店管理列表
     * 
     * @param retailStore 门店管理
     * @return 门店管理
     */
    @Override
    public List<RetailStore> selectRetailStoreList(RetailStore retailStore)
    {
        return retailStoreMapper.selectRetailStoreList(retailStore);
    }

    /**
     * 新增门店管理
     * 
     * @param retailStore 门店管理
     * @return 结果
     */
    @Override
    public int insertRetailStore(RetailStore retailStore)
    {
        retailStore.setCreateTime(new Date());
        return retailStoreMapper.insertRetailStore(retailStore);
    }
    
    /**
     * 保存门店信息
     * 
     * @param retailStore 门店信息
     * @return 结果
     */
    @Override
    @Transactional
    public int insertRetailStoreWithUsers(RetailStore retailStore)
    {
        retailStore.setCreateTime(new Date());
        // userId 和 userName 已经在请求对象中处理，直接插入即可
        return retailStoreMapper.insertRetailStore(retailStore);
    }

    /**
     * 修改门店管理
     * 
     * @param retailStore 门店管理
     * @return 结果
     */
    @Override
    public int updateRetailStore(RetailStore retailStore)
    {
        retailStore.setUpdateTime(new Date());
        return retailStoreMapper.updateRetailStore(retailStore);
    }
    
    /**
     * 更新门店信息
     * 
     * @param retailStore 门店信息
     * @return 结果
     */
    @Override
    @Transactional
    public int updateRetailStoreWithUsers(RetailStore retailStore)
    {
        retailStore.setUpdateTime(new Date());
        // userId 和 userName 已经在请求对象中处理，直接更新即可
        return retailStoreMapper.updateRetailStore(retailStore);
    }

    /**
     * 批量删除门店管理
     * 
     * @param ids 需要删除的门店管理主键
     * @return 结果
     */
    @Override
    @Transactional
    public int deleteRetailStoreByIds(Long[] ids)
    {
        return retailStoreMapper.deleteRetailStoreByIds(ids);
    }

    /**
     * 删除门店管理信息
     * 
     * @param id 门店管理主键
     * @return 结果
     */
    @Override
    @Transactional
    public int deleteRetailStoreById(Long id)
    {
        return retailStoreMapper.deleteRetailStoreById(id);
    }
}
