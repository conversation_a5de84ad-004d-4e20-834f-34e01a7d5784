package com.retail.project.retail.domain;

import java.util.List;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.retail.framework.aspectj.lang.annotation.Excel;
import com.retail.framework.web.domain.BaseEntity;

/**
 * 入库单对象 inventory_in_order
 * 
 * <AUTHOR>
 * @date 2025-04-17
 */
public class InventoryInOrder extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 入库单ID */
    private Long id;

    /** 入库单号 */
    @Excel(name = "入库单号")
    private String orderNumber;

    /** 渠道ID */
    @Excel(name = "渠道ID")
    private Long channelId;

    /** 是否期货 */
    @Excel(name = "是否期货")
    private Boolean isFutures;

    /** 期货物流 */
    @Excel(name = "期货物流")
    private String futuresLogistics;

    /** 渠道名称 */
    @Excel(name = "渠道名称")
    private String channelName;
    
    /** 入库明细列表 */
    private List<InventoryInDetail> detailList;

    public void setId(Long id) 
    {
        this.id = id;
    }

    public Long getId() 
    {
        return id;
    }
    
    public void setOrderNumber(String orderNumber) 
    {
        this.orderNumber = orderNumber;
    }

    public String getOrderNumber() 
    {
        return orderNumber;
    }
    
    public void setChannelId(Long channelId) 
    {
        this.channelId = channelId;
    }

    public Long getChannelId() 
    {
        return channelId;
    }
    
    public void setIsFutures(Boolean isFutures) 
    {
        this.isFutures = isFutures;
    }

    public Boolean getIsFutures() 
    {
        return isFutures;
    }
    
    public void setFuturesLogistics(String futuresLogistics) 
    {
        this.futuresLogistics = futuresLogistics;
    }

    public String getFuturesLogistics() 
    {
        return futuresLogistics;
    }
    
    public String getChannelName() 
    {
        return channelName;
    }

    public void setChannelName(String channelName) 
    {
        this.channelName = channelName;
    }
    
    public List<InventoryInDetail> getDetailList() 
    {
        return detailList;
    }

    public void setDetailList(List<InventoryInDetail> detailList) 
    {
        this.detailList = detailList;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("orderNumber", getOrderNumber())
            .append("channelId", getChannelId())
            .append("isFutures", getIsFutures())
            .append("futuresLogistics", getFuturesLogistics())
            .append("createBy", getCreateBy())
            .append("createTime", getCreateTime())
            .append("updateBy", getUpdateBy())
            .append("updateTime", getUpdateTime())
            .append("remark", getRemark())
            .append("channelName", getChannelName())
            .append("detailList", getDetailList())
            .toString();
    }
} 