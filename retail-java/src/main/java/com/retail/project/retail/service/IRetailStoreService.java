package com.retail.project.retail.service;

import java.util.List;
import com.retail.project.retail.domain.RetailStore;

/**
 * 门店管理Service接口
 * 
 * <AUTHOR>
 * @date 2025-04-18
 */
public interface IRetailStoreService 
{
    /**
     * 查询门店管理
     * 
     * @param id 门店管理主键
     * @return 门店管理
     */
    public RetailStore selectRetailStoreById(Long id);

    /**
     * 查询门店管理列表
     * 
     * @param retailStore 门店管理
     * @return 门店管理集合
     */
    public List<RetailStore> selectRetailStoreList(RetailStore retailStore);

    /**
     * 新增门店管理
     * 
     * @param retailStore 门店管理
     * @return 结果
     */
    public int insertRetailStore(RetailStore retailStore);
    
    /**
     * 保存门店信息
     * 
     * @param retailStore 门店管理
     * @return 结果
     */
    public int insertRetailStoreWithUsers(RetailStore retailStore);

    /**
     * 修改门店管理
     * 
     * @param retailStore 门店管理
     * @return 结果
     */
    public int updateRetailStore(RetailStore retailStore);
    
    /**
     * 更新门店信息
     * 
     * @param retailStore 门店管理
     * @return 结果
     */
    public int updateRetailStoreWithUsers(RetailStore retailStore);

    /**
     * 批量删除门店管理
     * 
     * @param ids 需要删除的门店管理主键集合
     * @return 结果
     */
    public int deleteRetailStoreByIds(Long[] ids);

    /**
     * 删除门店管理信息
     * 
     * @param id 门店管理主键
     * @return 结果
     */
    public int deleteRetailStoreById(Long id);
}
