package com.retail.project.retail.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.retail.framework.aspectj.lang.annotation.Log;
import com.retail.framework.aspectj.lang.enums.BusinessType;
import com.retail.project.retail.domain.InventoryInOrder;
import com.retail.project.retail.service.IInventoryInOrderService;
import com.retail.framework.web.controller.BaseController;
import com.retail.framework.web.domain.AjaxResult;
import com.retail.common.utils.poi.ExcelUtil;
import com.retail.framework.web.page.TableDataInfo;

/**
 * 入库单Controller
 * 
 * <AUTHOR>
 * @date 2025-04-17
 */
@RestController
@RequestMapping("/retail/inorder")
public class InventoryInOrderController extends BaseController
{
    @Autowired
    private IInventoryInOrderService inventoryInOrderService;

    /**
     * 查询入库单列表
     */
    @PreAuthorize("@ss.hasPermi('retail:inorder:list')")
    @GetMapping("/list")
    public TableDataInfo list(InventoryInOrder inventoryInOrder)
    {
        startPage();
        List<InventoryInOrder> list = inventoryInOrderService.selectInventoryInOrderList(inventoryInOrder);
        return getDataTable(list);
    }

    /**
     * 导出入库单列表
     */
    @PreAuthorize("@ss.hasPermi('retail:inorder:export')")
    @Log(title = "入库单", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, InventoryInOrder inventoryInOrder)
    {
        List<InventoryInOrder> list = inventoryInOrderService.selectInventoryInOrderList(inventoryInOrder);
        ExcelUtil<InventoryInOrder> util = new ExcelUtil<InventoryInOrder>(InventoryInOrder.class);
        util.exportExcel(response, list, "入库单数据");
    }

    /**
     * 获取入库单详细信息
     */
    @PreAuthorize("@ss.hasPermi('retail:inorder:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(inventoryInOrderService.selectInventoryInOrderById(id));
    }

    /**
     * 新增入库单
     */
    @PreAuthorize("@ss.hasPermi('retail:inorder:add')")
    @Log(title = "入库单", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody InventoryInOrder inventoryInOrder)
    {
        inventoryInOrder.setCreateBy(getUsername());
        return toAjax(inventoryInOrderService.insertInventoryInOrder(inventoryInOrder));
    }
    
    /**
     * 新增入库单及明细
     */
    @PreAuthorize("@ss.hasPermi('retail:inorder:add')")
    @Log(title = "入库单", businessType = BusinessType.INSERT)
    @PostMapping("/save")
    public AjaxResult saveWithDetails(@RequestBody InventoryInOrder inventoryInOrder)
    {
        inventoryInOrder.setCreateBy(getUsername());
        return toAjax(inventoryInOrderService.saveInventoryInOrderWithDetails(inventoryInOrder, inventoryInOrder.getDetailList()));
    }

    /**
     * 修改入库单
     */
    @PreAuthorize("@ss.hasPermi('retail:inorder:edit')")
    @Log(title = "入库单", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody InventoryInOrder inventoryInOrder)
    {
        inventoryInOrder.setUpdateBy(getUsername());
        return toAjax(inventoryInOrderService.updateInventoryInOrder(inventoryInOrder));
    }

    /**
     * 删除入库单
     */
    @PreAuthorize("@ss.hasPermi('retail:inorder:remove')")
    @Log(title = "入库单", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(inventoryInOrderService.deleteInventoryInOrderByIds(ids));
    }
}