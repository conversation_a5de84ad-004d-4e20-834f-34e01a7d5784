package com.retail.project.retail.domain;

import java.math.BigDecimal;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.retail.framework.aspectj.lang.annotation.Excel;
import com.retail.framework.web.domain.BaseEntity;

/**
 * 商品对象 retail_products
 * 
 * <AUTHOR>
 * @date 2025-04-17
 */
public class RetailProducts extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** ID */
    private Long id;

    /** 货号 */
    @Excel(name = "货号")
    private String productNumber;

    /** 商品名称 */
    @Excel(name = "商品名称")
    private String productName;

    /** 商品图 */
    @Excel(name = "商品图")
    private String productImage;

    /** 品牌 */
    @Excel(name = "品牌")
    private String brand;

    /** 品类 */
    @Excel(name = "品类")
    private String category;

    /** 规格 */
    @Excel(name = "规格")
    private String specification;

    /** 颜色 */
    @Excel(name = "颜色")
    private String color;

    /** 材质 */
    @Excel(name = "材质")
    private String material;

    /** 原产地 */
    @Excel(name = "原产地")
    private String origin;

    /** 执行标准 */
    @Excel(name = "执行标准")
    private String standard;

    /** 安全类别 */
    @Excel(name = "安全类别")
    private String safetyCategory;

    /** 产品等级 */
    @Excel(name = "产品等级")
    private String productGrade;
    
    /** 国内参考价 */
    @Excel(name = "国内参考价")
    private BigDecimal referencePrice;

    /** 门店零售价 */
    @Excel(name = "门店零售价")
    private BigDecimal retailPrice;

    public void setId(Long id) 
    {
        this.id = id;
    }

    public Long getId() 
    {
        return id;
    }

    public void setProductNumber(String productNumber) 
    {
        this.productNumber = productNumber;
    }

    public String getProductNumber() 
    {
        return productNumber;
    }

    public void setProductName(String productName) 
    {
        this.productName = productName;
    }

    public String getProductName() 
    {
        return productName;
    }

    public void setProductImage(String productImage) 
    {
        this.productImage = productImage;
    }

    public String getProductImage() 
    {
        return productImage;
    }

    public void setBrand(String brand) 
    {
        this.brand = brand;
    }

    public String getBrand() 
    {
        return brand;
    }

    public void setCategory(String category) 
    {
        this.category = category;
    }

    public String getCategory() 
    {
        return category;
    }

    public void setSpecification(String specification) 
    {
        this.specification = specification;
    }

    public String getSpecification() 
    {
        return specification;
    }

    public void setColor(String color) 
    {
        this.color = color;
    }

    public String getColor() 
    {
        return color;
    }

    public void setMaterial(String material) 
    {
        this.material = material;
    }

    public String getMaterial() 
    {
        return material;
    }

    public void setOrigin(String origin) 
    {
        this.origin = origin;
    }

    public String getOrigin() 
    {
        return origin;
    }

    public void setStandard(String standard) 
    {
        this.standard = standard;
    }

    public String getStandard() 
    {
        return standard;
    }

    public void setSafetyCategory(String safetyCategory) 
    {
        this.safetyCategory = safetyCategory;
    }

    public String getSafetyCategory() 
    {
        return safetyCategory;
    }

    public void setProductGrade(String productGrade) 
    {
        this.productGrade = productGrade;
    }

    public String getProductGrade() 
    {
        return productGrade;
    }
    
    public BigDecimal getReferencePrice() 
    {
        return referencePrice;
    }

    public void setReferencePrice(BigDecimal referencePrice) 
    {
        this.referencePrice = referencePrice;
    }

    public BigDecimal getRetailPrice() 
    {
        return retailPrice;
    }

    public void setRetailPrice(BigDecimal retailPrice) 
    {
        this.retailPrice = retailPrice;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("productNumber", getProductNumber())
            .append("productName", getProductName())
            .append("productImage", getProductImage())
            .append("brand", getBrand())
            .append("category", getCategory())
            .append("specification", getSpecification())
            .append("color", getColor())
            .append("material", getMaterial())
            .append("origin", getOrigin())
            .append("standard", getStandard())
            .append("safetyCategory", getSafetyCategory())
            .append("productGrade", getProductGrade())
            .append("referencePrice", getReferencePrice())
            .append("retailPrice", getRetailPrice())
            .append("createBy", getCreateBy())
            .append("createTime", getCreateTime())
            .append("updateBy", getUpdateBy())
            .append("updateTime", getUpdateTime())
            .append("remark", getRemark())
            .toString();
    }
}
