package com.retail.project.retail.service.impl;

import java.util.List;
import com.retail.common.utils.DateUtils;
import com.retail.common.utils.SecurityUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.retail.project.retail.mapper.InventoryOutFlowMapper;
import com.retail.project.retail.domain.InventoryOutFlow;
import com.retail.project.retail.service.IInventoryOutFlowService;

/**
 * 出库审核流程Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-04-27
 */
@Service
public class InventoryOutFlowServiceImpl implements IInventoryOutFlowService 
{
    @Autowired
    private InventoryOutFlowMapper inventoryOutFlowMapper;

    /**
     * 查询出库审核流程
     * 
     * @param id 出库审核流程主键
     * @return 出库审核流程
     */
    @Override
    public InventoryOutFlow selectInventoryOutFlowById(Long id)
    {
        return inventoryOutFlowMapper.selectInventoryOutFlowById(id);
    }

    /**
     * 查询出库审核流程列表
     * 
     * @param inventoryOutFlow 出库审核流程
     * @return 出库审核流程
     */
    @Override
    public List<InventoryOutFlow> selectInventoryOutFlowList(InventoryOutFlow inventoryOutFlow)
    {
        return inventoryOutFlowMapper.selectInventoryOutFlowList(inventoryOutFlow);
    }

    /**
     * 新增出库审核流程
     * 
     * @param inventoryOutFlow 出库审核流程
     * @return 结果
     */
    @Override
    public int insertInventoryOutFlow(InventoryOutFlow inventoryOutFlow)
    {
        inventoryOutFlow.setCreateBy(SecurityUtils.getUsername());
        inventoryOutFlow.setCreateTime(DateUtils.getNowDate());
        return inventoryOutFlowMapper.insertInventoryOutFlow(inventoryOutFlow);
    }

    /**
     * 修改出库审核流程
     * 
     * @param inventoryOutFlow 出库审核流程
     * @return 结果
     */
    @Override
    public int updateInventoryOutFlow(InventoryOutFlow inventoryOutFlow)
    {
        inventoryOutFlow.setUpdateBy(SecurityUtils.getUsername());
        inventoryOutFlow.setUpdateTime(DateUtils.getNowDate());
        return inventoryOutFlowMapper.updateInventoryOutFlow(inventoryOutFlow);
    }

    /**
     * 批量删除出库审核流程
     * 
     * @param ids 需要删除的出库审核流程主键
     * @return 结果
     */
    @Override
    public int deleteInventoryOutFlowByIds(Long[] ids)
    {
        return inventoryOutFlowMapper.deleteInventoryOutFlowByIds(ids);
    }

    /**
     * 删除出库审核流程信息
     * 
     * @param id 出库审核流程主键
     * @return 结果
     */
    @Override
    public int deleteInventoryOutFlowById(Long id)
    {
        return inventoryOutFlowMapper.deleteInventoryOutFlowById(id);
    }
} 