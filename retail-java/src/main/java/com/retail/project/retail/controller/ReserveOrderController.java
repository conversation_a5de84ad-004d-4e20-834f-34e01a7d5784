package com.retail.project.retail.controller;

import java.math.BigDecimal;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.retail.framework.aspectj.lang.annotation.Log;
import com.retail.framework.aspectj.lang.enums.BusinessType;
import com.retail.project.retail.domain.InventoryOutOrder;
import com.retail.project.retail.domain.InventoryOutDetail;
import com.retail.project.retail.service.IInventoryOutOrderService;
import com.retail.framework.web.controller.BaseController;
import com.retail.framework.web.domain.AjaxResult;
import com.retail.framework.web.page.TableDataInfo;
import com.retail.common.utils.SecurityUtils;
import com.retail.project.retail.mapper.RetailStoreMapper;
import com.retail.project.retail.utils.StorePermissionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * 预订单Controller
 *
 * <AUTHOR>
 * @date 2025-04-25
 */
@RestController
@RequestMapping("/retail/reserve")
public class ReserveOrderController extends BaseController
{
    private static final Logger log = LoggerFactory.getLogger(ReserveOrderController.class);

    @Autowired
    private IInventoryOutOrderService inventoryOutOrderService;

    @Autowired
    private RetailStoreMapper retailStoreMapper;

    /**
     * 取消预订单
     */
    @PreAuthorize("@ss.hasPermi('retail:outorder:reservecancel')")
    @Log(title = "取消预订单", businessType = BusinessType.UPDATE)
    @PostMapping("/cancel")
    public AjaxResult cancelReserveOrder(@RequestBody Map<String, Object> params)
    {
        if (params.get("id") == null) {
            return AjaxResult.error("预订单ID不能为空");
        }
        Long id = Long.valueOf(params.get("id").toString());
        String cancelReason = params.get("cancelReason") != null ? params.get("cancelReason").toString() : "";

        if (cancelReason.isEmpty()) {
            return AjaxResult.error("取消原因不能为空");
        }

        try {
            // 获取订单信息
            InventoryOutOrder order = inventoryOutOrderService.selectInventoryOutOrderById(id);
            if (order == null) {
                return error("订单不存在");
            }

            // 验证当前用户是否有权限操作该门店
            Long userId = SecurityUtils.getUserId();

            // 检查用户是否有该门店的权限
            boolean hasAuth = StorePermissionUtils.checkUserStorePermission(order.getStoreId(), userId, retailStoreMapper);
            if (!hasAuth) {
                return error("您没有权限操作此门店的订单");
            }

            int rows = inventoryOutOrderService.cancelReserveOrder(id, getUsername(), cancelReason);
            return toAjax(rows);
        } catch (Exception e) {
            return AjaxResult.error(e.getMessage());
        }
    }

    /**
     * 验证会员ID是否有效
     *
     * @param memberId 会员ID
     * @return 错误信息，如果没有错误则返回null
     */
    private String validateMemberId(Long memberId) {
        if (memberId == null) {
            return "会员ID不能为空";
        }
        return null;
    }

    /**
     * 验证用户是否有权限操作指定门店
     *
     * @param storeId 门店ID
     * @return 错误信息，如果没有错误则返回null
     */
    private String validateStorePermission(Long storeId) {
        if (storeId == null) {
            return "门店ID不能为空";
        }

        // 验证当前用户是否有权限操作该门店
        Long userId = SecurityUtils.getUserId();

        // 检查用户是否有该门店的权限
        boolean hasAuth = StorePermissionUtils.checkUserStorePermission(storeId, userId, retailStoreMapper);
        if (!hasAuth) {
            return "您没有权限操作此门店";
        }

        return null;
    }

    /**
     * 验证预订支付类型和定金金额
     *
     * @param depositType 预订支付类型
     * @param depositAmount 定金金额
     * @return 错误信息，如果没有错误则返回null
     */
    private String validateDepositInfo(Integer depositType, BigDecimal depositAmount) {
        if (depositType == null) {
            return "预订支付类型不能为空";
        }

        // 验证定金金额
        if (depositType == 0 && (depositAmount == null || depositAmount.compareTo(BigDecimal.ZERO) <= 0)) {
            return "定金金额必须大于0";
        }

        return null;
    }

    /**
     * 商品预订创建预订单
     */
    @PreAuthorize("@ss.hasPermi('retail:storestock:reserve')")
    @Log(title = "商品预订", businessType = BusinessType.INSERT)
    @PostMapping("/reserve")
    public AjaxResult reserve(@RequestBody ReserveRequest reserveRequest)
    {
        // 验证必要参数
        if (reserveRequest.getProductId() == null) {
            return error("商品ID不能为空");
        }

        if (reserveRequest.getSalePrice() == null || reserveRequest.getSalePrice().compareTo(BigDecimal.ZERO) <= 0) {
            return error("售价必须大于0");
        }

        // 验证会员ID是否提供
        String memberError = validateMemberId(reserveRequest.getMemberId());
        if (memberError != null) {
            return error(memberError);
        }

        // 验证门店权限
        String storeError = validateStorePermission(reserveRequest.getStoreId());
        if (storeError != null) {
            return error(storeError);
        }

        // 验证预订支付类型和定金金额
        String depositError = validateDepositInfo(reserveRequest.getDepositType(), reserveRequest.getDepositAmount());
        if (depositError != null) {
            return error(depositError);
        }

        try {
            // 创建预订单
            InventoryOutOrder outOrder = new InventoryOutOrder();
            outOrder.setOrderType(1); // 1表示预订单
            outOrder.setBusinessType(1); // 1表示预订业务
            outOrder.setProcessStatus(0); // 0表示待审核
            outOrder.setPayType(reserveRequest.getPayType());
            outOrder.setDepositType(reserveRequest.getDepositType());
            outOrder.setDepositAmount(reserveRequest.getDepositType() == 0 ? reserveRequest.getDepositAmount() : reserveRequest.getSalePrice());
            outOrder.setStoreId(reserveRequest.getStoreId());
            outOrder.setCreateBy(SecurityUtils.getUsername());
            outOrder.setRemark(reserveRequest.getRemark());
            outOrder.setMemberId(reserveRequest.getMemberId());
            // 设置实际售出时间，如果前端传入了则使用前端的时间，否则使用当前时间
            if (reserveRequest.getActualSaleTime() != null && !reserveRequest.getActualSaleTime().isEmpty()) {
                try {
                    SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                    Date actualSaleTime = sdf.parse(reserveRequest.getActualSaleTime());
                    outOrder.setActualSaleTime(actualSaleTime);
                } catch (ParseException e) {
                    log.warn("解析实际售出时间失败，使用当前时间: " + reserveRequest.getActualSaleTime(), e);
                    outOrder.setActualSaleTime(new Date());
                }
            } else {
                outOrder.setActualSaleTime(new Date());
            }

            // 创建出库明细
            List<InventoryOutDetail> detailList = new ArrayList<>();
            InventoryOutDetail detail = new InventoryOutDetail();
            detail.setProductId(reserveRequest.getProductId());
            detail.setCounterId(reserveRequest.getCounterId());
            detail.setChannelId(reserveRequest.getChannelId());
            detail.setQuantity(reserveRequest.getQuantity() != null ? reserveRequest.getQuantity() : 1);
            detail.setSalePrice(reserveRequest.getSalePrice());

            // 设置批次号，如果前端没有传入，则生成一个临时批次号
            if (reserveRequest.getBatchNumber() != null && !reserveRequest.getBatchNumber().isEmpty()) {
                detail.setBatchNumber(reserveRequest.getBatchNumber());
            } else {
                detail.setBatchNumber("R" + System.currentTimeMillis()); // 生成一个临时批次号
            }

            detailList.add(detail);

            // 计算总金额
            BigDecimal totalAmount = reserveRequest.getSalePrice().multiply(new BigDecimal(detail.getQuantity()));
            outOrder.setTotalAmount(totalAmount);

            // 保存预订单及明细
            int result = inventoryOutOrderService.saveReserveOrderWithDetails(outOrder, detailList);
            if (result > 0) {
                return success("商品预订成功");
            }
            return error("商品预订失败，请重试");
        } catch (Exception e) {
            log.error("商品预订异常", e);
            return error("商品预订失败：" + e.getMessage());
        }
    }

    /**
     * 批量商品预订创建预订单
     */
    @PreAuthorize("@ss.hasPermi('retail:storestock:reserve')")
    @Log(title = "批量商品预订", businessType = BusinessType.INSERT)
    @PostMapping("/batch-reserve")
    public AjaxResult batchReserve(@RequestBody BatchReserveRequest batchReserveRequest)
    {
        // 验证必要参数
        if (batchReserveRequest.getItems() == null || batchReserveRequest.getItems().isEmpty()) {
            return error("商品列表不能为空");
        }

        // 验证会员ID是否提供
        String memberError = validateMemberId(batchReserveRequest.getMemberId());
        if (memberError != null) {
            return error(memberError);
        }

        // 验证门店权限
        String storeError = validateStorePermission(batchReserveRequest.getStoreId());
        if (storeError != null) {
            return error(storeError);
        }

        // 验证预订支付类型和定金金额
        String depositError = validateDepositInfo(batchReserveRequest.getDepositType(), batchReserveRequest.getDepositAmount());
        if (depositError != null) {
            return error(depositError);
        }

        try {
            // 创建预订单
            InventoryOutOrder outOrder = new InventoryOutOrder();
            outOrder.setOrderType(1); // 1表示预订单
            outOrder.setBusinessType(1); // 1表示预订业务
            outOrder.setProcessStatus(0); // 0表示待审核
            outOrder.setPayType(batchReserveRequest.getPayType());
            outOrder.setDepositType(batchReserveRequest.getDepositType());
            outOrder.setDepositAmount(batchReserveRequest.getDepositType() == 0 ? batchReserveRequest.getDepositAmount() : batchReserveRequest.getTotalSalePrice());
            outOrder.setStoreId(batchReserveRequest.getStoreId());
            outOrder.setCreateBy(SecurityUtils.getUsername());
            outOrder.setRemark(batchReserveRequest.getRemark());
            outOrder.setMemberId(batchReserveRequest.getMemberId());
            // 设置实际售出时间，如果前端传入了则使用前端的时间，否则使用当前时间
            if (batchReserveRequest.getActualSaleTime() != null && !batchReserveRequest.getActualSaleTime().isEmpty()) {
                try {
                    SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                    Date actualSaleTime = sdf.parse(batchReserveRequest.getActualSaleTime());
                    outOrder.setActualSaleTime(actualSaleTime);
                } catch (ParseException e) {
                    log.warn("解析实际售出时间失败，使用当前时间: " + batchReserveRequest.getActualSaleTime(), e);
                    outOrder.setActualSaleTime(new Date());
                }
            } else {
                outOrder.setActualSaleTime(new Date());
            }

            // 创建出库明细列表
            List<InventoryOutDetail> detailList = new ArrayList<>();

            for (ReserveItem item : batchReserveRequest.getItems()) {
                // 创建出库明细
                InventoryOutDetail detail = new InventoryOutDetail();
                detail.setProductId(item.getProductId());
                detail.setCounterId(item.getCounterId());
                detail.setChannelId(item.getChannelId());
                detail.setQuantity(item.getQuantity() != null ? item.getQuantity() : 1);
                detail.setSalePrice(item.getSalePrice());
                detail.setProductName(item.getProductName());
                detail.setProductNumber(item.getProductNumber());

                // 设置批次号，如果前端没有传入，则生成一个临时批次号
                if (item.getBatchNumber() != null && !item.getBatchNumber().isEmpty()) {
                    detail.setBatchNumber(item.getBatchNumber());
                } else {
                    detail.setBatchNumber("R" + System.currentTimeMillis() + "-" + detailList.size()); // 生成一个临时批次号
                }

                detailList.add(detail);
            }

            // 使用前端传入的总实际售价，并验证价格一致性
            if (batchReserveRequest.getTotalSalePrice() != null && batchReserveRequest.getTotalSalePrice().compareTo(BigDecimal.ZERO) > 0) {
                outOrder.setTotalAmount(batchReserveRequest.getTotalSalePrice());

                // 验证明细价格总和是否与订单总金额一致
                BigDecimal detailTotalAmount = BigDecimal.ZERO;
                for (InventoryOutDetail detail : detailList) {
                    BigDecimal itemAmount = detail.getSalePrice().multiply(new BigDecimal(detail.getQuantity()));
                    detailTotalAmount = detailTotalAmount.add(itemAmount);
                }

                // 允许1分钱的误差（由于精度问题）
                BigDecimal difference = batchReserveRequest.getTotalSalePrice().subtract(detailTotalAmount).abs();
                if (difference.compareTo(new BigDecimal("0.01")) > 0) {
                    log.warn("批量预订价格不一致：订单总金额={}, 明细总金额={}, 差额={}",
                            batchReserveRequest.getTotalSalePrice(), detailTotalAmount, difference);
                    // 不返回错误，而是记录警告并继续处理
                }
            } else {
                // 如果没有传入总实际售价，则计算总和
                BigDecimal totalAmount = BigDecimal.ZERO;
                for (InventoryOutDetail detail : detailList) {
                    BigDecimal itemAmount = detail.getSalePrice().multiply(new BigDecimal(detail.getQuantity()));
                    totalAmount = totalAmount.add(itemAmount);
                }
                outOrder.setTotalAmount(totalAmount);
            }

            // 保存预订单及明细
            int result = inventoryOutOrderService.saveReserveOrderWithDetails(outOrder, detailList);
            if (result > 0) {
                return success("批量预订成功");
            }
            return error("批量预订失败，请重试");
        } catch (Exception e) {
            log.error("批量预订异常", e);
            return error("批量预订失败：" + e.getMessage());
        }
    }

    /**
     * 预订单交货
     */
    @PreAuthorize("@ss.hasPermi('retail:outorder:deliver')")
    @Log(title = "预订单交货", businessType = BusinessType.UPDATE)
    @PostMapping("/deliver-order")
    public AjaxResult deliver(@RequestBody DeliverRequest deliverRequest)
    {
        // 验证必要参数
        if (deliverRequest.getOrderId() == null) {
            return error("订单ID不能为空");
        }

        // 获取订单信息
        InventoryOutOrder order = inventoryOutOrderService.selectInventoryOutOrderById(deliverRequest.getOrderId());
        if (order == null) {
            return error("订单不存在");
        }

        // 验证订单类型
        if (order.getOrderType() == null || order.getOrderType() != 1) {
            return error("非预订单不能进行交货操作");
        }

        // 验证订单状态
        if (order.getProcessStatus() == null || order.getProcessStatus() != 1) { // 1=已审核
            return error("只有已审核状态的预订单才能进行交货操作");
        }

        // 验证当前用户是否有权限操作该门店
        Long userId = SecurityUtils.getUserId();

        // 检查用户是否有该门店的权限
        boolean hasAuth = StorePermissionUtils.checkUserStorePermission(order.getStoreId(), userId, retailStoreMapper);
        if (!hasAuth) {
            return error("您没有权限操作此门店的订单");
        }

        try {
            // 如果是定金支付，验证尾款信息
            if (order.getDepositType() != null && order.getDepositType() == 0) {
                if (deliverRequest.getBalancePayType() == null) {
                    return error("尾款支付方式不能为空");
                }
                if (deliverRequest.getBalanceAmount() == null || deliverRequest.getBalanceAmount().compareTo(BigDecimal.ZERO) < 0) {
                    return error("尾款金额不能小于0");
                }

                // 更新订单的尾款信息
                order.setBalancePayType(deliverRequest.getBalancePayType());
                order.setBalanceAmount(deliverRequest.getBalanceAmount());
            }

            // 获取交货备注，但不更新订单的remark字段
            // 将备注信息作为单独的参数传递给服务层
            String deliverRemark = deliverRequest.getRemark();

            // 执行交货操作
            int result = inventoryOutOrderService.deliverReserveOrder(order, deliverRemark);
            if (result > 0) {
                return success("预订单交货成功");
            }
            return error("预订单交货失败，请重试");
        } catch (Exception e) {
            log.error("预订单交货异常", e);
            return error("预订单交货失败：" + e.getMessage());
        }
    }

    /**
     * 预订请求对象
     */
    public static class ReserveRequest {
        private Long productId;
        private BigDecimal salePrice;
        private Integer payType;
        private Integer depositType; // 预订支付类型（0定金 1全款）
        private BigDecimal depositAmount; // 定金金额
        private String remark;
        private Integer quantity = 1;
        private Long memberId;
        private Long storeId; // 门店ID
        private Long counterId; // 柜台ID
        private Long channelId; // 渠道ID
        private String batchNumber; // 批次号
        private String actualSaleTime; // 实际售出时间字段（字符串格式）

        public Long getProductId() {
            return productId;
        }
        public void setProductId(Long productId) {
            this.productId = productId;
        }
        public BigDecimal getSalePrice() {
            return salePrice;
        }
        public void setSalePrice(BigDecimal salePrice) {
            this.salePrice = salePrice;
        }
        public Integer getPayType() {
            return payType;
        }
        public void setPayType(Integer payType) {
            this.payType = payType;
        }
        public Integer getDepositType() {
            return depositType;
        }
        public void setDepositType(Integer depositType) {
            this.depositType = depositType;
        }
        public BigDecimal getDepositAmount() {
            return depositAmount;
        }
        public void setDepositAmount(BigDecimal depositAmount) {
            this.depositAmount = depositAmount;
        }
        public String getRemark() {
            return remark;
        }
        public void setRemark(String remark) {
            this.remark = remark;
        }
        public Integer getQuantity() {
            return quantity;
        }
        public void setQuantity(Integer quantity) {
            this.quantity = quantity;
        }
        public Long getMemberId() {
            return memberId;
        }
        public void setMemberId(Long memberId) {
            this.memberId = memberId;
        }
        public Long getStoreId() {
            return storeId;
        }
        public void setStoreId(Long storeId) {
            this.storeId = storeId;
        }
        public Long getCounterId() {
            return counterId;
        }
        public void setCounterId(Long counterId) {
            this.counterId = counterId;
        }
        public Long getChannelId() {
            return channelId;
        }
        public void setChannelId(Long channelId) {
            this.channelId = channelId;
        }
        public String getBatchNumber() {
            return batchNumber;
        }
        public void setBatchNumber(String batchNumber) {
            this.batchNumber = batchNumber;
        }
        public String getActualSaleTime() {
            return actualSaleTime;
        }
        public void setActualSaleTime(String actualSaleTime) {
            this.actualSaleTime = actualSaleTime;
        }
    }

    /**
     * 批量预订请求对象
     */
    public static class BatchReserveRequest {
        private List<ReserveItem> items;
        private Integer payType;
        private Integer depositType; // 预订支付类型（0定金 1全款）
        private BigDecimal depositAmount; // 定金金额
        private String remark;
        private Long memberId;
        private Long storeId; // 门店ID
        private BigDecimal totalSalePrice; // 总实际售价
        private String actualSaleTime; // 实际售出时间字段（字符串格式）

        public List<ReserveItem> getItems() {
            return items;
        }
        public void setItems(List<ReserveItem> items) {
            this.items = items;
        }
        public Integer getPayType() {
            return payType;
        }
        public void setPayType(Integer payType) {
            this.payType = payType;
        }
        public Integer getDepositType() {
            return depositType;
        }
        public void setDepositType(Integer depositType) {
            this.depositType = depositType;
        }
        public BigDecimal getDepositAmount() {
            return depositAmount;
        }
        public void setDepositAmount(BigDecimal depositAmount) {
            this.depositAmount = depositAmount;
        }
        public String getRemark() {
            return remark;
        }
        public void setRemark(String remark) {
            this.remark = remark;
        }
        public Long getMemberId() {
            return memberId;
        }
        public void setMemberId(Long memberId) {
            this.memberId = memberId;
        }
        public Long getStoreId() {
            return storeId;
        }
        public void setStoreId(Long storeId) {
            this.storeId = storeId;
        }
        public BigDecimal getTotalSalePrice() {
            return totalSalePrice;
        }
        public void setTotalSalePrice(BigDecimal totalSalePrice) {
            this.totalSalePrice = totalSalePrice;
        }
        public String getActualSaleTime() {
            return actualSaleTime;
        }
        public void setActualSaleTime(String actualSaleTime) {
            this.actualSaleTime = actualSaleTime;
        }
    }

    /**
     * 预订商品项
     */
    public static class ReserveItem {
        private Long productId;
        private BigDecimal salePrice;
        private Integer quantity = 1;
        private Long counterId;
        private Long channelId;
        private String productName;
        private String productNumber;
        private String batchNumber; // 批次号

        public Long getProductId() {
            return productId;
        }
        public void setProductId(Long productId) {
            this.productId = productId;
        }
        public BigDecimal getSalePrice() {
            return salePrice;
        }
        public void setSalePrice(BigDecimal salePrice) {
            this.salePrice = salePrice;
        }
        public Integer getQuantity() {
            return quantity;
        }
        public void setQuantity(Integer quantity) {
            this.quantity = quantity;
        }
        public Long getCounterId() {
            return counterId;
        }
        public void setCounterId(Long counterId) {
            this.counterId = counterId;
        }
        public Long getChannelId() {
            return channelId;
        }
        public void setChannelId(Long channelId) {
            this.channelId = channelId;
        }
        public String getProductName() {
            return productName;
        }
        public void setProductName(String productName) {
            this.productName = productName;
        }
        public String getProductNumber() {
            return productNumber;
        }
        public void setProductNumber(String productNumber) {
            this.productNumber = productNumber;
        }
        public String getBatchNumber() {
            return batchNumber;
        }
        public void setBatchNumber(String batchNumber) {
            this.batchNumber = batchNumber;
        }
    }



    /**
     * 交货请求对象
     */
    public static class DeliverRequest {
        private Long orderId; // 预订单ID
        private Integer balancePayType; // 尾款支付方式
        private BigDecimal balanceAmount; // 尾款金额
        private String remark; // 备注

        public Long getOrderId() {
            return orderId;
        }
        public void setOrderId(Long orderId) {
            this.orderId = orderId;
        }
        public Integer getBalancePayType() {
            return balancePayType;
        }
        public void setBalancePayType(Integer balancePayType) {
            this.balancePayType = balancePayType;
        }
        public BigDecimal getBalanceAmount() {
            return balanceAmount;
        }
        public void setBalanceAmount(BigDecimal balanceAmount) {
            this.balanceAmount = balanceAmount;
        }
        public String getRemark() {
            return remark;
        }
        public void setRemark(String remark) {
            this.remark = remark;
        }
    }
}
