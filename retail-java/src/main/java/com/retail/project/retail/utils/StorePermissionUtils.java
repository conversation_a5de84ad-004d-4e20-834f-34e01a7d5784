package com.retail.project.retail.utils;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import com.retail.project.retail.mapper.RetailStoreMapper;
import com.retail.common.utils.SecurityUtils;

/**
 * 门店权限工具类
 *
 * <AUTHOR>
 */
public class StorePermissionUtils {
    private static final Logger log = LoggerFactory.getLogger(StorePermissionUtils.class);

    /**
     * 检查用户是否有权限访问指定门店
     *
     * @param storeId 门店ID
     * @param userId 用户ID
     * @param retailStoreMapper 门店Mapper
     * @return 是否有权限
     */
    public static boolean checkUserStorePermission(Long storeId, Long userId, RetailStoreMapper retailStoreMapper) {
        try {
            // 首先检查用户是否拥有管理类角色
            boolean hasManagementRole = SecurityUtils.hasManagementRole();
            if (hasManagementRole) {
                // 如果用户拥有管理类角色，直接返回true
                return true;
            }

            // 如果不是管理类角色，查询门店用户列表，判断用户是否在其中
            String userIds = retailStoreMapper.selectStoreUserIds(storeId);
            if (userIds == null || userIds.isEmpty()) {
                return false;
            }
            return userIds.contains(userId.toString());
        } catch (Exception e) {
            log.error("检查用户门店权限异常", e);
            return false;
        }
    }
}
