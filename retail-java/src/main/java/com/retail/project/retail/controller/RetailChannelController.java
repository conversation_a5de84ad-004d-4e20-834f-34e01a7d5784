package com.retail.project.retail.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.retail.framework.aspectj.lang.annotation.Log;
import com.retail.framework.aspectj.lang.enums.BusinessType;
import com.retail.project.retail.domain.RetailChannel;
import com.retail.project.retail.service.IRetailChannelService;
import com.retail.framework.web.controller.BaseController;
import com.retail.framework.web.domain.AjaxResult;
import com.retail.common.utils.poi.ExcelUtil;
import com.retail.framework.web.page.TableDataInfo;
import com.retail.project.system.domain.SysUser;
import com.retail.project.system.mapper.SysUserMapper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * 渠道管理Controller
 *
 * <AUTHOR>
 * @date 2025-04-17
 */
@RestController
@RequestMapping("/retail/channel")
public class RetailChannelController extends BaseController
{
    private static final Logger log = LoggerFactory.getLogger(RetailChannelController.class);

    @Autowired
    private IRetailChannelService retailChannelService;

    @Autowired
    private SysUserMapper userMapper;

    /**
     * 查询渠道管理列表
     */
    @PreAuthorize("@ss.hasPermi('retail:channel:list')")
    @GetMapping("/list")
    public TableDataInfo list(RetailChannel retailChannel)
    {
        startPage();
        List<RetailChannel> list = retailChannelService.selectRetailChannelList(retailChannel);
        return getDataTable(list);
    }

    /**
     * 导出渠道管理列表
     */
    @PreAuthorize("@ss.hasPermi('retail:channel:export')")
    @Log(title = "渠道管理", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, RetailChannel retailChannel)
    {
        List<RetailChannel> list = retailChannelService.selectRetailChannelList(retailChannel);
        ExcelUtil<RetailChannel> util = new ExcelUtil<RetailChannel>(RetailChannel.class);
        util.exportExcel(response, list, "渠道管理数据");
    }

    /**
     * 获取渠道管理详细信息
     */
    @PreAuthorize("@ss.hasPermi('retail:channel:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(retailChannelService.selectRetailChannelById(id));
    }

    /**
     * 新增渠道管理
     */
    @PreAuthorize("@ss.hasPermi('retail:channel:add')")
    @Log(title = "渠道管理", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody RetailChannel retailChannel)
    {
        retailChannel.setCreateBy(getUsername());
        return toAjax(retailChannelService.insertRetailChannel(retailChannel));
    }

    /**
     * 修改渠道管理
     */
    @PreAuthorize("@ss.hasPermi('retail:channel:edit')")
    @Log(title = "渠道管理", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody RetailChannel retailChannel)
    {
        retailChannel.setUpdateBy(getUsername());
        return toAjax(retailChannelService.updateRetailChannel(retailChannel));
    }

    /**
     * 删除渠道管理
     */
    @PreAuthorize("@ss.hasPermi('retail:channel:remove')")
    @Log(title = "渠道管理", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(retailChannelService.deleteRetailChannelByIds(ids));
    }

    /**
     * 查询所有渠道列表（不分页）
     */
    @PreAuthorize("@ss.hasPermi('retail:channel:list')")
    @GetMapping("/all")
    public AjaxResult listAll(RetailChannel retailChannel)
    {
        List<RetailChannel> list = retailChannelService.selectRetailChannelList(retailChannel);
        return success(list);
    }

    /**
     * 获取渠道管理员用户列表
     */
    @PreAuthorize("@ss.hasPermi('retail:channel:list')")
    @GetMapping("/channelUsers")
    public AjaxResult listChannelUsers()
    {
        try {
            List<SysUser> users = userMapper.selectUserListByRoleKey("channel");
            log.info("查询渠道管理员用户列表成功，找到{}个用户", users.size());
            return success(users);
        } catch (Exception e) {
            log.error("查询渠道管理员用户列表失败", e);
            return error("查询用户列表失败：" + e.getMessage());
        }
    }
}
