package com.retail.project.retail.mapper;

import java.util.List;
import com.retail.project.retail.domain.InventoryOutDetail;

/**
 * 出库明细Mapper接口
 *
 * <AUTHOR>
 * @date 2025-04-17
 */
public interface InventoryOutDetailMapper
{
    /**
     * 查询出库明细
     *
     * @param id 出库明细主键
     * @return 出库明细
     */
    public InventoryOutDetail selectInventoryOutDetailById(Long id);

    /**
     * 查询出库明细列表
     *
     * @param inventoryOutDetail 出库明细
     * @return 出库明细集合
     */
    public List<InventoryOutDetail> selectInventoryOutDetailList(InventoryOutDetail inventoryOutDetail);

    /**
     * 根据出库单ID查询出库明细列表
     *
     * @param orderId 出库单ID
     * @return 出库明细集合
     */
    public List<InventoryOutDetail> selectInventoryOutDetailByOrderId(Long orderId);

    /**
     * 新增出库明细
     *
     * @param inventoryOutDetail 出库明细
     * @return 结果
     */
    public int insertInventoryOutDetail(InventoryOutDetail inventoryOutDetail);

    /**
     * 批量新增出库明细
     *
     * @param inventoryOutDetailList 出库明细列表
     * @return 结果
     */
    public int batchInsertInventoryOutDetail(List<InventoryOutDetail> inventoryOutDetailList);

    /**
     * 修改出库明细
     *
     * @param inventoryOutDetail 出库明细
     * @return 结果
     */
    public int updateInventoryOutDetail(InventoryOutDetail inventoryOutDetail);

    /**
     * 删除出库明细
     *
     * @param id 出库明细主键
     * @return 结果
     */
    public int deleteInventoryOutDetailById(Long id);

    /**
     * 批量删除出库明细
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteInventoryOutDetailByIds(Long[] ids);

    /**
     * 根据出库单ID删除出库明细
     *
     * @param orderId 出库单ID
     * @return 结果
     */
    public int deleteInventoryOutDetailByOrderId(Long orderId);

    /**
     * 查询按时间维度分组的销售数量统计
     *
     * @param params 查询参数，包含channelId和timeUnit
     * @return 按时间维度分组的销售数量统计
     */
    public List<java.util.Map<String, Object>> selectTimeSalesQuantity(java.util.Map<String, Object> params);

    /**
     * 查询按月份分组的销售数量统计（保留向后兼容）
     *
     * @param channelId 渠道ID
     * @return 按月份分组的销售数量统计
     */
    public List<java.util.Map<String, Object>> selectMonthlySalesQuantity(Long channelId);

    /**
     * 查询按商品分组的销售数量统计
     *
     * @param channelId 渠道ID
     * @return 按商品分组的销售数量统计
     */
    public List<java.util.Map<String, Object>> selectProductSalesQuantity(Long channelId);

    /**
     * 查询渠道产品销售明细
     *
     * @param params 查询参数，包含channelId、beginTime、endTime
     * @return 渠道产品销售明细
     */
    public List<java.util.Map<String, Object>> selectChannelProductSalesDetails(java.util.Map<String, Object> params);

    /**
     * 查询按商品分组的销售数量统计（支持筛选条件）
     *
     * @param params 查询参数，包含channelId、storeId、beginTime、endTime
     * @return 按商品分组的销售数量统计
     */
    public List<java.util.Map<String, Object>> selectProductSalesQuantityWithFilter(java.util.Map<String, Object> params);

    /**
     * 查询按时间维度分组的销售数量统计（支持筛选条件）
     *
     * @param params 查询参数，包含channelId、storeId、beginTime、endTime、timeUnit
     * @return 按时间维度分组的销售数量统计
     */
    public List<java.util.Map<String, Object>> selectTimeSalesQuantityWithFilter(java.util.Map<String, Object> params);

    /**
     * 销售数据统计查询（优化版本）
     *
     * @param params 查询参数
     * @return 销售统计数据
     */
    public java.util.Map<String, Object> selectSaleStatistics(java.util.Map<String, Object> params);

    /**
     * 销售趋势数据查询（优化版本）
     *
     * @param params 查询参数
     * @return 销售趋势数据
     */
    public List<java.util.Map<String, Object>> selectSaleTrendData(java.util.Map<String, Object> params);

    /**
     * 渠道销售分布查询（优化版本）
     *
     * @param params 查询参数
     * @return 渠道销售分布数据
     */
    public List<java.util.Map<String, Object>> selectChannelSalesDistribution(java.util.Map<String, Object> params);

    /**
     * 门店销售排行查询（优化版本）
     *
     * @param params 查询参数
     * @return 门店销售排行数据
     */
    public List<java.util.Map<String, Object>> selectStoreSalesRanking(java.util.Map<String, Object> params);

    /**
     * 商品销售排行查询（优化版本）
     *
     * @param params 查询参数
     * @return 商品销售排行数据
     */
    public List<java.util.Map<String, Object>> selectProductSalesRanking(java.util.Map<String, Object> params);

    /**
     * 客户性别分布查询（优化版本）
     *
     * @param params 查询参数
     * @return 客户性别分布数据
     */
    public List<java.util.Map<String, Object>> selectCustomerGenderDistribution(java.util.Map<String, Object> params);

    /**
     * 利润分析数据查询（优化版本）
     *
     * @param params 查询参数
     * @return 利润分析数据
     */
    public List<java.util.Map<String, Object>> selectProfitAnalysisData(java.util.Map<String, Object> params);
}