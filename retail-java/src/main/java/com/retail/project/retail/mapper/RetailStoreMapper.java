package com.retail.project.retail.mapper;

import java.util.List;
import com.retail.project.retail.domain.RetailStore;

/**
 * 门店管理Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-04-18
 */
public interface RetailStoreMapper 
{
    /**
     * 查询门店管理
     * 
     * @param id 门店管理主键
     * @return 门店管理
     */
    public RetailStore selectRetailStoreById(Long id);

    /**
     * 查询门店管理列表
     * 
     * @param retailStore 门店管理
     * @return 门店管理集合
     */
    public List<RetailStore> selectRetailStoreList(RetailStore retailStore);

    /**
     * 新增门店管理
     * 
     * @param retailStore 门店管理
     * @return 结果
     */
    public int insertRetailStore(RetailStore retailStore);

    /**
     * 修改门店管理
     * 
     * @param retailStore 门店管理
     * @return 结果
     */
    public int updateRetailStore(RetailStore retailStore);

    /**
     * 删除门店管理
     * 
     * @param id 门店管理主键
     * @return 结果
     */
    public int deleteRetailStoreById(Long id);

    /**
     * 批量删除门店管理
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteRetailStoreByIds(Long[] ids);

    /**
     * 查询门店关联的用户ID列表
     * 
     * @param storeId 门店ID
     * @return 用户ID字符串，多个ID用逗号分隔
     */
    public String selectStoreUserIds(Long storeId);
}
