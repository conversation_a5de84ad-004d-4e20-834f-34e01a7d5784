package com.retail.project.retail.service.impl;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.Map;
import java.util.stream.Collectors;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import com.retail.project.retail.mapper.InventoryOutOrderMapper;
import com.retail.project.retail.mapper.InventoryOutDetailMapper;
import com.retail.project.retail.mapper.InventoryStockMapper;
import com.retail.project.retail.mapper.InventoryOutAuditMapper;
import com.retail.project.retail.domain.InventoryOutOrder;
import com.retail.project.retail.domain.InventoryOutDetail;
import com.retail.project.retail.domain.InventoryStock;
import com.retail.project.retail.domain.InventoryOutAudit;
import com.retail.project.retail.domain.enums.BusinessType;
import com.retail.project.retail.domain.enums.ProcessStatus;
import com.retail.project.retail.service.IInventoryOutOrderService;
import com.retail.framework.aspectj.lang.annotation.DataScope;
import com.retail.project.member.service.IMemberService;
import com.retail.project.member.domain.Member;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * 出库单Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-04-17
 */
@Service
public class InventoryOutOrderServiceImpl implements IInventoryOutOrderService
{
    private static final Logger log = LoggerFactory.getLogger(InventoryOutOrderServiceImpl.class);

    @Autowired
    private InventoryOutOrderMapper inventoryOutOrderMapper;

    @Autowired
    private InventoryOutDetailMapper inventoryOutDetailMapper;

    @Autowired
    private InventoryStockMapper inventoryStockMapper;

    @Autowired
    private InventoryOutAuditMapper inventoryOutAuditMapper;

    @Autowired
    private IMemberService memberService;

    /**
     * 查询出库单
     *
     * @param id 出库单主键
     * @return 出库单
     */
    @Override
    public InventoryOutOrder selectInventoryOutOrderById(Long id)
    {
        InventoryOutOrder outOrder = inventoryOutOrderMapper.selectInventoryOutOrderById(id);
        if (outOrder != null) {
            // 查询出库单关联的明细列表
            List<InventoryOutDetail> detailList = inventoryOutDetailMapper.selectInventoryOutDetailByOrderId(id);
            outOrder.setDetailList(detailList);

            // 查询出库单关联的审核记录
            List<InventoryOutAudit> auditList = inventoryOutAuditMapper.selectInventoryOutAuditByOrderId(id);
            outOrder.setAuditList(auditList);
        }
        return outOrder;
    }

    /**
     * 查询出库单列表
     *
     * @param inventoryOutOrder 出库单
     * @return 出库单
     */
    @Override
    @DataScope(permission = "retail:outorder:list")
    public List<InventoryOutOrder> selectInventoryOutOrderList(InventoryOutOrder inventoryOutOrder)
    {
        return inventoryOutOrderMapper.selectInventoryOutOrderList(inventoryOutOrder);
    }

    /**
     * 新增出库单
     *
     * @param inventoryOutOrder 出库单
     * @return 结果
     */
    @Override
    public int insertInventoryOutOrder(InventoryOutOrder inventoryOutOrder)
    {

        // 处理BaseEntity的字段
        inventoryOutOrder.setCreateTime(new Date());

        if (inventoryOutOrder.getOrderNumber() == null || "".equals(inventoryOutOrder.getOrderNumber())) {
            // 生成订单号 OUT + 年月日时分秒 + 4位随机数
            String orderNumber = "OUT" + System.currentTimeMillis() + (int)((Math.random() * 9 + 1) * 1000);
            inventoryOutOrder.setOrderNumber(orderNumber);
        }

        return inventoryOutOrderMapper.insertInventoryOutOrder(inventoryOutOrder);
    }

    /**
     * 修改出库单
     *
     * @param inventoryOutOrder 出库单
     * @return 结果
     */
    @Override
    public int updateInventoryOutOrder(InventoryOutOrder inventoryOutOrder)
    {
        // 自动填充更新时间
        inventoryOutOrder.setUpdateTime(new Date());
        return inventoryOutOrderMapper.updateInventoryOutOrder(inventoryOutOrder);
    }

    /**
     * 批量删除出库单
     *
     * @param ids 需要删除的出库单主键
     * @return 结果
     */
    @Override
    @Transactional
    public int deleteInventoryOutOrderByIds(Long[] ids)
    {
        for (Long id : ids) {
            // 删除明细
            inventoryOutDetailMapper.deleteInventoryOutDetailByOrderId(id);
        }
        return inventoryOutOrderMapper.deleteInventoryOutOrderByIds(ids);
    }

    /**
     * 删除出库单信息
     *
     * @param id 出库单主键
     * @return 结果
     */
    @Override
    @Transactional
    public int deleteInventoryOutOrderById(Long id)
    {
        // 删除明细
        inventoryOutDetailMapper.deleteInventoryOutDetailByOrderId(id);
        return inventoryOutOrderMapper.deleteInventoryOutOrderById(id);
    }

    /**
     * 保存出库单及明细
     *
     * @param inventoryOutOrder 出库单
     * @param detailList 出库明细列表
     * @return 结果
     */
    @Override
    @Transactional
    public int saveInventoryOutOrderWithDetails(InventoryOutOrder inventoryOutOrder, List<InventoryOutDetail> detailList)
    {
        // 保存出库单
        int rows = insertInventoryOutOrder(inventoryOutOrder);
        if (rows > 0 && detailList != null && !detailList.isEmpty()) {
            // 处理出库明细
            for (InventoryOutDetail detail : detailList) {
                detail.setOrderId(inventoryOutOrder.getId());
                // 设置创建人
                detail.setCreateBy(inventoryOutOrder.getCreateBy());

                // 查找库存，按先进先出原则，只查询非期货库存
                List<InventoryStock> allStockList = inventoryStockMapper.selectInventoryStockByLocation(
                    detail.getProductId(),
                    inventoryOutOrder.getStoreId(),
                    detail.getCounterId(),
                    detail.getChannelId()
                );

                // 过滤掉期货库存
                List<InventoryStock> stockList = allStockList.stream()
                    .filter(stock -> stock.getIsFutures() == null || !stock.getIsFutures())
                    .collect(Collectors.toList());

                if (stockList.isEmpty()) {
                    throw new RuntimeException("商品【" + detail.getProductName() + "】在当前门店没有可用库存或仅有期货库存，不能售出");
                }

                // 检查库存是否充足
                int totalAvailableQuantity = stockList.stream().mapToInt(InventoryStock::getQuantity).sum();
                if (totalAvailableQuantity < detail.getQuantity()) {
                    throw new RuntimeException("商品【" + detail.getProductName() + "】在当前门店非期货库存不足，需要" +
                        detail.getQuantity() + "，实际可用" + totalAvailableQuantity);
                }

                // 扣减库存
                int remainQuantity = detail.getQuantity();
                List<String> batchNumbers = new ArrayList<>();

                for (InventoryStock stock : stockList) {
                    if (remainQuantity <= 0) {
                        break;
                    }

                    int deductQuantity = Math.min(remainQuantity, stock.getQuantity());
                    remainQuantity -= deductQuantity;

                    // 更新库存
                    InventoryStock updateStock = new InventoryStock();
                    updateStock.setId(stock.getId());
                    updateStock.setQuantity(stock.getQuantity() - deductQuantity);
                    // 设置更新人
                    updateStock.setUpdateBy(inventoryOutOrder.getCreateBy());
                    inventoryStockMapper.updateInventoryStock(updateStock);

                    // 记录批次号
                    batchNumbers.add(stock.getBatchNumber());

                    // 如果是第一条库存记录，获取商品图片
                    if (detail.getProductImage() == null && stock.getProductImage() != null) {
                        detail.setProductImage(stock.getProductImage());
                    }
                }

                if (remainQuantity > 0) {
                    throw new RuntimeException("商品【" + detail.getProductName() + "】库存不足");
                }

                // 设置批次号
                detail.setBatchNumber(String.join(",", batchNumbers));

                // 保存出库明细
                inventoryOutDetailMapper.insertInventoryOutDetail(detail);
            }

            // 确保业务场景不为空，如果为空则根据订单类型设置默认值
            Integer businessType = inventoryOutOrder.getBusinessType();
            if (businessType == null) {
                // 根据订单类型设置默认业务场景
                if (inventoryOutOrder.getOrderType() != null && inventoryOutOrder.getOrderType() == 1) {
                    businessType = BusinessType.RESERVE; // 预订
                } else {
                    businessType = BusinessType.SALE; // 默认为售出
                }
            }

            // 库存删除和库存迁移是管理员直接操作，不需要审核流程
            if (businessType == BusinessType.DELETE || businessType == BusinessType.MIGRATE) {
                // 直接设置为已审核状态
                InventoryOutOrder updateOrder = new InventoryOutOrder();
                updateOrder.setId(inventoryOutOrder.getId());
                updateOrder.setBusinessType(businessType);
                updateOrder.setProcessStatus(ProcessStatus.APPROVED); // 直接设置为已审核
                updateOrder.setUpdateBy(inventoryOutOrder.getCreateBy());
                updateInventoryOutOrder(updateOrder);

                // 创建一条直接完成的审核记录
                InventoryOutAudit audit = new InventoryOutAudit();
                audit.setOrderId(inventoryOutOrder.getId());
                audit.setBusinessType(businessType);
                audit.setProcessStatus(ProcessStatus.APPROVED); // 直接已审核
                audit.setReason("管理员直接操作，无需审核");
                audit.setOperator(inventoryOutOrder.getCreateBy());
                inventoryOutAuditMapper.insertInventoryOutAudit(audit);
            } else {
                // 其他业务类型需要审核流程
                // 创建一条出库单提交的审核记录
                InventoryOutAudit audit = new InventoryOutAudit();
                audit.setOrderId(inventoryOutOrder.getId());
                audit.setBusinessType(businessType); // 业务场景
                audit.setProcessStatus(ProcessStatus.PENDING); // 待审核
                audit.setOperator(inventoryOutOrder.getCreateBy());
                inventoryOutAuditMapper.insertInventoryOutAudit(audit);
            }
        }

        return rows;
    }

    /**
     * 审核通过出库单
     *
     * @param id 出库单ID
     * @param approveBy 审核人
     * @param remark 审核备注
     * @return 结果
     */
    @Override
    @Transactional
    public int approveOutOrder(Long id, String approveBy, String remark) {
        // 查询出库单
        InventoryOutOrder order = selectInventoryOutOrderById(id);
        if (order == null) {
            throw new RuntimeException("出库单不存在");
        }

        // 检查状态是否为待审核
        if (order.getProcessStatus() != null && order.getProcessStatus() != ProcessStatus.PENDING) {
            throw new RuntimeException("出库单状态不正确，无法审核");
        }

        // 更新出库单状态
        InventoryOutOrder updateOrder = new InventoryOutOrder();
        updateOrder.setId(id);

        // 无论是否为预订单，审核通过后都设置为已审核状态
        updateOrder.setBusinessType(order.getBusinessType()); // 保持原业务场景不变
        updateOrder.setProcessStatus(ProcessStatus.APPROVED); // 已审核
        updateOrder.setUpdateBy(approveBy);

        // 创建审核记录
        InventoryOutAudit audit = new InventoryOutAudit();
        audit.setOrderId(id);
        audit.setBusinessType(order.getBusinessType()); // 业务场景
        audit.setProcessStatus(ProcessStatus.APPROVED); // 流程状态
        audit.setReason(remark);
        audit.setOperator(approveBy);
        inventoryOutAuditMapper.insertInventoryOutAudit(audit);

        // 更新会员累计消费金额
        // 只有在以下情况才更新会员累计消费金额：
        // 1. 非预订单，或者
        // 2. 预订单且业务场景为交货（DELIVER）且流程状态为已审核
        if ((order.getOrderType() == null || order.getOrderType() != 1) ||
            (order.getOrderType() == 1 && order.getBusinessType() != null && order.getBusinessType() == BusinessType.DELIVER)) {
            updateMemberAmount(order);
        }

        return updateInventoryOutOrder(updateOrder);
    }

    /**
     * 驳回出库单
     *
     * @param id 出库单ID
     * @param rejectBy 驳回人
     * @param rejectReason 驳回原因
     * @return 结果
     */
    @Override
    @Transactional
    public int rejectOutOrder(Long id, String rejectBy, String rejectReason) {
        // 查询出库单
        InventoryOutOrder order = selectInventoryOutOrderById(id);
        if (order == null) {
            throw new RuntimeException("出库单不存在");
        }

        // 检查状态是否为待审核
        if (order.getProcessStatus() != null && order.getProcessStatus() != ProcessStatus.PENDING) {
            throw new RuntimeException("出库单状态不正确，无法驳回");
        }

        // 获取出库明细
        List<InventoryOutDetail> detailList = order.getDetailList();
        if (detailList == null || detailList.isEmpty()) {
            throw new RuntimeException("出库单明细不存在");
        }

        // 恢复库存
        for (InventoryOutDetail detail : detailList) {
            // 批次号可能包含多个，以逗号分隔
            String[] batchNumbers = detail.getBatchNumber().split(",");

            // 查找相关的库存记录
            for (String batchNumber : batchNumbers) {
                // 查询该批次的库存记录，确保匹配正确的商品和门店
                InventoryStock stockQuery = new InventoryStock();
                stockQuery.setBatchNumber(batchNumber);
                stockQuery.setProductId(detail.getProductId());
                stockQuery.setStoreId(order.getStoreId());
                stockQuery.setCounterId(detail.getCounterId());
                stockQuery.setChannelId(detail.getChannelId());

                List<InventoryStock> stockList = inventoryStockMapper.selectInventoryStockList(stockQuery);

                if (!stockList.isEmpty()) {
                    InventoryStock stock = stockList.get(0);

                    // 恢复库存
                    InventoryStock updateStock = new InventoryStock();
                    updateStock.setId(stock.getId());
                    updateStock.setQuantity(stock.getQuantity() + detail.getQuantity());
                    updateStock.setUpdateBy(rejectBy);
                    inventoryStockMapper.updateInventoryStock(updateStock);
                } else {
                    // 如果找不到完全匹配的记录，记录日志并继续处理
                    log.warn("驳回恢复库存时未找到匹配记录: 批次号={}, 商品ID={}, 门店ID={}",
                        batchNumber, detail.getProductId(), order.getStoreId());
                }
            }
        }

        // 更新出库单状态为已驳回
        InventoryOutOrder updateOrder = new InventoryOutOrder();
        updateOrder.setId(id);
        updateOrder.setProcessStatus(ProcessStatus.REJECTED); // 已驳回
        updateOrder.setUpdateBy(rejectBy);

        // 创建驳回记录
        InventoryOutAudit audit = new InventoryOutAudit();
        audit.setOrderId(id);
        audit.setBusinessType(order.getBusinessType()); // 业务场景
        audit.setProcessStatus(ProcessStatus.REJECTED); // 已驳回
        audit.setReason(rejectReason);
        audit.setOperator(rejectBy);
        inventoryOutAuditMapper.insertInventoryOutAudit(audit);

        return updateInventoryOutOrder(updateOrder);
    }

    /**
     * 撤销出库单
     *
     * @param id 出库单ID
     * @param cancelBy 撤销人
     * @param cancelReason 撤销原因
     * @return 结果
     */
    @Override
    @Transactional
    public int cancelOutOrder(Long id, String cancelBy, String cancelReason) {
        // 查询出库单
        InventoryOutOrder order = selectInventoryOutOrderById(id);
        if (order == null) {
            throw new RuntimeException("出库单不存在");
        }

        // 检查状态是否为待审核
        if (order.getProcessStatus() != null && order.getProcessStatus() != ProcessStatus.PENDING) {
            throw new RuntimeException("出库单状态不正确，无法撤销");
        }

        // 检查创建人是否匹配
        if (!cancelBy.equals(order.getCreateBy())) {
            throw new RuntimeException("只有申请人才能撤销订单");
        }

        // 获取出库明细
        List<InventoryOutDetail> detailList = order.getDetailList();
        if (detailList == null || detailList.isEmpty()) {
            throw new RuntimeException("出库单明细不存在");
        }

        // 恢复库存（与驳回类似）
        for (InventoryOutDetail detail : detailList) {
            // 批次号可能包含多个，以逗号分隔
            String[] batchNumbers = detail.getBatchNumber().split(",");

            // 查找相关的库存记录
            for (String batchNumber : batchNumbers) {
                // 查询该批次的库存记录，确保匹配正确的商品和门店
                InventoryStock stockQuery = new InventoryStock();
                stockQuery.setBatchNumber(batchNumber);
                stockQuery.setProductId(detail.getProductId());
                stockQuery.setStoreId(order.getStoreId());
                stockQuery.setCounterId(detail.getCounterId());
                stockQuery.setChannelId(detail.getChannelId());

                List<InventoryStock> stockList = inventoryStockMapper.selectInventoryStockList(stockQuery);

                if (!stockList.isEmpty()) {
                    InventoryStock stock = stockList.get(0);

                    // 恢复库存
                    InventoryStock updateStock = new InventoryStock();
                    updateStock.setId(stock.getId());
                    updateStock.setQuantity(stock.getQuantity() + detail.getQuantity());
                    updateStock.setUpdateBy(cancelBy);
                    inventoryStockMapper.updateInventoryStock(updateStock);
                } else {
                    // 如果找不到完全匹配的记录，记录日志并继续处理
                    log.warn("撤销恢复库存时未找到匹配记录: 批次号={}, 商品ID={}, 门店ID={}",
                        batchNumber, detail.getProductId(), order.getStoreId());
                }
            }
        }

        // 更新出库单状态为已撤销
        InventoryOutOrder updateOrder = new InventoryOutOrder();
        updateOrder.setId(id);
        updateOrder.setProcessStatus(ProcessStatus.CANCELED); // 已撤销
        updateOrder.setUpdateBy(cancelBy);

        // 创建撤销记录
        InventoryOutAudit audit = new InventoryOutAudit();
        audit.setOrderId(id);
        audit.setBusinessType(order.getBusinessType()); // 业务场景
        audit.setProcessStatus(ProcessStatus.CANCELED); // 已撤销
        audit.setReason(cancelReason);
        audit.setOperator(cancelBy);
        inventoryOutAuditMapper.insertInventoryOutAudit(audit);

        return updateInventoryOutOrder(updateOrder);
    }

    /**
     * 根据出库单ID查询审核记录列表
     *
     * @param orderId 出库单ID
     * @return 出库单审核记录集合
     */
    @Override
    public List<InventoryOutAudit> selectInventoryOutAuditByOrderId(Long orderId) {
        return inventoryOutAuditMapper.selectInventoryOutAuditByOrderId(orderId);
    }

    /**
     * 申请退货
     *
     * @param id 出库单ID
     * @param applyBy 申请人
     * @param applyReason 退货原因
     * @return 结果
     */
    @Override
    @Transactional
    public int applyReturnOutOrder(Long id, String applyBy, String applyReason) {
        // 查询出库单
        InventoryOutOrder order = selectInventoryOutOrderById(id);
        if (order == null) {
            throw new RuntimeException("出库单不存在");
        }

        // 检查状态是否为已审核
        if (order.getProcessStatus() == null || order.getProcessStatus() != ProcessStatus.APPROVED) {
            throw new RuntimeException("只有已审核的订单才能申请退货");
        }

        // 检查业务类型是否为售出或交货
        if (order.getBusinessType() == null || (order.getBusinessType() != BusinessType.SALE && order.getBusinessType() != BusinessType.DELIVER)) {
            throw new RuntimeException("只有售出单或交货完成的预订单才能申请退货");
        }

        // 更新出库单状态为退货申请中
        InventoryOutOrder updateOrder = new InventoryOutOrder();
        updateOrder.setId(id);
        updateOrder.setBusinessType(BusinessType.RETURN); // 设置业务场景为退货
        updateOrder.setProcessStatus(ProcessStatus.PENDING); // 设置流程节点为待审核
        updateOrder.setUpdateBy(applyBy);

        // 创建退货申请记录
        InventoryOutAudit audit = new InventoryOutAudit();
        audit.setOrderId(id);
        // 移除旧的 action 设置，使用 businessType 和 processStatus 替代
        audit.setBusinessType(BusinessType.RETURN); // 退货业务
        audit.setProcessStatus(ProcessStatus.PENDING); // 待审核
        audit.setReason(applyReason);
        audit.setOperator(applyBy);
        inventoryOutAuditMapper.insertInventoryOutAudit(audit);

        return updateInventoryOutOrder(updateOrder);
    }

    /**
     * 审核通过退货申请
     *
     * @param id 出库单ID
     * @param approveBy 审核人
     * @param remark 审核备注
     * @return 结果
     */
    @Override
    @Transactional
    public int approveReturnOutOrder(Long id, String approveBy, String remark) {
        // 查询出库单
        InventoryOutOrder order = selectInventoryOutOrderById(id);
        if (order == null) {
            throw new RuntimeException("出库单不存在");
        }

        // 检查是否为退货业务且状态为待审核
        if (order.getBusinessType() == null || order.getBusinessType() != BusinessType.RETURN ||
            order.getProcessStatus() == null || order.getProcessStatus() != ProcessStatus.PENDING) {
            throw new RuntimeException("只有退货申请中的订单才能审核通过");
        }

        // 获取出库明细
        List<InventoryOutDetail> detailList = order.getDetailList();
        if (detailList == null || detailList.isEmpty()) {
            throw new RuntimeException("出库单明细不存在");
        }

        // 恢复库存
        for (InventoryOutDetail detail : detailList) {
            // 批次号可能包含多个，以逗号分隔
            String[] batchNumbers = detail.getBatchNumber().split(",");

            // 查找相关的库存记录
            for (String batchNumber : batchNumbers) {
                // 查询该批次的库存记录，确保匹配正确的商品和门店
                InventoryStock stockQuery = new InventoryStock();
                stockQuery.setBatchNumber(batchNumber);
                stockQuery.setProductId(detail.getProductId());
                stockQuery.setStoreId(order.getStoreId());
                stockQuery.setCounterId(detail.getCounterId());
                stockQuery.setChannelId(detail.getChannelId());

                List<InventoryStock> stockList = inventoryStockMapper.selectInventoryStockList(stockQuery);

                if (!stockList.isEmpty()) {
                    InventoryStock stock = stockList.get(0);

                    // 恢复库存
                    InventoryStock updateStock = new InventoryStock();
                    updateStock.setId(stock.getId());
                    updateStock.setQuantity(stock.getQuantity() + detail.getQuantity());
                    updateStock.setUpdateBy(approveBy);
                    inventoryStockMapper.updateInventoryStock(updateStock);
                } else {
                    // 如果找不到完全匹配的记录，创建新的库存记录
                    InventoryStock newStock = new InventoryStock();
                    newStock.setProductId(detail.getProductId());
                    newStock.setStoreId(order.getStoreId());
                    newStock.setCounterId(detail.getCounterId());
                    newStock.setChannelId(detail.getChannelId());
                    newStock.setBatchNumber(batchNumber);
                    newStock.setQuantity(detail.getQuantity());
                    newStock.setIsFutures(false); // 退货库存不是期货
                    newStock.setProductName(detail.getProductName());
                    newStock.setProductNumber(detail.getProductNumber());
                    newStock.setProductImage(detail.getProductImage());
                    newStock.setCounterName(detail.getCounterName());
                    newStock.setChannelName(detail.getChannelName());
                    newStock.setCreateBy(approveBy);
                    inventoryStockMapper.insertInventoryStock(newStock);

                    log.info("退货创建新库存: 批次号={}, 商品ID={}, 门店ID={}",
                        batchNumber, detail.getProductId(), order.getStoreId());
                }
            }
        }

        // 更新出库单状态为退货已审核
        InventoryOutOrder updateOrder = new InventoryOutOrder();
        updateOrder.setId(id);
        updateOrder.setBusinessType(BusinessType.RETURN); // 退货
        updateOrder.setProcessStatus(ProcessStatus.APPROVED); // 已审核
        updateOrder.setUpdateBy(approveBy);

        // 创建退货审核通过记录
        InventoryOutAudit audit = new InventoryOutAudit();
        audit.setOrderId(id);
        // 移除旧的 action 设置，使用 businessType 和 processStatus 替代
        audit.setBusinessType(BusinessType.RETURN); // 退货业务
        audit.setProcessStatus(ProcessStatus.APPROVED); // 已审核
        audit.setReason(remark);
        audit.setOperator(approveBy);
        inventoryOutAuditMapper.insertInventoryOutAudit(audit);

        // 更新会员累计消费金额（减去退货金额）
        updateMemberAmountForReturn(order);

        return updateInventoryOutOrder(updateOrder);
    }

    /**
     * 驳回退货申请
     *
     * @param id 出库单ID
     * @param rejectBy 驳回人
     * @param rejectReason 驳回原因
     * @return 结果
     */
    @Override
    @Transactional
    public int rejectReturnOutOrder(Long id, String rejectBy, String rejectReason) {
        // 查询出库单
        InventoryOutOrder order = selectInventoryOutOrderById(id);
        if (order == null) {
            throw new RuntimeException("出库单不存在");
        }

        // 检查是否为退货业务且状态为待审核
        if (order.getBusinessType() == null || order.getBusinessType() != BusinessType.RETURN ||
            order.getProcessStatus() == null || order.getProcessStatus() != ProcessStatus.PENDING) {
            throw new RuntimeException("只有退货申请中的订单才能驳回");
        }

        // 更新出库单状态为退货已驳回
        InventoryOutOrder updateOrder = new InventoryOutOrder();
        updateOrder.setId(id);
        updateOrder.setBusinessType(BusinessType.RETURN); // 退货
        updateOrder.setProcessStatus(ProcessStatus.REJECTED); // 已驳回
        updateOrder.setUpdateBy(rejectBy);

        // 创建退货驳回记录
        InventoryOutAudit audit = new InventoryOutAudit();
        audit.setOrderId(id);
        // 移除旧的 action 设置，使用 businessType 和 processStatus 替代
        audit.setBusinessType(BusinessType.RETURN); // 退货业务
        audit.setProcessStatus(ProcessStatus.REJECTED); // 已驳回
        audit.setReason(rejectReason);
        audit.setOperator(rejectBy);
        inventoryOutAuditMapper.insertInventoryOutAudit(audit);

        return updateInventoryOutOrder(updateOrder);
    }

    /**
     * 撤销退货申请
     *
     * @param id 出库单ID
     * @param cancelBy 撤销人
     * @param cancelReason 撤销原因
     * @return 结果
     */
    @Override
    @Transactional
    public int cancelReturnOutOrder(Long id, String cancelBy, String cancelReason) {
        // 查询出库单
        InventoryOutOrder order = selectInventoryOutOrderById(id);
        if (order == null) {
            throw new RuntimeException("出库单不存在");
        }

        // 检查是否为退货业务且状态为待审核
        if (order.getBusinessType() == null || order.getBusinessType() != BusinessType.RETURN ||
            order.getProcessStatus() == null || order.getProcessStatus() != ProcessStatus.PENDING) {
            throw new RuntimeException("只有退货申请中的订单才能撤销");
        }

        // 检查创建人是否匹配
        if (!cancelBy.equals(order.getCreateBy())) {
            throw new RuntimeException("只有申请人才能撤销退货申请");
        }

        // 获取订单的原始业务类型（从审核记录中查找）
        Integer originalBusinessType = BusinessType.SALE; // 默认为售出业务

        // 查询该订单的审核记录，按时间倒序排列
        List<InventoryOutAudit> auditList = inventoryOutAuditMapper.selectInventoryOutAuditByOrderId(id);
        if (auditList != null && !auditList.isEmpty()) {
            // 查找最近一次非退货业务的记录
            for (InventoryOutAudit audit : auditList) {
                if (audit.getBusinessType() != null && audit.getBusinessType() != BusinessType.RETURN) {
                    originalBusinessType = audit.getBusinessType();
                    break;
                }
            }
        }

        // 更新出库单状态为已审核（恢复到退货申请前的状态）
        InventoryOutOrder updateOrder = new InventoryOutOrder();
        updateOrder.setId(id);
        updateOrder.setBusinessType(originalBusinessType); // 恢复为原始业务类型
        updateOrder.setProcessStatus(ProcessStatus.APPROVED); // 已审核
        updateOrder.setUpdateBy(cancelBy);

        // 创建退货申请撤销记录
        InventoryOutAudit audit = new InventoryOutAudit();
        audit.setOrderId(id);
        // 使用正确的业务类型和流程状态
        audit.setBusinessType(BusinessType.RETURN); // 退货业务
        audit.setProcessStatus(ProcessStatus.CANCELED); // 已撤销
        audit.setReason(cancelReason);
        audit.setOperator(cancelBy);
        inventoryOutAuditMapper.insertInventoryOutAudit(audit);

        return updateInventoryOutOrder(updateOrder);
    }

    /**
     * 更新会员累计消费金额
     *
     * @param order 出库单
     */
    private void updateMemberAmount(InventoryOutOrder order) {
        // 直接从订单中获取会员ID
        Long memberId = order.getMemberId();

        // 如果订单没有关联会员ID，无法更新会员累计消费金额
        if (memberId == null) {
            return;
        }

        // 更新会员累计消费金额
        if (order.getTotalAmount() != null) {
            double amount = order.getTotalAmount().doubleValue();
            memberService.updateMemberAmount(memberId, amount);
            log.info("更新会员累计消费金额: 会员ID={}, 金额={}", memberId, amount);
        }
    }

    /**
     * 更新会员累计消费金额（退货时减少）
     *
     * @param order 出库单
     */
    private void updateMemberAmountForReturn(InventoryOutOrder order) {
        // 直接从订单中获取会员ID
        Long memberId = order.getMemberId();

        // 如果订单没有关联会员ID，无法更新会员累计消费金额
        if (memberId == null) {
            return;
        }

        // 更新会员累计消费金额（减去退货金额）
        if (order.getTotalAmount() != null) {
            double amount = -order.getTotalAmount().doubleValue(); // 负值表示减少
            memberService.updateMemberAmount(memberId, amount);
            log.info("退货减少会员累计消费金额: 会员ID={}, 金额={}", memberId, amount);
        }
    }

    /**
     * 查询出库单列表（用于数据分析，不应用数据范围过滤）
     *
     * @param inventoryOutOrder 出库单
     * @return 出库单集合
     */
    @Override
    public List<InventoryOutOrder> selectInventoryOutOrderListForAnalysis(InventoryOutOrder inventoryOutOrder)
    {
        // 确保params不为null
        if (inventoryOutOrder.getParams() == null) {
            inventoryOutOrder.setParams(new HashMap<>());
        }

        // 设置跳过数据范围过滤的标记
        inventoryOutOrder.getParams().put("skipDataScope", true);

        // 记录查询参数
        log.info("查询出库单列表（用于数据分析），参数：businessType={}, processStatus={}, params={}",
                inventoryOutOrder.getBusinessType(),
                inventoryOutOrder.getProcessStatus(),
                inventoryOutOrder.getParams());

        List<InventoryOutOrder> result = inventoryOutOrderMapper.selectInventoryOutOrderList(inventoryOutOrder);
        log.info("查询结果数量: {}", result.size());

        return result;
    }

    /**
     * 保存预订单及明细
     *
     * @param inventoryOutOrder 预订单
     * @param detailList 预订明细列表
     * @return 结果
     */
    @Override
    @Transactional
    public int saveReserveOrderWithDetails(InventoryOutOrder inventoryOutOrder, List<InventoryOutDetail> detailList)
    {
        // 设置订单类型为预订单
        inventoryOutOrder.setOrderType(1); // 1=预订单

        // 设置业务场景为预订
        inventoryOutOrder.setBusinessType(BusinessType.RESERVE);

        // 设置流程节点为待审核
        inventoryOutOrder.setProcessStatus(ProcessStatus.PENDING);

        // 保存预订单
        int rows = insertInventoryOutOrder(inventoryOutOrder);
        if (rows > 0 && detailList != null && !detailList.isEmpty()) {
            // 处理预订明细
            for (InventoryOutDetail detail : detailList) {
                detail.setOrderId(inventoryOutOrder.getId());
                // 设置创建人
                detail.setCreateBy(inventoryOutOrder.getCreateBy());

                // 保存预订明细
                inventoryOutDetailMapper.insertInventoryOutDetail(detail);
            }

            // 创建一条预订单提交的审核记录
            InventoryOutAudit audit = new InventoryOutAudit();
            audit.setOrderId(inventoryOutOrder.getId());
            // 移除旧的 action 设置，使用 businessType 和 processStatus 替代
            audit.setBusinessType(BusinessType.RESERVE); // 预订业务
            audit.setProcessStatus(ProcessStatus.PENDING); // 待审核
            audit.setOperator(inventoryOutOrder.getCreateBy());
            inventoryOutAuditMapper.insertInventoryOutAudit(audit);
        }

        return rows;
    }

    /**
     * 从Map创建预订单
     *
     * @param params 预订单参数
     * @return 结果
     */
    @Override
    @Transactional
    public int createReserveOrderFromMap(Map<String, Object> params)
    {
        if (params == null) {
            throw new RuntimeException("参数不能为空");
        }

        // 创建预订单对象
        InventoryOutOrder order = new InventoryOutOrder();

        // 设置订单类型为预订单
        order.setOrderType(1); // 1=预订单

        // 设置业务场景为预订
        order.setBusinessType(BusinessType.RESERVE);

        // 设置流程节点为待审核
        order.setProcessStatus(ProcessStatus.PENDING);

        // 设置支付方式
        if (params.get("payType") != null) {
            order.setPayType(Integer.valueOf(params.get("payType").toString()));
        }

        // 设置预订支付类型
        if (params.get("depositType") != null) {
            order.setDepositType(Integer.valueOf(params.get("depositType").toString()));
        }

        // 设置定金金额
        if (params.get("depositAmount") != null) {
            order.setDepositAmount(new BigDecimal(params.get("depositAmount").toString()));
        }

        // 设置会员ID
        if (params.get("memberId") != null) {
            order.setMemberId(Long.valueOf(params.get("memberId").toString()));
        }

        // 设置门店ID
        if (params.get("storeId") != null) {
            order.setStoreId(Long.valueOf(params.get("storeId").toString()));
        }

        // 设置总金额
        if (params.get("totalSalePrice") != null) {
            order.setTotalAmount(new BigDecimal(params.get("totalSalePrice").toString()));
        }

        // 设置备注
        if (params.get("remark") != null) {
            order.setRemark(params.get("remark").toString());
        }

        // 设置流程状态
        if (params.get("processStatus") != null) {
            order.setProcessStatus(Integer.valueOf(params.get("processStatus").toString()));
        } else {
            order.setProcessStatus(ProcessStatus.PENDING); // 默认待审核
        }

        // 设置创建人
        if (params.get("createBy") != null) {
            order.setCreateBy(params.get("createBy").toString());
        }

        // 获取预订明细列表
        List<InventoryOutDetail> detailList = new ArrayList<>();
        if (params.get("items") != null && params.get("items") instanceof List) {
            List<Map<String, Object>> items = (List<Map<String, Object>>) params.get("items");

            for (Map<String, Object> item : items) {
                InventoryOutDetail detail = new InventoryOutDetail();

                // 设置商品ID
                if (item.get("productId") != null) {
                    detail.setProductId(Long.valueOf(item.get("productId").toString()));
                }

                // 设置商品名称
                if (item.get("productName") != null) {
                    detail.setProductName(item.get("productName").toString());
                }

                // 设置商品货号
                if (item.get("productNumber") != null) {
                    detail.setProductNumber(item.get("productNumber").toString());
                }

                // 设置售价
                if (item.get("salePrice") != null) {
                    detail.setSalePrice(new BigDecimal(item.get("salePrice").toString()));
                }

                // 设置数量
                if (item.get("quantity") != null) {
                    detail.setQuantity(Integer.valueOf(item.get("quantity").toString()));
                } else {
                    detail.setQuantity(1); // 默认数量为1
                }

                detailList.add(detail);
            }
        }

        // 保存预订单及明细
        return saveReserveOrderWithDetails(order, detailList);
    }

    /**
     * 交货预订单
     *
     * @param inventoryOutOrder 预订单
     * @return 结果
     */
    @Override
    @Transactional
    public int deliverReserveOrder(InventoryOutOrder inventoryOutOrder, String remark)
    {
        // 验证预订单
        if (inventoryOutOrder == null || inventoryOutOrder.getId() == null) {
            throw new RuntimeException("预订单不存在");
        }

        // 验证预订单状态 - 不需要检查业务类型和流程状态，因为这是第一次调用，还没有设置业务类型

        // 验证预订单类型
        if (inventoryOutOrder.getOrderType() == null || inventoryOutOrder.getOrderType() != 1) {
            throw new RuntimeException("非预订单不能进行交货操作");
        }

        // 获取完整的预订单信息
        InventoryOutOrder fullOrder = inventoryOutOrderMapper.selectInventoryOutOrderById(inventoryOutOrder.getId());
        if (fullOrder == null) {
            throw new RuntimeException("预订单不存在");
        }

        // 获取预订单明细
        List<InventoryOutDetail> detailList = inventoryOutDetailMapper.selectInventoryOutDetailByOrderId(inventoryOutOrder.getId());
        if (detailList == null || detailList.isEmpty()) {
            throw new RuntimeException("预订单明细不存在");
        }

        // 检查每个商品是否有库存
        for (InventoryOutDetail detail : detailList) {
            // 查询当前门店是否有该商品的库存
            InventoryStock stockQuery = new InventoryStock();
            stockQuery.setProductId(detail.getProductId());
            stockQuery.setStoreId(fullOrder.getStoreId());
            stockQuery.setQuantity(1); // 至少有1个库存

            List<InventoryStock> stockList = inventoryStockMapper.selectInventoryStockList(stockQuery);
            if (stockList == null || stockList.isEmpty()) {
                throw new RuntimeException("商品 " + detail.getProductName() + " 在当前门店没有库存，无法完成交货");
            }

            // 检查库存数量是否足够
            int availableQuantity = stockList.stream().mapToInt(InventoryStock::getQuantity).sum();
            if (availableQuantity < detail.getQuantity()) {
                throw new RuntimeException("商品 " + detail.getProductName() + " 库存不足，需要 " + detail.getQuantity() + " 个，但只有 " + availableQuantity + " 个");
            }
        }

        // 处理每个商品的库存
        for (InventoryOutDetail detail : detailList) {
            // 查询当前门店该商品的库存，按入库时间排序
            InventoryStock stockQuery = new InventoryStock();
            stockQuery.setProductId(detail.getProductId());
            stockQuery.setStoreId(fullOrder.getStoreId());
            stockQuery.getParams().put("orderBy", "create_time asc"); // 按入库时间排序

            List<InventoryStock> stockList = inventoryStockMapper.selectInventoryStockList(stockQuery);

            // 需要扣减的数量
            int remainingQuantity = detail.getQuantity();

            // 从最早入库的库存开始扣减
            for (InventoryStock stock : stockList) {
                if (remainingQuantity <= 0) {
                    break;
                }

                // 确定本次从该库存扣减的数量
                int deductQuantity = Math.min(remainingQuantity, stock.getQuantity());

                // 更新库存数量
                InventoryStock updateStock = new InventoryStock();
                updateStock.setId(stock.getId());
                updateStock.setQuantity(stock.getQuantity() - deductQuantity);
                updateStock.setUpdateBy(inventoryOutOrder.getUpdateBy());
                inventoryStockMapper.updateInventoryStock(updateStock);

                // 更新预订单明细的库存信息
                // 设置库存相关信息
                detail.setCounterId(stock.getCounterId());
                detail.setChannelId(stock.getChannelId());
                detail.setBatchNumber(stock.getBatchNumber());
                detail.setUpdateBy(inventoryOutOrder.getUpdateBy());
                detail.setUpdateTime(new Date());
                inventoryOutDetailMapper.updateInventoryOutDetail(detail);

                // 减少剩余需要扣减的数量
                remainingQuantity -= deductQuantity;
            }

            // 如果还有剩余数量未扣减，说明库存不足
            if (remainingQuantity > 0) {
                throw new RuntimeException("商品 " + detail.getProductName() + " 库存不足，无法完成交货");
            }
        }

        // 更新预订单状态为待审核
        InventoryOutOrder updateOrder = new InventoryOutOrder();
        updateOrder.setId(inventoryOutOrder.getId());
        updateOrder.setBusinessType(BusinessType.DELIVER); // 交货
        updateOrder.setProcessStatus(ProcessStatus.PENDING); // 待审核

        // 如果是定金支付，更新尾款信息
        if (inventoryOutOrder.getDepositType() != null && inventoryOutOrder.getDepositType() == 0) {
            updateOrder.setBalancePayType(inventoryOutOrder.getBalancePayType());
            updateOrder.setBalanceAmount(inventoryOutOrder.getBalanceAmount());
        }

        // 不更新订单备注，而是将备注添加到审核记录中

        // 创建交货记录
        InventoryOutAudit audit = new InventoryOutAudit();
        audit.setOrderId(inventoryOutOrder.getId());
        // 移除旧的 action 设置，使用 businessType 和 processStatus 替代
        audit.setBusinessType(BusinessType.DELIVER); // 交货业务
        audit.setProcessStatus(ProcessStatus.PENDING); // 待审核
        audit.setReason(remark); // 将申请备注添加到审核记录中
        audit.setOperator(inventoryOutOrder.getUpdateBy());
        inventoryOutAuditMapper.insertInventoryOutAudit(audit);

        // 交货申请还没有审核通过，不更新会员累计消费金额
        // 会员累计消费金额将在交货审核通过后更新

        return updateInventoryOutOrder(updateOrder);
    }

    /**
     * 取消预订单（待交货状态）
     *
     * @param id 预订单ID
     * @param cancelBy 取消人
     * @param cancelReason 取消原因
     * @return 结果
     */
    @Override
    @Transactional
    public int cancelReserveOrder(Long id, String cancelBy, String cancelReason) {
        // 查询预订单
        InventoryOutOrder order = selectInventoryOutOrderById(id);
        if (order == null) {
            throw new RuntimeException("预订单不存在");
        }

        // 检查是否为预订单
        if (order.getOrderType() == null || order.getOrderType() != 1) {
            throw new RuntimeException("该订单不是预订单，无法取消");
        }

        // 检查是否为预订业务且状态为已审核
        if (order.getBusinessType() == null || order.getBusinessType() != BusinessType.RESERVE ||
            order.getProcessStatus() == null || order.getProcessStatus() != ProcessStatus.APPROVED) {
            throw new RuntimeException("只有已审核的预订单才能取消");
        }

        // 更新预订单状态为待审核的取消申请
        InventoryOutOrder updateOrder = new InventoryOutOrder();
        updateOrder.setId(id);
        updateOrder.setBusinessType(BusinessType.CANCEL); // 取消
        updateOrder.setProcessStatus(ProcessStatus.PENDING); // 待审核
        updateOrder.setUpdateBy(cancelBy);

        // 创建取消申请记录
        InventoryOutAudit audit = new InventoryOutAudit();
        audit.setOrderId(id);
        // 移除旧的 action 设置，使用 businessType 和 processStatus 替代
        audit.setBusinessType(BusinessType.CANCEL); // 取消业务
        audit.setProcessStatus(ProcessStatus.PENDING); // 待审核
        audit.setReason(cancelReason);
        audit.setOperator(cancelBy);
        inventoryOutAuditMapper.insertInventoryOutAudit(audit);

        return updateInventoryOutOrder(updateOrder);
    }

    /**
     * 审核通过取消预订单申请
     *
     * @param id 预订单ID
     * @param approveBy 审核人
     * @param remark 审核备注
     * @return 结果
     */
    @Override
    @Transactional
    public int approveCancelReserveOrder(Long id, String approveBy, String remark) {
        // 查询预订单
        InventoryOutOrder order = selectInventoryOutOrderById(id);
        if (order == null) {
            throw new RuntimeException("预订单不存在");
        }

        // 检查是否为取消业务且状态为待审核
        if (order.getBusinessType() == null || order.getBusinessType() != BusinessType.CANCEL ||
            order.getProcessStatus() == null || order.getProcessStatus() != ProcessStatus.PENDING) {
            throw new RuntimeException("只有待审核的取消申请才能审核通过");
        }

        // 更新预订单状态为已取消且已审核
        InventoryOutOrder updateOrder = new InventoryOutOrder();
        updateOrder.setId(id);
        updateOrder.setBusinessType(BusinessType.CANCEL); // 取消
        updateOrder.setProcessStatus(ProcessStatus.APPROVED); // 已审核
        updateOrder.setUpdateBy(approveBy);

        // 创建审核通过记录
        InventoryOutAudit audit = new InventoryOutAudit();
        audit.setOrderId(id);
        audit.setBusinessType(BusinessType.CANCEL); // 取消业务
        audit.setProcessStatus(ProcessStatus.APPROVED); // 已审核
        audit.setReason(remark);
        audit.setOperator(approveBy);
        inventoryOutAuditMapper.insertInventoryOutAudit(audit);

        return updateInventoryOutOrder(updateOrder);
    }

    /**
     * 驳回取消预订单申请
     *
     * @param id 预订单ID
     * @param rejectBy 驳回人
     * @param rejectReason 驳回原因
     * @return 结果
     */
    @Override
    @Transactional
    public int rejectCancelReserveOrder(Long id, String rejectBy, String rejectReason) {
        // 查询预订单
        InventoryOutOrder order = selectInventoryOutOrderById(id);
        if (order == null) {
            throw new RuntimeException("预订单不存在");
        }

        // 检查是否为取消业务且状态为待审核
        if (order.getBusinessType() == null || order.getBusinessType() != BusinessType.CANCEL ||
            order.getProcessStatus() == null || order.getProcessStatus() != ProcessStatus.PENDING) {
            throw new RuntimeException("只有待审核的取消申请才能驳回");
        }

        // 恢复订单状态为原来的预订状态
        InventoryOutOrder updateOrder = new InventoryOutOrder();
        updateOrder.setId(id);
        updateOrder.setBusinessType(BusinessType.RESERVE); // 恢复为预订业务
        updateOrder.setProcessStatus(ProcessStatus.APPROVED); // 已审核
        updateOrder.setUpdateBy(rejectBy);

        // 创建驳回记录
        InventoryOutAudit audit = new InventoryOutAudit();
        audit.setOrderId(id);
        audit.setBusinessType(BusinessType.CANCEL); // 取消业务
        audit.setProcessStatus(ProcessStatus.REJECTED); // 已驳回
        audit.setReason(rejectReason);
        audit.setOperator(rejectBy);
        inventoryOutAuditMapper.insertInventoryOutAudit(audit);

        return updateInventoryOutOrder(updateOrder);
    }

    /**
     * 审核通过交货申请
     *
     * @param id 预订单ID
     * @param approveBy 审核人
     * @param remark 审核备注
     * @return 结果
     */
    @Override
    @Transactional
    public int approveDeliverReserveOrder(Long id, String approveBy, String remark) {
        // 查询预订单
        InventoryOutOrder order = selectInventoryOutOrderById(id);
        if (order == null) {
            throw new RuntimeException("预订单不存在");
        }

        // 检查是否为交货业务且状态为待审核
        if (order.getBusinessType() == null || order.getBusinessType() != BusinessType.DELIVER ||
            order.getProcessStatus() == null || order.getProcessStatus() != ProcessStatus.PENDING) {
            throw new RuntimeException("只有待审核的交货申请才能审核通过");
        }

        // 更新预订单状态为已交货且已审核
        InventoryOutOrder updateOrder = new InventoryOutOrder();
        updateOrder.setId(id);
        updateOrder.setBusinessType(BusinessType.DELIVER); // 交货
        updateOrder.setProcessStatus(ProcessStatus.APPROVED); // 已审核
        updateOrder.setUpdateBy(approveBy);

        // 创建审核通过记录
        InventoryOutAudit audit = new InventoryOutAudit();
        audit.setOrderId(id);
        audit.setBusinessType(BusinessType.DELIVER); // 交货业务
        audit.setProcessStatus(ProcessStatus.APPROVED); // 已审核
        audit.setReason(remark);
        audit.setOperator(approveBy);
        inventoryOutAuditMapper.insertInventoryOutAudit(audit);

        // 更新会员累计消费金额
        updateMemberAmount(order);

        return updateInventoryOutOrder(updateOrder);
    }

    /**
     * 驳回交货申请
     *
     * @param id 预订单ID
     * @param rejectBy 驳回人
     * @param rejectReason 驳回原因
     * @return 结果
     */
    @Override
    @Transactional
    public int rejectDeliverReserveOrder(Long id, String rejectBy, String rejectReason) {
        // 查询预订单
        InventoryOutOrder order = selectInventoryOutOrderById(id);
        if (order == null) {
            throw new RuntimeException("预订单不存在");
        }

        // 检查是否为交货业务且状态为待审核
        if (order.getBusinessType() == null || order.getBusinessType() != BusinessType.DELIVER ||
            order.getProcessStatus() == null || order.getProcessStatus() != ProcessStatus.PENDING) {
            throw new RuntimeException("只有待审核的交货申请才能驳回");
        }

        // 恢复订单状态为原来的预订状态
        InventoryOutOrder updateOrder = new InventoryOutOrder();
        updateOrder.setId(id);
        updateOrder.setBusinessType(BusinessType.RESERVE); // 恢复为预订业务
        updateOrder.setProcessStatus(ProcessStatus.APPROVED); // 已审核
        updateOrder.setUpdateBy(rejectBy);

        // 创建驳回记录
        InventoryOutAudit audit = new InventoryOutAudit();
        audit.setOrderId(id);
        audit.setBusinessType(BusinessType.DELIVER); // 交货业务
        audit.setProcessStatus(ProcessStatus.REJECTED); // 已驳回
        audit.setReason(rejectReason);
        audit.setOperator(rejectBy);
        inventoryOutAuditMapper.insertInventoryOutAudit(audit);

        return updateInventoryOutOrder(updateOrder);
    }
}