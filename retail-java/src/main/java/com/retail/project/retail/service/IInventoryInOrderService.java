package com.retail.project.retail.service;

import java.util.List;
import com.retail.project.retail.domain.InventoryInOrder;
import com.retail.project.retail.domain.InventoryInDetail;

/**
 * 入库单Service接口
 * 
 * <AUTHOR>
 * @date 2025-04-17
 */
public interface IInventoryInOrderService 
{
    /**
     * 查询入库单
     * 
     * @param id 入库单主键
     * @return 入库单
     */
    public InventoryInOrder selectInventoryInOrderById(Long id);

    /**
     * 查询入库单列表
     * 
     * @param inventoryInOrder 入库单
     * @return 入库单集合
     */
    public List<InventoryInOrder> selectInventoryInOrderList(InventoryInOrder inventoryInOrder);

    /**
     * 新增入库单
     * 
     * @param inventoryInOrder 入库单
     * @return 结果
     */
    public int insertInventoryInOrder(InventoryInOrder inventoryInOrder);

    /**
     * 修改入库单
     * 
     * @param inventoryInOrder 入库单
     * @return 结果
     */
    public int updateInventoryInOrder(InventoryInOrder inventoryInOrder);

    /**
     * 批量删除入库单
     * 
     * @param ids 需要删除的入库单主键集合
     * @return 结果
     */
    public int deleteInventoryInOrderByIds(Long[] ids);

    /**
     * 删除入库单信息
     * 
     * @param id 入库单主键
     * @return 结果
     */
    public int deleteInventoryInOrderById(Long id);
    
    /**
     * 保存入库单及明细
     * 
     * @param inventoryInOrder 入库单
     * @param detailList 入库明细列表
     * @return 结果
     */
    public int saveInventoryInOrderWithDetails(InventoryInOrder inventoryInOrder, List<InventoryInDetail> detailList);
} 