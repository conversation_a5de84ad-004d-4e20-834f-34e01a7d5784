package com.retail.common.constant;

/**
 * 角色常量信息
 * 
 * <AUTHOR>
 */
public class RoleConstants
{
    /**
     * 超级管理员角色
     */
    public static final String ADMIN = "admin";

    /**
     * 管理员角色
     */
    public static final String MANAGER = "manager";

    /**
     * 财务角色
     */
    public static final String FINANCE = "finance";

    /**
     * 判断是否为管理类角色（admin、manager、finance）
     * 
     * @param roleKey 角色标识
     * @return 是否为管理类角色
     */
    public static boolean isManagementRole(String roleKey)
    {
        return ADMIN.equals(roleKey) || MANAGER.equals(roleKey) || FINANCE.equals(roleKey);
    }
}
