-- 添加member_id字段到inventory_out_order表
ALTER TABLE `inventory_out_order` ADD COLUMN `member_id` bigint(20) DEFAULT NULL COMMENT '会员ID' AFTER `order_number`;

-- 添加索引
ALTER TABLE `inventory_out_order` ADD INDEX `idx_member_id` (`member_id`) COMMENT '会员ID索引';

-- 添加外键约束
ALTER TABLE `inventory_out_order` ADD CONSTRAINT `fk_out_order_member` FOREIGN KEY (`member_id`) REFERENCES `member` (`member_id`) ON DELETE RESTRICT ON UPDATE CASCADE;
