<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.retail.project.member.mapper.MemberLevelMapper">

    <resultMap type="MemberLevel" id="MemberLevelResult">
        <result property="levelId"    column="level_id"    />
        <result property="levelName"    column="level_name"    />
        <result property="minAmount"    column="min_amount"    />
        <result property="maxAmount"    column="max_amount"    />
        <result property="discountRate"    column="discount_rate"    />
        <result property="tagType"    column="tag_type"    />
        <result property="status"    column="status"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="remark"    column="remark"    />
    </resultMap>

    <sql id="selectMemberLevelVo">
        select level_id, level_name, min_amount, max_amount, discount_rate, tag_type, status, create_by, create_time, update_by, update_time, remark from member_level
    </sql>

    <select id="selectMemberLevelList" parameterType="MemberLevel" resultMap="MemberLevelResult">
        <include refid="selectMemberLevelVo"/>
        <where>
            <if test="levelName != null  and levelName != ''"> and level_name like concat('%', #{levelName}, '%')</if>
            <if test="status != null"> and status = #{status}</if>
        </where>
    </select>

    <select id="selectMemberLevelByLevelId" parameterType="Long" resultMap="MemberLevelResult">
        <include refid="selectMemberLevelVo"/>
        where level_id = #{levelId}
    </select>

    <select id="selectMemberLevelAll" resultMap="MemberLevelResult">
        <include refid="selectMemberLevelVo"/>
        where status = 1
    </select>

    <insert id="insertMemberLevel" parameterType="MemberLevel" useGeneratedKeys="true" keyProperty="levelId">
        insert into member_level
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="levelName != null and levelName != ''">level_name,</if>
            <if test="minAmount != null">min_amount,</if>
            <if test="maxAmount != null">max_amount,</if>
            <if test="discountRate != null">discount_rate,</if>
            <if test="tagType != null">tag_type,</if>
            <if test="status != null">status,</if>
            <if test="createBy != null and createBy != ''">create_by,</if>
            <if test="remark != null and remark != ''">remark,</if>
            create_time,
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="levelName != null and levelName != ''">#{levelName},</if>
            <if test="minAmount != null">#{minAmount},</if>
            <if test="maxAmount != null">#{maxAmount},</if>
            <if test="discountRate != null">#{discountRate},</if>
            <if test="tagType != null">#{tagType},</if>
            <if test="status != null">#{status},</if>
            <if test="createBy != null and createBy != ''">#{createBy},</if>
            <if test="remark != null and remark != ''">#{remark},</if>
            sysdate(),
         </trim>
    </insert>

    <update id="updateMemberLevel" parameterType="MemberLevel">
        update member_level
        <trim prefix="SET" suffixOverrides=",">
            <if test="levelName != null and levelName != ''">level_name = #{levelName},</if>
            <if test="minAmount != null">min_amount = #{minAmount},</if>
            <if test="maxAmount != null">max_amount = #{maxAmount},</if>
            <if test="discountRate != null">discount_rate = #{discountRate},</if>
            <if test="tagType != null">tag_type = #{tagType},</if>
            <if test="status != null">status = #{status},</if>
            <if test="updateBy != null and updateBy != ''">update_by = #{updateBy},</if>
            <if test="remark != null and remark != ''">remark = #{remark},</if>
            update_time = sysdate(),
        </trim>
        where level_id = #{levelId}
    </update>

    <delete id="deleteMemberLevelByLevelId" parameterType="Long">
        delete from member_level where level_id = #{levelId}
    </delete>

    <delete id="deleteMemberLevelByLevelIds" parameterType="String">
        delete from member_level where level_id in
        <foreach item="levelId" collection="array" open="(" separator="," close=")">
            #{levelId}
        </foreach>
    </delete>
</mapper>
