<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.retail.project.member.mapper.MemberMapper">

    <resultMap type="Member" id="MemberResult">
        <result property="memberId"    column="member_id"    />
        <result property="memberName"    column="member_name"    />
        <result property="phoneNumber"    column="phone_number"    />
        <result property="gender"    column="gender"    />
        <result property="birthday"    column="birthday"    />
        <result property="totalAmount"    column="total_amount"    />
        <result property="memberLevel"    column="member_level"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="remark"    column="remark"    />
    </resultMap>

    <sql id="selectMemberVo">
        select member_id, member_name, phone_number, gender, birthday, total_amount, member_level, create_by, create_time, update_by, update_time, remark from member
    </sql>

    <select id="selectMemberList" parameterType="Member" resultMap="MemberResult">
        <include refid="selectMemberVo"/>
        <where>
            <if test="memberName != null  and memberName != ''"> and member_name like concat('%', #{memberName}, '%')</if>
            <if test="phoneNumber != null  and phoneNumber != ''"> and phone_number like concat('%', #{phoneNumber}, '%')</if>
            <if test="gender != null"> and gender = #{gender}</if>
            <if test="memberLevel != null "> and member_level = #{memberLevel}</if>
            <if test="params.beginCreateTime != null and params.beginCreateTime != '' and params.endCreateTime != null and params.endCreateTime != ''"> and create_time between #{params.beginCreateTime} and #{params.endCreateTime}</if>
        </where>
    </select>

    <select id="selectMemberByMemberId" parameterType="Long" resultMap="MemberResult">
        <include refid="selectMemberVo"/>
        where member_id = #{memberId}
    </select>

    <select id="selectMemberByPhoneNumber" parameterType="String" resultMap="MemberResult">
        <include refid="selectMemberVo"/>
        where phone_number = #{phoneNumber}
    </select>

    <insert id="insertMember" parameterType="Member">
        insert into member
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="memberId != null">member_id,</if>
            <if test="memberName != null and memberName != ''">member_name,</if>
            <if test="phoneNumber != null and phoneNumber != ''">phone_number,</if>
            <if test="gender != null">gender,</if>
            <if test="birthday != null">birthday,</if>
            <if test="totalAmount != null">total_amount,</if>
            <if test="memberLevel != null">member_level,</if>
            <if test="createBy != null and createBy != ''">create_by,</if>
            <if test="remark != null and remark != ''">remark,</if>
            create_time,
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="memberId != null">#{memberId},</if>
            <if test="memberName != null and memberName != ''">#{memberName},</if>
            <if test="phoneNumber != null and phoneNumber != ''">#{phoneNumber},</if>
            <if test="gender != null">#{gender},</if>
            <if test="birthday != null">#{birthday},</if>
            <if test="totalAmount != null">#{totalAmount},</if>
            <if test="memberLevel != null">#{memberLevel},</if>
            <if test="createBy != null and createBy != ''">#{createBy},</if>
            <if test="remark != null and remark != ''">#{remark},</if>
            sysdate(),
         </trim>
    </insert>

    <update id="updateMember" parameterType="Member">
        update member
        <trim prefix="SET" suffixOverrides=",">
            <if test="memberName != null and memberName != ''">member_name = #{memberName},</if>
            <if test="phoneNumber != null and phoneNumber != ''">phone_number = #{phoneNumber},</if>
            <if test="gender != null">gender = #{gender},</if>
            <if test="birthday != null">birthday = #{birthday},</if>
            <if test="totalAmount != null">total_amount = #{totalAmount},</if>
            <if test="memberLevel != null">member_level = #{memberLevel},</if>
            <if test="updateBy != null and updateBy != ''">update_by = #{updateBy},</if>
            <if test="remark != null and remark != ''">remark = #{remark},</if>
            update_time = sysdate(),
        </trim>
        where member_id = #{memberId}
    </update>

    <delete id="deleteMemberByMemberId" parameterType="Long">
        delete from member where member_id = #{memberId}
    </delete>

    <delete id="deleteMemberByMemberIds" parameterType="String">
        delete from member where member_id in
        <foreach item="memberId" collection="array" open="(" separator="," close=")">
            #{memberId}
        </foreach>
    </delete>

    <update id="updateMemberAmount" parameterType="Member">
        update member
        <trim prefix="SET" suffixOverrides=",">
            <if test="totalAmount != null">total_amount = #{totalAmount},</if>
            <if test="memberLevel != null">member_level = #{memberLevel},</if>
            update_time = sysdate(),
        </trim>
        where member_id = #{memberId}
    </update>
</mapper>
