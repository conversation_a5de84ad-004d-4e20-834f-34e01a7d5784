<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.retail.project.retail.mapper.RetailChannelMapper">
    
    <resultMap type="RetailChannel" id="RetailChannelResult">
        <result property="id"    column="id"    />
        <result property="channelName"    column="channel_name"    />
        <result property="userId"    column="user_id"    />
        <result property="userName"    column="user_name"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="remark"    column="remark"    />
    </resultMap>

    <sql id="selectRetailChannelVo">
        select id, channel_name, user_id, user_name, create_by, create_time, update_by, update_time, remark from retail_channel
    </sql>

    <select id="selectRetailChannelList" parameterType="RetailChannel" resultMap="RetailChannelResult">
        <include refid="selectRetailChannelVo"/>
        <where>  
            <if test="channelName != null  and channelName != ''"> and channel_name like concat('%', #{channelName}, '%')</if>
            <if test="userId != null  and userId != ''"> and user_id = #{userId}</if>
            <if test="userName != null  and userName != ''"> and user_name like concat('%', #{userName}, '%')</if>
        </where>
    </select>
    
    <select id="selectRetailChannelById" parameterType="Long" resultMap="RetailChannelResult">
        <include refid="selectRetailChannelVo"/>
        where id = #{id}
    </select>

    <insert id="insertRetailChannel" parameterType="RetailChannel" useGeneratedKeys="true" keyProperty="id">
        insert into retail_channel
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="channelName != null">channel_name,</if>
            <if test="userId != null">user_id,</if>
            <if test="userName != null">user_name,</if>
            <if test="createBy != null and createBy != ''">create_by,</if>
            <if test="remark != null and remark != ''">remark,</if>
            create_time,
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="channelName != null">#{channelName},</if>
            <if test="userId != null">#{userId},</if>
            <if test="userName != null">#{userName},</if>
            <if test="createBy != null and createBy != ''">#{createBy},</if>
            <if test="remark != null and remark != ''">#{remark},</if>
            sysdate(),
         </trim>
    </insert>

    <update id="updateRetailChannel" parameterType="RetailChannel">
        update retail_channel
        <trim prefix="SET" suffixOverrides=",">
            <if test="channelName != null">channel_name = #{channelName},</if>
            <if test="userId != null">user_id = #{userId},</if>
            <if test="userName != null">user_name = #{userName},</if>
            <if test="updateBy != null and updateBy != ''">update_by = #{updateBy},</if>
            <if test="remark != null">remark = #{remark},</if>
            update_time = sysdate(),
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteRetailChannelById" parameterType="Long">
        delete from retail_channel where id = #{id}
    </delete>

    <delete id="deleteRetailChannelByIds" parameterType="String">
        delete from retail_channel where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>