<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.retail.project.retail.mapper.InventoryInDetailMapper">
    
    <resultMap type="InventoryInDetail" id="InventoryInDetailResult">
        <result property="id"    column="id"    />
        <result property="orderId"    column="order_id"    />
        <result property="productId"    column="product_id"    />
        <result property="storeId"    column="store_id"    />
        <result property="counterId"    column="counter_id"    />
        <result property="quantity"    column="quantity"    />
        <result property="purchasePrice"    column="purchase_price"    />
        <result property="batchNumber"    column="batch_number"    />
        <result property="productName"    column="product_name"    />
        <result property="productNumber"    column="product_number"    />
        <result property="productImage"    column="product_image"    />
        <result property="storeName"    column="store_name"    />
        <result property="counterName"    column="counter_name"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="remark"    column="remark"    />
    </resultMap>

    <sql id="selectInventoryInDetailVo">
        select d.id, d.order_id, d.product_id, d.store_id, d.counter_id, d.quantity, d.purchase_price, d.batch_number,
        p.product_number, p.product_name, p.product_image, s.store_name, c.counter_name, d.create_by, d.create_time, d.update_by, d.update_time, d.remark
        from inventory_in_detail d
        left join retail_products p on d.product_id = p.id
        left join retail_store s on d.store_id = s.id
        left join retail_counter c on d.counter_id = c.id
    </sql>

    <select id="selectInventoryInDetailList" parameterType="InventoryInDetail" resultMap="InventoryInDetailResult">
        <include refid="selectInventoryInDetailVo"/>
        <where>  
            <if test="orderId != null "> and d.order_id = #{orderId}</if>
            <if test="productId != null "> and d.product_id = #{productId}</if>
            <if test="storeId != null "> and d.store_id = #{storeId}</if>
            <if test="counterId != null "> and d.counter_id = #{counterId}</if>
            <if test="productNumber != null  and productNumber != ''"> and p.product_number like concat('%', #{productNumber}, '%')</if>
            <if test="storeName != null  and storeName != ''"> and s.store_name like concat('%', #{storeName}, '%')</if>
            <if test="counterName != null  and counterName != ''"> and c.counter_name like concat('%', #{counterName}, '%')</if>
        </where>
    </select>
    
    <select id="selectInventoryInDetailById" parameterType="Long" resultMap="InventoryInDetailResult">
        <include refid="selectInventoryInDetailVo"/>
        where d.id = #{id}
    </select>
        
    <select id="selectInventoryInDetailByOrderId" parameterType="Long" resultMap="InventoryInDetailResult">
        <include refid="selectInventoryInDetailVo"/>
        where d.order_id = #{orderId}
    </select>
    
    <insert id="insertInventoryInDetail" parameterType="InventoryInDetail" useGeneratedKeys="true" keyProperty="id">
        insert into inventory_in_detail
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="orderId != null">order_id,</if>
            <if test="productId != null">product_id,</if>
            <if test="storeId != null">store_id,</if>
            <if test="counterId != null">counter_id,</if>
            <if test="quantity != null">quantity,</if>
            <if test="purchasePrice != null">purchase_price,</if>
            <if test="batchNumber != null">batch_number,</if>
            <if test="productName != null and productName != ''">product_name,</if>
            <if test="createBy != null and createBy != ''">create_by,</if>
            <if test="remark != null and remark != ''">remark,</if>
            create_time,
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="orderId != null">#{orderId},</if>
            <if test="productId != null">#{productId},</if>
            <if test="storeId != null">#{storeId},</if>
            <if test="counterId != null">#{counterId},</if>
            <if test="quantity != null">#{quantity},</if>
            <if test="purchasePrice != null">#{purchasePrice},</if>
            <if test="batchNumber != null">#{batchNumber},</if>
            <if test="productName != null and productName != ''">#{productName},</if>
            <if test="createBy != null and createBy != ''">#{createBy},</if>
            <if test="remark != null and remark != ''">#{remark},</if>
            sysdate(),
         </trim>
    </insert>
    
    <insert id="batchInsertInventoryInDetail" parameterType="java.util.List">
        insert into inventory_in_detail (order_id, product_id, store_id, counter_id, quantity, purchase_price, batch_number, product_name, create_by, create_time) values
        <foreach item="item" collection="list" separator=",">
            (#{item.orderId}, #{item.productId}, #{item.storeId}, #{item.counterId}, #{item.quantity}, #{item.purchasePrice}, #{item.batchNumber}, #{item.productName}, #{item.createBy}, sysdate())
        </foreach>
    </insert>

    <update id="updateInventoryInDetail" parameterType="InventoryInDetail">
        update inventory_in_detail
        <trim prefix="SET" suffixOverrides=",">
            <if test="orderId != null">order_id = #{orderId},</if>
            <if test="productId != null">product_id = #{productId},</if>
            <if test="storeId != null">store_id = #{storeId},</if>
            <if test="counterId != null">counter_id = #{counterId},</if>
            <if test="quantity != null">quantity = #{quantity},</if>
            <if test="purchasePrice != null">purchase_price = #{purchasePrice},</if>
            <if test="batchNumber != null">batch_number = #{batchNumber},</if>
            <if test="productName != null and productName != ''">product_name = #{productName},</if>
            <if test="updateBy != null and updateBy != ''">update_by = #{updateBy},</if>
            <if test="remark != null">remark = #{remark},</if>
            update_time = sysdate(),
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteInventoryInDetailById" parameterType="Long">
        delete from inventory_in_detail where id = #{id}
    </delete>

    <delete id="deleteInventoryInDetailByIds" parameterType="String">
        delete from inventory_in_detail where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
    
    <delete id="deleteInventoryInDetailByOrderId" parameterType="Long">
        delete from inventory_in_detail where order_id = #{orderId}
    </delete>
</mapper> 