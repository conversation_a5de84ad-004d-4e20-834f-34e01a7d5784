<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.retail.project.retail.mapper.InventoryOutOrderMapper">

    <resultMap type="InventoryOutOrder" id="InventoryOutOrderResult">
        <result property="id"    column="id"    />
        <result property="orderNumber"    column="order_number"    />
        <result property="orderType"    column="order_type"    />
        <result property="memberId"    column="member_id"    />
        <result property="storeId"    column="store_id"    />
        <result property="totalAmount"    column="total_amount"    />
        <result property="payType"    column="pay_type"    />
        <result property="depositType"    column="deposit_type"    />
        <result property="depositAmount"    column="deposit_amount"    />
        <result property="balanceAmount"    column="balance_amount"    />
        <result property="balancePayType"    column="balance_pay_type"    />
        <result property="businessType"    column="business_type"    />
        <result property="actualSaleTime"    column="actual_sale_time"    />
        <result property="processStatus"    column="process_status"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="remark"    column="remark"    />
        <result property="storeName"    column="store_name"    />
        <!-- 会员信息 -->
        <result property="memberName"    column="member_name"    />
        <result property="phoneNumber"    column="phone_number"    />
        <result property="gender"    column="gender"    />
        <result property="memberLevel"    column="member_level"    />
    </resultMap>

    <sql id="selectInventoryOutOrderVo">
        select o.id, o.order_number, o.order_type, o.member_id, o.store_id, o.total_amount,
        o.pay_type, o.deposit_type, o.deposit_amount, o.balance_amount, o.balance_pay_type,
        o.business_type, o.actual_sale_time, o.process_status,
        o.create_by, o.create_time, o.update_by, o.update_time, o.remark,
        s.store_name,
        m.member_name, m.phone_number, m.gender, m.member_level
        from inventory_out_order o
        left join retail_store s on o.store_id = s.id
        left join member m on o.member_id = m.member_id
    </sql>

    <select id="selectInventoryOutOrderList" parameterType="InventoryOutOrder" resultMap="InventoryOutOrderResult">
        <include refid="selectInventoryOutOrderVo"/>
        <where>
            <if test="orderNumber != null  and orderNumber != ''"> and o.order_number like concat('%', #{orderNumber}, '%')</if>
            <if test="memberName != null  and memberName != ''"> and m.member_name like concat('%', #{memberName}, '%')</if>
            <if test="phoneNumber != null  and phoneNumber != ''"> and m.phone_number like concat('%', #{phoneNumber}, '%')</if>
            <if test="gender != null"> and m.gender = #{gender}</if>
            <if test="memberId != null"> and o.member_id = #{memberId}</if>
            <if test="orderType != null"> and o.order_type = #{orderType}</if>
            <if test="storeId != null "> and o.store_id = #{storeId}</if>
            <if test="storeName != null and storeName != ''"> and s.store_name like concat('%', #{storeName}, '%')</if>
            <if test="payType != null"> and o.pay_type = #{payType}</if>
            <if test="businessType != null"> and o.business_type = #{businessType}</if>
            <if test="processStatus != null"> and o.process_status = #{processStatus}</if>
            <!-- 根据货号查询 -->
            <if test="productNumber != null and productNumber != ''">
                and EXISTS (
                    SELECT 1 FROM inventory_out_detail d
                    LEFT JOIN retail_products p ON d.product_id = p.id
                    WHERE d.order_id = o.id
                    AND p.product_number like concat('%', #{productNumber}, '%')
                )
            </if>
            <!-- 支持直接传递businessTypeList参数 -->
            <if test="businessTypeList != null and params.validOrderConditions == null"> and o.business_type in
                <foreach collection="businessTypeList" item="businessType" open="(" separator="," close=")">
                    #{businessType}
                </foreach>
            </if>
            <!-- 支持通过params传递businessTypeList参数 -->
            <if test="params.businessTypeList != null and params.validOrderConditions == null"> and o.business_type in
                <foreach collection="params.businessTypeList" item="businessType" open="(" separator="," close=")">
                    #{businessType}
                </foreach>
            </if>
            <!-- 支持排除特定业务类型 -->
            <if test="excludeBusinessTypes != null and params.validOrderConditions == null"> and o.business_type not in
                <foreach collection="excludeBusinessTypes" item="businessType" open="(" separator="," close=")">
                    #{businessType}
                </foreach>
            </if>
            <if test="params.processStatusList != null and params.validOrderConditions == null"> and o.process_status in
                <foreach collection="params.processStatusList" item="processStatus" open="(" separator="," close=")">
                    #{processStatus}
                </foreach>
            </if>
            <!-- 有效订单条件：申请已审核的售出单、交货已审核的预订单、退货被驳回的售出单和预订单 -->
            <if test="params.validOrderConditions != null and params.validOrderConditions == true">
                and (
                    (o.business_type = 0 and o.process_status = 1) or <!-- 申请已审核的售出单 -->
                    (o.business_type = 4 and o.process_status = 1) or <!-- 交货已审核的预订单 -->
                    (o.business_type = 2 and o.process_status = 2)    <!-- 退货被驳回的售出单和预订单 -->
                )
            </if>
            <if test="params.beginCreateTime != null and params.beginCreateTime != ''"><!-- 开始时间检索 -->
                and date_format(o.create_time,'%Y-%m-%d') &gt;= date_format(#{params.beginCreateTime},'%Y-%m-%d')
            </if>
            <if test="params.endCreateTime != null and params.endCreateTime != ''"><!-- 结束时间检索 -->
                and date_format(o.create_time,'%Y-%m-%d') &lt;= date_format(#{params.endCreateTime},'%Y-%m-%d')
            </if>
            <if test="params.beginActualSaleTime != null and params.beginActualSaleTime != ''"><!-- 开始实际售出时间检索 -->
                and date_format(o.actual_sale_time,'%Y-%m-%d') &gt;= date_format(#{params.beginActualSaleTime},'%Y-%m-%d')
            </if>
            <if test="params.endActualSaleTime != null and params.endActualSaleTime != ''"><!-- 结束实际售出时间检索 -->
                and date_format(o.actual_sale_time,'%Y-%m-%d') &lt;= date_format(#{params.endActualSaleTime},'%Y-%m-%d')
            </if>
            <!-- 数据权限过滤 - 门店权限 -->
            <if test="params.userId != null">
                AND EXISTS (
                    SELECT 1 FROM retail_store rs
                    WHERE rs.id = o.store_id
                    AND FIND_IN_SET(#{params.userId}, rs.user_id)
                )
            </if>
            <!-- 数据范围过滤 -->
            <if test="params != null and params.dataScope != null and params.dataScope != ''">
                ${params.dataScope}
            </if>
            <!-- 跳过数据范围过滤的标记 -->
            <if test="params != null and params.skipDataScope != null and params.skipDataScope == true">
                <!-- 跳过数据范围过滤 -->
            </if>
        </where>
        order by o.create_time desc
    </select>

    <select id="selectInventoryOutOrderById" parameterType="Long" resultMap="InventoryOutOrderResult">
        <include refid="selectInventoryOutOrderVo"/>
        where o.id = #{id}
    </select>

    <insert id="insertInventoryOutOrder" parameterType="InventoryOutOrder" useGeneratedKeys="true" keyProperty="id">
        insert into inventory_out_order
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="orderNumber != null">order_number,</if>
            <if test="orderType != null">order_type,</if>
            <if test="memberId != null">member_id,</if>
            <if test="storeId != null">store_id,</if>
            <if test="totalAmount != null">total_amount,</if>
            <if test="payType != null">pay_type,</if>
            <if test="depositType != null">deposit_type,</if>
            <if test="depositAmount != null">deposit_amount,</if>
            <if test="balanceAmount != null">balance_amount,</if>
            <if test="balancePayType != null">balance_pay_type,</if>
            <if test="businessType != null">business_type,</if>
            <if test="actualSaleTime != null">actual_sale_time,</if>
            <if test="processStatus != null">process_status,</if>
            <if test="createBy != null and createBy != ''">create_by,</if>
            <if test="remark != null and remark != ''">remark,</if>
            create_time,
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="orderNumber != null">#{orderNumber},</if>
            <if test="orderType != null">#{orderType},</if>
            <if test="memberId != null">#{memberId},</if>
            <if test="storeId != null">#{storeId},</if>
            <if test="totalAmount != null">#{totalAmount},</if>
            <if test="payType != null">#{payType},</if>
            <if test="depositType != null">#{depositType},</if>
            <if test="depositAmount != null">#{depositAmount},</if>
            <if test="balanceAmount != null">#{balanceAmount},</if>
            <if test="balancePayType != null">#{balancePayType},</if>
            <if test="businessType != null">#{businessType},</if>
            <if test="actualSaleTime != null">#{actualSaleTime},</if>
            <if test="processStatus != null">#{processStatus},</if>
            <if test="createBy != null and createBy != ''">#{createBy},</if>
            <if test="remark != null and remark != ''">#{remark},</if>
            sysdate(),
         </trim>
    </insert>

    <update id="updateInventoryOutOrder" parameterType="InventoryOutOrder">
        update inventory_out_order
        <trim prefix="SET" suffixOverrides=",">
            <if test="orderNumber != null">order_number = #{orderNumber},</if>
            <if test="orderType != null">order_type = #{orderType},</if>
            <if test="memberId != null">member_id = #{memberId},</if>
            <if test="storeId != null">store_id = #{storeId},</if>
            <if test="totalAmount != null">total_amount = #{totalAmount},</if>
            <if test="payType != null">pay_type = #{payType},</if>
            <if test="depositType != null">deposit_type = #{depositType},</if>
            <if test="depositAmount != null">deposit_amount = #{depositAmount},</if>
            <if test="balanceAmount != null">balance_amount = #{balanceAmount},</if>
            <if test="balancePayType != null">balance_pay_type = #{balancePayType},</if>
            <if test="businessType != null">business_type = #{businessType},</if>
            <if test="actualSaleTime != null">actual_sale_time = #{actualSaleTime},</if>
            <if test="processStatus != null">process_status = #{processStatus},</if>
            <if test="updateBy != null and updateBy != ''">update_by = #{updateBy},</if>
            <if test="remark != null">remark = #{remark},</if>
            update_time = sysdate(),
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteInventoryOutOrderById" parameterType="Long">
        delete from inventory_out_order where id = #{id}
    </delete>

    <delete id="deleteInventoryOutOrderByIds" parameterType="String">
        delete from inventory_out_order where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>
