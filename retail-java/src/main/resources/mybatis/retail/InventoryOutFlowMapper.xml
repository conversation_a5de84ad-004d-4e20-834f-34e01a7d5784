<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.retail.project.retail.mapper.InventoryOutFlowMapper">
    
    <resultMap type="InventoryOutFlow" id="InventoryOutFlowResult">
        <result property="id"    column="id"    />
        <result property="name"    column="name"    />
        <result property="description"    column="description"    />
        <result property="storeId"    column="store_id"    />
        <result property="approverRoles"    column="approver_roles"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="status"    column="status"    />
        <result property="storeName"    column="store_name"    />
    </resultMap>

    <sql id="selectInventoryOutFlowVo">
        select f.id, f.name, f.description, f.store_id, f.approver_roles, f.create_by, f.create_time, f.update_by, f.update_time, f.status,
        s.store_name
        from inventory_out_flow f
        left join retail_store s on f.store_id = s.id
    </sql>

    <select id="selectInventoryOutFlowList" parameterType="InventoryOutFlow" resultMap="InventoryOutFlowResult">
        <include refid="selectInventoryOutFlowVo"/>
        <where>  
            <if test="name != null  and name != ''"> and f.name like concat('%', #{name}, '%')</if>
            <if test="description != null  and description != ''"> and f.description like concat('%', #{description}, '%')</if>
            <if test="storeId != null "> and f.store_id = #{storeId}</if>
            <if test="storeName != null and storeName != ''"> and s.store_name like concat('%', #{storeName}, '%')</if>
            <if test="approverRoles != null  and approverRoles != ''"> and f.approver_roles like concat('%', #{approverRoles}, '%')</if>
            <if test="status != null "> and f.status = #{status}</if>
        </where>
        order by f.create_time desc
    </select>
    
    <select id="selectInventoryOutFlowById" parameterType="Long" resultMap="InventoryOutFlowResult">
        <include refid="selectInventoryOutFlowVo"/>
        where f.id = #{id}
    </select>
        
    <insert id="insertInventoryOutFlow" parameterType="InventoryOutFlow" useGeneratedKeys="true" keyProperty="id">
        insert into inventory_out_flow
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="name != null">name,</if>
            <if test="description != null">description,</if>
            <if test="storeId != null">store_id,</if>
            <if test="approverRoles != null">approver_roles,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="status != null">status,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="name != null">#{name},</if>
            <if test="description != null">#{description},</if>
            <if test="storeId != null">#{storeId},</if>
            <if test="approverRoles != null">#{approverRoles},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="status != null">#{status},</if>
         </trim>
    </insert>

    <update id="updateInventoryOutFlow" parameterType="InventoryOutFlow">
        update inventory_out_flow
        <trim prefix="SET" suffixOverrides=",">
            <if test="name != null">name = #{name},</if>
            <if test="description != null">description = #{description},</if>
            <if test="storeId != null">store_id = #{storeId},</if>
            <if test="approverRoles != null">approver_roles = #{approverRoles},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="status != null">status = #{status},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteInventoryOutFlowById" parameterType="Long">
        delete from inventory_out_flow where id = #{id}
    </delete>

    <delete id="deleteInventoryOutFlowByIds" parameterType="String">
        delete from inventory_out_flow where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper> 