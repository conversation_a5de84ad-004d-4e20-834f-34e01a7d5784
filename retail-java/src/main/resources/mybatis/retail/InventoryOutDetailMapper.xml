<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.retail.project.retail.mapper.InventoryOutDetailMapper">

    <resultMap type="InventoryOutDetail" id="InventoryOutDetailResult">
        <result property="id"    column="id"    />
        <result property="orderId"    column="order_id"    />
        <result property="productId"    column="product_id"    />
        <result property="counterId"    column="counter_id"    />
        <result property="channelId"    column="channel_id"    />
        <result property="quantity"    column="quantity"    />
        <result property="salePrice"    column="sale_price"    />
        <result property="batchNumber"    column="batch_number"    />
        <result property="productName"    column="product_name"    />
        <result property="productNumber"    column="product_number"    />
        <result property="productImage"    column="product_image"    />
        <result property="channelName"    column="channel_name"    />
        <result property="counterName"    column="counter_name"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="remark"    column="remark"    />
    </resultMap>

    <sql id="selectInventoryOutDetailVo">
        select d.id, d.order_id, d.product_id, d.counter_id, d.channel_id, d.quantity, d.sale_price, d.batch_number, d.remark,
        p.product_number, p.product_name, p.product_image, ch.channel_name, co.counter_name, d.create_by, d.create_time, d.update_by, d.update_time
        from inventory_out_detail d
        left join retail_products p on d.product_id = p.id
        left join retail_channel ch on d.channel_id = ch.id
        left join retail_counter co on d.counter_id = co.id
    </sql>

    <select id="selectInventoryOutDetailList" parameterType="InventoryOutDetail" resultMap="InventoryOutDetailResult">
        <include refid="selectInventoryOutDetailVo"/>
        <where>
            <if test="orderId != null "> and d.order_id = #{orderId}</if>
            <if test="productId != null "> and d.product_id = #{productId}</if>
            <if test="counterId != null "> and d.counter_id = #{counterId}</if>
            <if test="channelId != null "> and d.channel_id = #{channelId}</if>
            <if test="productNumber != null  and productNumber != ''"> and p.product_number like concat('%', #{productNumber}, '%')</if>
            <if test="channelName != null  and channelName != ''"> and ch.channel_name like concat('%', #{channelName}, '%')</if>
            <if test="counterName != null  and counterName != ''"> and co.counter_name like concat('%', #{counterName}, '%')</if>
        </where>
    </select>

    <select id="selectInventoryOutDetailById" parameterType="Long" resultMap="InventoryOutDetailResult">
        <include refid="selectInventoryOutDetailVo"/>
        where d.id = #{id}
    </select>

    <select id="selectInventoryOutDetailByOrderId" parameterType="Long" resultMap="InventoryOutDetailResult">
        <include refid="selectInventoryOutDetailVo"/>
        where d.order_id = #{orderId}
    </select>

    <insert id="insertInventoryOutDetail" parameterType="InventoryOutDetail" useGeneratedKeys="true" keyProperty="id">
        insert into inventory_out_detail
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="orderId != null">order_id,</if>
            <if test="productId != null">product_id,</if>
            <if test="counterId != null">counter_id,</if>
            <if test="channelId != null">channel_id,</if>
            <if test="quantity != null">quantity,</if>
            <if test="salePrice != null">sale_price,</if>
            <if test="batchNumber != null">batch_number,</if>
            <if test="productName != null and productName != ''">product_name,</if>
            <if test="createBy != null and createBy != ''">create_by,</if>
            <if test="remark != null and remark != ''">remark,</if>
            create_time,
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="orderId != null">#{orderId},</if>
            <if test="productId != null">#{productId},</if>
            <if test="counterId != null">#{counterId},</if>
            <if test="channelId != null">#{channelId},</if>
            <if test="quantity != null">#{quantity},</if>
            <if test="salePrice != null">#{salePrice},</if>
            <if test="batchNumber != null">#{batchNumber},</if>
            <if test="productName != null and productName != ''">#{productName},</if>
            <if test="createBy != null and createBy != ''">#{createBy},</if>
            <if test="remark != null and remark != ''">#{remark},</if>
            sysdate(),
         </trim>
    </insert>

    <insert id="batchInsertInventoryOutDetail" parameterType="java.util.List">
        insert into inventory_out_detail (order_id, product_id, counter_id, channel_id, quantity, sale_price, batch_number, product_name, remark, create_by, create_time) values
        <foreach item="item" collection="list" separator=",">
            (#{item.orderId}, #{item.productId}, #{item.counterId}, #{item.channelId}, #{item.quantity}, #{item.salePrice}, #{item.batchNumber}, #{item.productName}, #{item.remark}, #{item.createBy}, sysdate())
        </foreach>
    </insert>

    <update id="updateInventoryOutDetail" parameterType="InventoryOutDetail">
        update inventory_out_detail
        <trim prefix="SET" suffixOverrides=",">
            <if test="orderId != null">order_id = #{orderId},</if>
            <if test="productId != null">product_id = #{productId},</if>
            <if test="counterId != null">counter_id = #{counterId},</if>
            <if test="channelId != null">channel_id = #{channelId},</if>
            <if test="quantity != null">quantity = #{quantity},</if>
            <if test="salePrice != null">sale_price = #{salePrice},</if>
            <if test="batchNumber != null">batch_number = #{batchNumber},</if>
            <if test="productName != null and productName != ''">product_name = #{productName},</if>
            <if test="updateBy != null and updateBy != ''">update_by = #{updateBy},</if>
            <if test="remark != null">remark = #{remark},</if>
            update_time = sysdate(),
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteInventoryOutDetailById" parameterType="Long">
        delete from inventory_out_detail where id = #{id}
    </delete>

    <delete id="deleteInventoryOutDetailByIds" parameterType="String">
        delete from inventory_out_detail where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <delete id="deleteInventoryOutDetailByOrderId" parameterType="Long">
        delete from inventory_out_detail where order_id = #{orderId}
    </delete>

    <!-- 查询按时间维度分组的销售数量统计 -->
    <select id="selectTimeSalesQuantity" resultType="java.util.Map">
        SELECT
        <choose>
            <when test="timeUnit == 'day'">
                DATE_FORMAT(COALESCE(o.actual_sale_time, o.create_time), '%Y-%m-%d') AS timeLabel,
                DATE_FORMAT(COALESCE(o.actual_sale_time, o.create_time), '%Y-%m-%d') AS timeValue
            </when>
            <when test="timeUnit == 'month'">
                DATE_FORMAT(COALESCE(o.actual_sale_time, o.create_time), '%Y-%m') AS timeLabel,
                DATE_FORMAT(COALESCE(o.actual_sale_time, o.create_time), '%Y-%m') AS timeValue
            </when>
            <when test="timeUnit == 'year'">
                DATE_FORMAT(COALESCE(o.actual_sale_time, o.create_time), '%Y') AS timeLabel,
                DATE_FORMAT(COALESCE(o.actual_sale_time, o.create_time), '%Y') AS timeValue
            </when>
            <otherwise>
                DATE_FORMAT(COALESCE(o.actual_sale_time, o.create_time), '%Y-%m') AS timeLabel,
                DATE_FORMAT(COALESCE(o.actual_sale_time, o.create_time), '%Y-%m') AS timeValue
            </otherwise>
        </choose>
        , SUM(d.quantity) AS totalQuantity
        FROM inventory_out_detail d
        INNER JOIN inventory_out_order o ON d.order_id = o.id
        WHERE (
            (o.business_type = 0 AND o.process_status = 1) OR <!-- 申请已审核的售出单 -->
            (o.business_type = 4 AND o.process_status = 1) OR <!-- 交货已审核的预订单 -->
            (o.business_type = 2 AND o.process_status = 2)    <!-- 退货被驳回的售出单和预订单 -->
        )
        <if test="channelId != null">
            AND d.channel_id = #{channelId}
        </if>
        <choose>
            <when test="timeUnit == 'day'">
                AND COALESCE(o.actual_sale_time, o.create_time) >= DATE_SUB(CURDATE(), INTERVAL 30 DAY)
                GROUP BY DATE_FORMAT(COALESCE(o.actual_sale_time, o.create_time), '%Y-%m-%d')
            </when>
            <when test="timeUnit == 'month'">
                AND COALESCE(o.actual_sale_time, o.create_time) >= DATE_SUB(CURDATE(), INTERVAL 12 MONTH)
                GROUP BY DATE_FORMAT(COALESCE(o.actual_sale_time, o.create_time), '%Y-%m')
            </when>
            <when test="timeUnit == 'year'">
                AND COALESCE(o.actual_sale_time, o.create_time) >= DATE_SUB(CURDATE(), INTERVAL 5 YEAR)
                GROUP BY DATE_FORMAT(COALESCE(o.actual_sale_time, o.create_time), '%Y')
            </when>
            <otherwise>
                AND COALESCE(o.actual_sale_time, o.create_time) >= DATE_SUB(CURDATE(), INTERVAL 12 MONTH)
                GROUP BY DATE_FORMAT(COALESCE(o.actual_sale_time, o.create_time), '%Y-%m')
            </otherwise>
        </choose>
        ORDER BY timeValue
    </select>

    <!-- 查询按月份分组的销售数量统计（保留向后兼容） -->
    <select id="selectMonthlySalesQuantity" resultType="java.util.Map">
        SELECT DATE_FORMAT(COALESCE(o.actual_sale_time, o.create_time), '%Y-%m') AS month, SUM(d.quantity) AS totalQuantity
        FROM inventory_out_detail d
        INNER JOIN inventory_out_order o ON d.order_id = o.id
        WHERE (
            (o.business_type = 0 AND o.process_status = 1) OR <!-- 申请已审核的售出单 -->
            (o.business_type = 4 AND o.process_status = 1) OR <!-- 交货已审核的预订单 -->
            (o.business_type = 2 AND o.process_status = 2)    <!-- 退货被驳回的售出单和预订单 -->
        )
        <if test="channelId != null">
            AND d.channel_id = #{channelId}
        </if>
        GROUP BY DATE_FORMAT(COALESCE(o.actual_sale_time, o.create_time), '%Y-%m')
        ORDER BY month
    </select>

    <!-- 查询按商品分组的销售数量统计 -->
    <select id="selectProductSalesQuantity" resultType="java.util.Map">
        SELECT d.product_id AS productId, p.product_name AS productName, SUM(d.quantity) AS totalQuantity
        FROM inventory_out_detail d
        INNER JOIN inventory_out_order o ON d.order_id = o.id
        LEFT JOIN retail_products p ON d.product_id = p.id
        WHERE (
            (o.business_type = 0 AND o.process_status = 1) OR <!-- 申请已审核的售出单 -->
            (o.business_type = 4 AND o.process_status = 1) OR <!-- 交货已审核的预订单 -->
            (o.business_type = 2 AND o.process_status = 2)    <!-- 退货被驳回的售出单和预订单 -->
        )
        <if test="channelId != null">
            AND d.channel_id = #{channelId}
        </if>
        GROUP BY d.product_id, p.product_name
        ORDER BY totalQuantity DESC
    </select>

    <!-- 查询渠道产品销售明细 -->
    <select id="selectChannelProductSalesDetails" resultType="java.util.Map">
        SELECT
            d.id AS detailId,
            d.product_id AS productId,
            p.product_name AS productName,
            p.product_number AS productNumber,
            p.product_image AS productImage,
            p.brand AS brand,
            d.channel_id AS channelId,
            ch.channel_name AS channelName,
            o.store_id AS storeId,
            s.store_name AS storeName,
            d.quantity AS totalQuantity,
            d.sale_price * d.quantity AS totalAmount,
            COALESCE(o.actual_sale_time, o.create_time) AS saleTime,
            o.order_number AS orderNumber
        FROM inventory_out_detail d
        INNER JOIN inventory_out_order o ON d.order_id = o.id
        LEFT JOIN retail_products p ON d.product_id = p.id
        LEFT JOIN retail_channel ch ON d.channel_id = ch.id
        LEFT JOIN retail_store s ON o.store_id = s.id
        WHERE 1=1
        AND (
            (o.business_type = 0 AND o.process_status = 1) OR
            (o.business_type = 4 AND o.process_status = 1) OR
            (o.business_type = 2 AND o.process_status = 2)
        )
        <if test="channelId != null">
            AND d.channel_id = #{channelId}
        </if>
        <if test="storeId != null">
            AND o.store_id = #{storeId}
        </if>
        <if test="beginTime != null and beginTime != ''">
            AND DATE(COALESCE(o.actual_sale_time, o.create_time)) &gt;= #{beginTime}
        </if>
        <if test="endTime != null and endTime != ''">
            AND DATE(COALESCE(o.actual_sale_time, o.create_time)) &lt;= #{endTime}
        </if>
        ORDER BY COALESCE(o.actual_sale_time, o.create_time) DESC, d.id DESC
    </select>

    <!-- 查询按商品分组的销售数量统计（支持筛选条件） -->
    <select id="selectProductSalesQuantityWithFilter" resultType="java.util.Map">
        SELECT
            d.product_id AS productId,
            p.product_name AS productName,
            p.product_number AS productNumber,
            p.product_image AS productImage,
            SUM(d.quantity) AS totalQuantity
        FROM inventory_out_detail d
        INNER JOIN inventory_out_order o ON d.order_id = o.id
        LEFT JOIN retail_products p ON d.product_id = p.id
        WHERE 1=1
        AND (
            (o.business_type = 0 AND o.process_status = 1) OR
            (o.business_type = 4 AND o.process_status = 1) OR
            (o.business_type = 2 AND o.process_status = 2)
        )
        <if test="channelId != null">
            AND d.channel_id = #{channelId}
        </if>
        <if test="storeId != null">
            AND o.store_id = #{storeId}
        </if>
        <if test="beginTime != null and beginTime != ''">
            AND DATE(COALESCE(o.actual_sale_time, o.create_time)) &gt;= #{beginTime}
        </if>
        <if test="endTime != null and endTime != ''">
            AND DATE(COALESCE(o.actual_sale_time, o.create_time)) &lt;= #{endTime}
        </if>
        GROUP BY d.product_id, p.product_name, p.product_number, p.product_image
        ORDER BY totalQuantity DESC
    </select>

    <!-- 查询按时间维度分组的销售数量统计（支持筛选条件） -->
    <select id="selectTimeSalesQuantityWithFilter" resultType="java.util.Map">
        SELECT
            <choose>
                <when test="timeUnit == 'day'">
                    DATE_FORMAT(COALESCE(o.actual_sale_time, o.create_time), '%Y-%m-%d') AS timeLabel
                </when>
                <when test="timeUnit == 'year'">
                    DATE_FORMAT(COALESCE(o.actual_sale_time, o.create_time), '%Y') AS timeLabel
                </when>
                <otherwise>
                    DATE_FORMAT(COALESCE(o.actual_sale_time, o.create_time), '%Y-%m') AS timeLabel
                </otherwise>
            </choose>,
            SUM(d.quantity) AS totalQuantity
        FROM inventory_out_detail d
        INNER JOIN inventory_out_order o ON d.order_id = o.id
        WHERE 1=1
        AND (
            (o.business_type = 0 AND o.process_status = 1) OR
            (o.business_type = 4 AND o.process_status = 1) OR
            (o.business_type = 2 AND o.process_status = 2)
        )
        <if test="channelId != null">
            AND d.channel_id = #{channelId}
        </if>
        <if test="storeId != null">
            AND o.store_id = #{storeId}
        </if>
        <if test="beginTime != null and beginTime != ''">
            AND DATE(COALESCE(o.actual_sale_time, o.create_time)) &gt;= #{beginTime}
        </if>
        <if test="endTime != null and endTime != ''">
            AND DATE(COALESCE(o.actual_sale_time, o.create_time)) &lt;= #{endTime}
        </if>
        GROUP BY timeLabel
        ORDER BY timeLabel
    </select>

    <!-- 销售数据统计查询（优化版本） -->
    <select id="selectSaleStatistics" resultType="java.util.Map">
        SELECT
            COUNT(DISTINCT o.id) AS totalOrders,
            COALESCE(SUM(d.sale_price * d.quantity), 0) AS totalSales,
            COALESCE(SUM(d.quantity), 0) AS totalQuantity,
            COALESCE(SUM(d.sale_price * d.quantity) - SUM(COALESCE(
                (SELECT COALESCE(
                    (SELECT ist.purchase_price FROM inventory_stock ist WHERE ist.batch_number = d.batch_number LIMIT 1),
                    (SELECT iid.purchase_price FROM inventory_in_detail iid WHERE iid.batch_number = d.batch_number LIMIT 1)
                )),
                0) * d.quantity), 0) AS totalProfit
        FROM inventory_out_detail d
        INNER JOIN inventory_out_order o ON d.order_id = o.id
        WHERE 1=1
        AND (
            (o.business_type = 0 AND o.process_status = 1) OR
            (o.business_type = 4 AND o.process_status = 1) OR
            (o.business_type = 2 AND o.process_status = 2)
        )
        <if test="storeId != null">
            AND o.store_id = #{storeId}
        </if>
        <if test="channelId != null">
            AND d.channel_id = #{channelId}
        </if>
        <if test="beginActualSaleTime != null and beginActualSaleTime != ''">
            AND COALESCE(o.actual_sale_time, o.create_time) &gt;= #{beginActualSaleTime}
        </if>
        <if test="endActualSaleTime != null and endActualSaleTime != ''">
            AND COALESCE(o.actual_sale_time, o.create_time) &lt;= #{endActualSaleTime}
        </if>
    </select>

    <!-- 销售趋势数据查询（优化版本） -->
    <select id="selectSaleTrendData" resultType="java.util.Map">
        SELECT
            <choose>
                <when test="timeUnit == 'day'">
                    DATE_FORMAT(COALESCE(o.actual_sale_time, o.create_time), '%Y-%m-%d') AS timeLabel
                </when>
                <when test="timeUnit == 'year'">
                    DATE_FORMAT(COALESCE(o.actual_sale_time, o.create_time), '%Y') AS timeLabel
                </when>
                <otherwise>
                    DATE_FORMAT(COALESCE(o.actual_sale_time, o.create_time), '%Y-%m') AS timeLabel
                </otherwise>
            </choose>,
            COUNT(DISTINCT o.id) AS orderCount,
            COALESCE(SUM(d.sale_price * d.quantity), 0) AS salesAmount,
            COALESCE(SUM(d.quantity), 0) AS totalQuantity
        FROM inventory_out_detail d
        INNER JOIN inventory_out_order o ON d.order_id = o.id
        WHERE 1=1
        AND (
            (o.business_type = 0 AND o.process_status = 1) OR
            (o.business_type = 4 AND o.process_status = 1) OR
            (o.business_type = 2 AND o.process_status = 2)
        )
        <if test="storeId != null">
            AND o.store_id = #{storeId}
        </if>
        <if test="channelId != null">
            AND d.channel_id = #{channelId}
        </if>
        <if test="beginActualSaleTime != null and beginActualSaleTime != ''">
            AND COALESCE(o.actual_sale_time, o.create_time) &gt;= #{beginActualSaleTime}
        </if>
        <if test="endActualSaleTime != null and endActualSaleTime != ''">
            AND COALESCE(o.actual_sale_time, o.create_time) &lt;= #{endActualSaleTime}
        </if>
        GROUP BY timeLabel
        ORDER BY timeLabel
    </select>

    <!-- 渠道销售分布查询（优化版本） -->
    <select id="selectChannelSalesDistribution" resultType="java.util.Map">
        SELECT
            d.channel_id AS channelId,
            ch.channel_name AS channelName,
            COALESCE(SUM(d.sale_price * d.quantity), 0) AS salesAmount
        FROM inventory_out_detail d
        INNER JOIN inventory_out_order o ON d.order_id = o.id
        LEFT JOIN retail_channel ch ON d.channel_id = ch.id
        WHERE 1=1
        AND (
            (o.business_type = 0 AND o.process_status = 1) OR
            (o.business_type = 4 AND o.process_status = 1) OR
            (o.business_type = 2 AND o.process_status = 2)
        )
        <if test="storeId != null">
            AND o.store_id = #{storeId}
        </if>
        <if test="channelId != null">
            AND d.channel_id = #{channelId}
        </if>
        <if test="beginActualSaleTime != null and beginActualSaleTime != ''">
            AND COALESCE(o.actual_sale_time, o.create_time) &gt;= #{beginActualSaleTime}
        </if>
        <if test="endActualSaleTime != null and endActualSaleTime != ''">
            AND COALESCE(o.actual_sale_time, o.create_time) &lt;= #{endActualSaleTime}
        </if>
        GROUP BY d.channel_id, ch.channel_name
        HAVING salesAmount > 0
        ORDER BY salesAmount DESC
    </select>

    <!-- 门店销售排行查询（优化版本） -->
    <select id="selectStoreSalesRanking" resultType="java.util.Map">
        SELECT
            o.store_id AS storeId,
            s.store_name AS storeName,
            COUNT(DISTINCT o.id) AS orderCount,
            COALESCE(SUM(d.sale_price * d.quantity), 0) AS salesAmount
        FROM inventory_out_detail d
        INNER JOIN inventory_out_order o ON d.order_id = o.id
        LEFT JOIN retail_store s ON o.store_id = s.id
        WHERE 1=1
        AND (
            (o.business_type = 0 AND o.process_status = 1) OR
            (o.business_type = 4 AND o.process_status = 1) OR
            (o.business_type = 2 AND o.process_status = 2)
        )
        <if test="storeId != null">
            AND o.store_id = #{storeId}
        </if>
        <if test="channelId != null">
            AND d.channel_id = #{channelId}
        </if>
        <if test="beginActualSaleTime != null and beginActualSaleTime != ''">
            AND COALESCE(o.actual_sale_time, o.create_time) &gt;= #{beginActualSaleTime}
        </if>
        <if test="endActualSaleTime != null and endActualSaleTime != ''">
            AND COALESCE(o.actual_sale_time, o.create_time) &lt;= #{endActualSaleTime}
        </if>
        GROUP BY o.store_id, s.store_name
        HAVING salesAmount > 0
        ORDER BY salesAmount DESC
        LIMIT 5
    </select>

    <!-- 商品销售排行查询（优化版本） -->
    <select id="selectProductSalesRanking" resultType="java.util.Map">
        SELECT
            d.product_id AS productId,
            d.product_name AS productName,
            p.product_number AS productNumber,
            p.product_image AS productImage,
            COALESCE(SUM(d.sale_price * d.quantity), 0) AS salesAmount,
            COALESCE(SUM(d.quantity), 0) AS totalQuantity
        FROM inventory_out_detail d
        INNER JOIN inventory_out_order o ON d.order_id = o.id
        LEFT JOIN retail_products p ON d.product_id = p.id
        WHERE 1=1
        AND (
            (o.business_type = 0 AND o.process_status = 1) OR
            (o.business_type = 4 AND o.process_status = 1) OR
            (o.business_type = 2 AND o.process_status = 2)
        )
        <if test="storeId != null">
            AND o.store_id = #{storeId}
        </if>
        <if test="channelId != null">
            AND d.channel_id = #{channelId}
        </if>
        <if test="beginActualSaleTime != null and beginActualSaleTime != ''">
            AND COALESCE(o.actual_sale_time, o.create_time) &gt;= #{beginActualSaleTime}
        </if>
        <if test="endActualSaleTime != null and endActualSaleTime != ''">
            AND COALESCE(o.actual_sale_time, o.create_time) &lt;= #{endActualSaleTime}
        </if>
        GROUP BY d.product_id, d.product_name, p.product_number, p.product_image
        HAVING salesAmount > 0
        ORDER BY salesAmount DESC
        LIMIT 5
    </select>

    <!-- 客户性别分布查询（优化版本） -->
    <select id="selectCustomerGenderDistribution" resultType="java.util.Map">
        SELECT
            m.gender AS gender,
            COUNT(DISTINCT o.id) AS orderCount
        FROM inventory_out_detail d
        INNER JOIN inventory_out_order o ON d.order_id = o.id
        LEFT JOIN member m ON o.member_id = m.member_id
        WHERE 1=1
        AND (
            (o.business_type = 0 AND o.process_status = 1) OR
            (o.business_type = 4 AND o.process_status = 1) OR
            (o.business_type = 2 AND o.process_status = 2)
        )
        AND m.gender IS NOT NULL
        <if test="storeId != null">
            AND o.store_id = #{storeId}
        </if>
        <if test="channelId != null">
            AND d.channel_id = #{channelId}
        </if>
        <if test="beginActualSaleTime != null and beginActualSaleTime != ''">
            AND COALESCE(o.actual_sale_time, o.create_time) &gt;= #{beginActualSaleTime}
        </if>
        <if test="endActualSaleTime != null and endActualSaleTime != ''">
            AND COALESCE(o.actual_sale_time, o.create_time) &lt;= #{endActualSaleTime}
        </if>
        GROUP BY m.gender
        ORDER BY orderCount DESC
    </select>

    <!-- 利润分析数据查询（优化版本） -->
    <select id="selectProfitAnalysisData" resultType="java.util.Map">
        SELECT
            <choose>
                <when test="timeUnit == 'day'">
                    DATE_FORMAT(COALESCE(o.actual_sale_time, o.create_time), '%Y-%m-%d') AS timeLabel
                </when>
                <when test="timeUnit == 'year'">
                    DATE_FORMAT(COALESCE(o.actual_sale_time, o.create_time), '%Y') AS timeLabel
                </when>
                <otherwise>
                    DATE_FORMAT(COALESCE(o.actual_sale_time, o.create_time), '%Y-%m') AS timeLabel
                </otherwise>
            </choose>,
            COALESCE(SUM(d.sale_price * d.quantity), 0) AS salesAmount,
            COALESCE(SUM(d.sale_price * d.quantity) - SUM(COALESCE(
                (SELECT COALESCE(
                    (SELECT ist.purchase_price FROM inventory_stock ist WHERE ist.batch_number = d.batch_number LIMIT 1),
                    (SELECT iid.purchase_price FROM inventory_in_detail iid WHERE iid.batch_number = d.batch_number LIMIT 1)
                )),
                0) * d.quantity), 0) AS profitAmount
        FROM inventory_out_detail d
        INNER JOIN inventory_out_order o ON d.order_id = o.id
        WHERE 1=1
        AND (
            (o.business_type = 0 AND o.process_status = 1) OR
            (o.business_type = 4 AND o.process_status = 1) OR
            (o.business_type = 2 AND o.process_status = 2)
        )
        <if test="storeId != null">
            AND o.store_id = #{storeId}
        </if>
        <if test="channelId != null">
            AND d.channel_id = #{channelId}
        </if>
        <if test="beginActualSaleTime != null and beginActualSaleTime != ''">
            AND COALESCE(o.actual_sale_time, o.create_time) &gt;= #{beginActualSaleTime}
        </if>
        <if test="endActualSaleTime != null and endActualSaleTime != ''">
            AND COALESCE(o.actual_sale_time, o.create_time) &lt;= #{endActualSaleTime}
        </if>
        GROUP BY timeLabel
        ORDER BY timeLabel
    </select>
</mapper>