<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.retail.project.retail.mapper.InventoryStockMapper">

    <resultMap type="InventoryStock" id="InventoryStockResult">
        <result property="id"    column="id"    />
        <result property="productId"    column="product_id"    />
        <result property="channelId"    column="channel_id"    />
        <result property="storeId"    column="store_id"    />
        <result property="counterId"    column="counter_id"    />
        <result property="batchNumber"    column="batch_number"    />
        <result property="purchasePrice"    column="purchase_price"    />
        <result property="quantity"    column="quantity"    />
        <result property="createTime"    column="create_time"    />
        <result property="createBy"    column="create_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="productName"    column="product_name"    />
        <result property="productNumber"    column="product_number"    />
        <result property="productImage"    column="product_image"    />
        <result property="channelName"    column="channel_name"    />
        <result property="storeName"    column="store_name"    />
        <result property="counterName"    column="counter_name"    />
        <result property="isFutures"    column="is_futures"    />
        <result property="brand"    column="brand"    />
        <result property="category"    column="category"    />
        <result property="specification"    column="specification"    />
        <result property="color"    column="color"    />
        <result property="material"    column="material"    />
        <result property="origin"    column="origin"    />
        <result property="standard"    column="standard"    />
        <result property="safetyCategory"    column="safety_category"    />
        <result property="productGrade"    column="product_grade"    />
        <result property="referencePrice"    column="reference_price"    />
        <result property="retailPrice"    column="retail_price"    />
    </resultMap>

    <sql id="selectInventoryStockVo">
        select s.id, s.product_id, s.channel_id, s.store_id, s.counter_id, s.batch_number, s.purchase_price, s.quantity, s.create_time, s.create_by, s.update_time, s.update_by, s.is_futures,
        p.product_number, p.product_name, p.product_image, p.brand, p.category, p.specification, p.color, p.material, p.origin, p.standard, p.safety_category, p.product_grade, p.reference_price, p.retail_price,
        ch.channel_name, st.store_name, co.counter_name
        from inventory_stock s
        left join retail_products p on s.product_id = p.id
        left join retail_channel ch on s.channel_id = ch.id
        left join retail_store st on s.store_id = st.id
        left join retail_counter co on s.counter_id = co.id
    </sql>

    <select id="selectInventoryStockList" parameterType="InventoryStock" resultMap="InventoryStockResult">
        <include refid="selectInventoryStockVo"/>
        <where>
            <if test="productId != null "> and s.product_id = #{productId}</if>
            <if test="channelId != null "> and s.channel_id = #{channelId}</if>
            <if test="storeId != null "> and s.store_id = #{storeId}</if>
            <if test="counterId != null "> and s.counter_id = #{counterId}</if>
            <if test="batchNumber != null  and batchNumber != ''"> and s.batch_number = #{batchNumber}</if>
            <if test="productNumber != null  and productNumber != ''"> and p.product_number like concat('%', #{productNumber}, '%')</if>
            <if test="channelName != null  and channelName != ''"> and ch.channel_name like concat('%', #{channelName}, '%')</if>
            <if test="storeName != null  and storeName != ''"> and st.store_name like concat('%', #{storeName}, '%')</if>
            <if test="counterName != null  and counterName != ''"> and co.counter_name like concat('%', #{counterName}, '%')</if>
            <if test="brand != null and brand != ''"> and p.brand like concat('%', #{brand}, '%')</if>
            <if test="category != null and category != ''"> and p.category like concat('%', #{category}, '%')</if>
            <if test="params.beginCreateTime != null and params.beginCreateTime != ''"><!-- 开始时间检索 -->
                and date_format(s.create_time,'%y%m%d') &gt;= date_format(#{params.beginCreateTime},'%y%m%d')
            </if>
            <if test="params.endCreateTime != null and params.endCreateTime != ''"><!-- 结束时间检索 -->
                and date_format(s.create_time,'%y%m%d') &lt;= date_format(#{params.endCreateTime},'%y%m%d')
            </if>
            <if test="minQuantity != null"><!-- 最小数量检索 -->
                and s.quantity &gt;= #{minQuantity}
            </if>
            <if test="maxCreateTime != null and maxCreateTime != ''"><!-- 最大创建时间检索 -->
                and date_format(s.create_time,'%Y-%m-%d') &lt;= #{maxCreateTime}
            </if>
            <!-- 数据权限过滤 - 门店权限 -->
            <if test="params.userId != null and params.isAdmin != true">
                AND EXISTS (
                    SELECT 1 FROM retail_store rs
                    WHERE rs.id = s.store_id
                    AND FIND_IN_SET(#{params.userId}, rs.user_id)
                )
            </if>
            <!-- 数据范围过滤 -->
            <if test="params.isSkipDataScope != true">
                ${params.dataScope}
            </if>
        </where>
    </select>

    <select id="selectInventoryStockById" parameterType="Long" resultMap="InventoryStockResult">
        <include refid="selectInventoryStockVo"/>
        where s.id = #{id}
    </select>

    <select id="selectInventoryStockByLocation" resultMap="InventoryStockResult">
        <include refid="selectInventoryStockVo"/>
        <where>
            and s.product_id = #{productId}
            <if test="storeId != null"> and s.store_id = #{storeId}</if>
            <if test="counterId != null"> and s.counter_id = #{counterId}</if>
            <if test="channelId != null"> and s.channel_id = #{channelId}</if>
            and s.quantity > 0
        </where>
        order by s.create_time asc
    </select>

    <insert id="insertInventoryStock" parameterType="InventoryStock" useGeneratedKeys="true" keyProperty="id">
        insert into inventory_stock
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="productId != null">product_id,</if>
            <if test="channelId != null">channel_id,</if>
            <if test="storeId != null">store_id,</if>
            <if test="counterId != null">counter_id,</if>
            <if test="batchNumber != null">batch_number,</if>
            <if test="purchasePrice != null">purchase_price,</if>
            <if test="quantity != null">quantity,</if>
            <if test="createBy != null and createBy != ''">create_by,</if>
            <if test="isFutures != null">is_futures,</if>
            create_time,
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="productId != null">#{productId},</if>
            <if test="channelId != null">#{channelId},</if>
            <if test="storeId != null">#{storeId},</if>
            <if test="counterId != null">#{counterId},</if>
            <if test="batchNumber != null">#{batchNumber},</if>
            <if test="purchasePrice != null">#{purchasePrice},</if>
            <if test="quantity != null">#{quantity},</if>
            <if test="createBy != null and createBy != ''">#{createBy},</if>
            <if test="isFutures != null">#{isFutures},</if>
            sysdate(),
         </trim>
    </insert>

    <update id="updateInventoryStock" parameterType="InventoryStock">
        update inventory_stock
        <trim prefix="SET" suffixOverrides=",">
            <if test="productId != null">product_id = #{productId},</if>
            <if test="channelId != null">channel_id = #{channelId},</if>
            <if test="storeId != null">store_id = #{storeId},</if>
            <if test="counterId != null">counter_id = #{counterId},</if>
            <if test="batchNumber != null">batch_number = #{batchNumber},</if>
            <if test="purchasePrice != null">purchase_price = #{purchasePrice},</if>
            <if test="quantity != null">quantity = #{quantity},</if>
            <if test="updateBy != null and updateBy != ''">update_by = #{updateBy},</if>
            <if test="isFutures != null">is_futures = #{isFutures},</if>
            update_time = sysdate(),
        </trim>
        where id = #{id}
    </update>

    <!-- 专门用于更新期货状态的方法 -->
    <update id="updateInventoryStockFuturesStatus">
        update inventory_stock
        set is_futures = #{isFutures}
        where batch_number = #{batchNumber}
    </update>

    <delete id="deleteInventoryStockById" parameterType="Long">
        delete from inventory_stock where id = #{id}
    </delete>

    <delete id="deleteInventoryStockByIds" parameterType="String">
        delete from inventory_stock where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <select id="selectStockByProductIdOrderByTime" parameterType="InventoryStock" resultMap="InventoryStockResult">
        <include refid="selectInventoryStockVo"/>
        <where>
            <if test="productId != null"> and s.product_id = #{productId}</if>
            <if test="storeId != null"> and s.store_id = #{storeId}</if>
            <if test="counterId != null"> and s.counter_id = #{counterId}</if>
            <if test="channelId != null"> and s.channel_id = #{channelId}</if>
            <if test="isFutures != null"> and s.is_futures = #{isFutures}</if>
            and s.quantity > 0
        </where>
        order by s.create_time asc
    </select>
</mapper>