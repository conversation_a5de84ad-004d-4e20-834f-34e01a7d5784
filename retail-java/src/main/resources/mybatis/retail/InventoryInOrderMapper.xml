<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.retail.project.retail.mapper.InventoryInOrderMapper">
    
    <resultMap type="InventoryInOrder" id="InventoryInOrderResult">
        <result property="id"    column="id"    />
        <result property="orderNumber"    column="order_number"    />
        <result property="channelId"    column="channel_id"    />
        <result property="isFutures"    column="is_futures"    />
        <result property="futuresLogistics"    column="futures_logistics"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="remark"    column="remark"    />
        <result property="channelName"    column="channel_name"    />
    </resultMap>

    <sql id="selectInventoryInOrderVo">
        select o.id, o.order_number, o.channel_id, o.is_futures, o.futures_logistics,
        o.create_by, o.create_time, o.update_by, o.update_time, o.remark,
        c.channel_name
        from inventory_in_order o
        left join retail_channel c on o.channel_id = c.id
    </sql>

    <select id="selectInventoryInOrderList" parameterType="InventoryInOrder" resultMap="InventoryInOrderResult">
        <include refid="selectInventoryInOrderVo"/>
        <where>  
            <if test="orderNumber != null  and orderNumber != ''"> and o.order_number like concat('%', #{orderNumber}, '%')</if>
            <if test="channelId != null "> and o.channel_id = #{channelId}</if>
            <if test="isFutures != null "> and o.is_futures = #{isFutures}</if>
            <if test="channelName != null and channelName != ''"> and c.channel_name like concat('%', #{channelName}, '%')</if>
            <if test="params.beginCreateTime != null and params.beginCreateTime != ''"><!-- 开始时间检索 -->
                and date_format(o.create_time,'%y%m%d') &gt;= date_format(#{params.beginCreateTime},'%y%m%d')
            </if>
            <if test="params.endCreateTime != null and params.endCreateTime != ''"><!-- 结束时间检索 -->
                and date_format(o.create_time,'%y%m%d') &lt;= date_format(#{params.endCreateTime},'%y%m%d')
            </if>
        </where>
        order by o.create_time desc
    </select>
    
    <select id="selectInventoryInOrderById" parameterType="Long" resultMap="InventoryInOrderResult">
        <include refid="selectInventoryInOrderVo"/>
        where o.id = #{id}
    </select>
        
    <insert id="insertInventoryInOrder" parameterType="InventoryInOrder" useGeneratedKeys="true" keyProperty="id">
        insert into inventory_in_order
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="orderNumber != null">order_number,</if>
            <if test="channelId != null">channel_id,</if>
            <if test="isFutures != null">is_futures,</if>
            <if test="futuresLogistics != null">futures_logistics,</if>
            <if test="createBy != null and createBy != ''">create_by,</if>
            <if test="remark != null and remark != ''">remark,</if>
            create_time,
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="orderNumber != null">#{orderNumber},</if>
            <if test="channelId != null">#{channelId},</if>
            <if test="isFutures != null">#{isFutures},</if>
            <if test="futuresLogistics != null">#{futuresLogistics},</if>
            <if test="createBy != null and createBy != ''">#{createBy},</if>
            <if test="remark != null and remark != ''">#{remark},</if>
            sysdate(),
         </trim>
    </insert>

    <update id="updateInventoryInOrder" parameterType="InventoryInOrder">
        update inventory_in_order
        <trim prefix="SET" suffixOverrides=",">
            <if test="orderNumber != null">order_number = #{orderNumber},</if>
            <if test="channelId != null">channel_id = #{channelId},</if>
            <if test="isFutures != null">is_futures = #{isFutures},</if>
            <if test="futuresLogistics != null">futures_logistics = #{futuresLogistics},</if>
            <if test="updateBy != null and updateBy != ''">update_by = #{updateBy},</if>
            <if test="remark != null">remark = #{remark},</if>
            update_time = sysdate(),
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteInventoryInOrderById" parameterType="Long">
        delete from inventory_in_order where id = #{id}
    </delete>

    <delete id="deleteInventoryInOrderByIds" parameterType="String">
        delete from inventory_in_order where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper> 