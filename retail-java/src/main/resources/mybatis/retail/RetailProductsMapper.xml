<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.retail.project.retail.mapper.RetailProductsMapper">
    
    <resultMap type="RetailProducts" id="RetailProductsResult">
        <result property="id"    column="id"    />
        <result property="productNumber"    column="product_number"    />
        <result property="productName"    column="product_name"    />
        <result property="productImage"    column="product_image"    />
        <result property="brand"    column="brand"    />
        <result property="category"    column="category"    />
        <result property="specification"    column="specification"    />
        <result property="color"    column="color"    />
        <result property="material"    column="material"    />
        <result property="origin"    column="origin"    />
        <result property="standard"    column="standard"    />
        <result property="safetyCategory"    column="safety_category"    />
        <result property="productGrade"    column="product_grade"    />
        <result property="referencePrice"    column="reference_price"    />
        <result property="retailPrice"    column="retail_price"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="remark"    column="remark"    />
    </resultMap>

    <sql id="selectRetailProductsVo">
        select id, product_number, product_name, product_image, brand, category, specification, color, material, origin, standard, safety_category, product_grade, reference_price, retail_price, create_by, create_time, update_by, update_time, remark from retail_products
    </sql>

    <select id="selectRetailProductsList" parameterType="RetailProducts" resultMap="RetailProductsResult">
        <include refid="selectRetailProductsVo"/>
        <where>  
            <if test="productNumber != null  and productNumber != ''"> and product_number like CONCAT('%', #{productNumber}, '%')</if>
            <if test="productName != null  and productName != ''"> and product_name like CONCAT('%', #{productName}, '%')</if>
            <if test="brand != null  and brand != ''"> and brand like CONCAT('%', #{brand}, '%')</if>
            <if test="category != null  and category != ''"> and category like CONCAT('%', #{category}, '%')</if>
        </where>
    </select>
    
    <select id="selectRetailProductsById" parameterType="Long" resultMap="RetailProductsResult">
        <include refid="selectRetailProductsVo"/>
        where id = #{id}
    </select>

    <insert id="insertRetailProducts" parameterType="RetailProducts" useGeneratedKeys="true" keyProperty="id">
        insert into retail_products
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="productNumber != null">product_number,</if>
            <if test="productName != null">product_name,</if>
            <if test="productImage != null">product_image,</if>
            <if test="brand != null">brand,</if>
            <if test="category != null">category,</if>
            <if test="specification != null">specification,</if>
            <if test="color != null">color,</if>
            <if test="material != null">material,</if>
            <if test="origin != null">origin,</if>
            <if test="standard != null">standard,</if>
            <if test="safetyCategory != null">safety_category,</if>
            <if test="productGrade != null">product_grade,</if>
            <if test="referencePrice != null">reference_price,</if>
            <if test="retailPrice != null">retail_price,</if>
            <if test="createBy != null and createBy != ''">create_by,</if>
            <if test="remark != null and remark != ''">remark,</if>
            create_time,
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="productNumber != null">#{productNumber},</if>
            <if test="productName != null">#{productName},</if>
            <if test="productImage != null">#{productImage},</if>
            <if test="brand != null">#{brand},</if>
            <if test="category != null">#{category},</if>
            <if test="specification != null">#{specification},</if>
            <if test="color != null">#{color},</if>
            <if test="material != null">#{material},</if>
            <if test="origin != null">#{origin},</if>
            <if test="standard != null">#{standard},</if>
            <if test="safetyCategory != null">#{safetyCategory},</if>
            <if test="productGrade != null">#{productGrade},</if>
            <if test="referencePrice != null">#{referencePrice},</if>
            <if test="retailPrice != null">#{retailPrice},</if>
            <if test="createBy != null and createBy != ''">#{createBy},</if>
            <if test="remark != null and remark != ''">#{remark},</if>
            sysdate(),
         </trim>
    </insert>

    <update id="updateRetailProducts" parameterType="RetailProducts">
        update retail_products
        <trim prefix="SET" suffixOverrides=",">
            <if test="productNumber != null">product_number = #{productNumber},</if>
            <if test="productName != null">product_name = #{productName},</if>
            <if test="productImage != null">product_image = #{productImage},</if>
            <if test="brand != null">brand = #{brand},</if>
            <if test="category != null">category = #{category},</if>
            <if test="specification != null">specification = #{specification},</if>
            <if test="color != null">color = #{color},</if>
            <if test="material != null">material = #{material},</if>
            <if test="origin != null">origin = #{origin},</if>
            <if test="standard != null">standard = #{standard},</if>
            <if test="safetyCategory != null">safety_category = #{safetyCategory},</if>
            <if test="productGrade != null">product_grade = #{productGrade},</if>
            <if test="referencePrice != null">reference_price = #{referencePrice},</if>
            <if test="retailPrice != null">retail_price = #{retailPrice},</if>
            <if test="updateBy != null and updateBy != ''">update_by = #{updateBy},</if>
            <if test="remark != null">remark = #{remark},</if>
            update_time = sysdate(),
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteRetailProductsById" parameterType="Long">
        delete from retail_products where id = #{id}
    </delete>

    <delete id="deleteRetailProductsByIds" parameterType="String">
        delete from retail_products where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>