<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.retail.project.retail.mapper.RetailCounterMapper">
    
    <resultMap type="RetailCounter" id="RetailCounterResult">
        <result property="id"    column="id"    />
        <result property="storeId"    column="store_id"    />
        <result property="counterName"    column="counter_name"    />
        <result property="storeName"    column="store_name"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="remark"    column="remark"    />
    </resultMap>

    <sql id="selectRetailCounterVo">
        select c.id, c.store_id, c.counter_name, s.store_name, c.create_by, c.create_time, c.update_by, c.update_time, c.remark
        from retail_counter c
        left join retail_store s on c.store_id = s.id
    </sql>

    <select id="selectRetailCounterList" parameterType="RetailCounter" resultMap="RetailCounterResult">
        <include refid="selectRetailCounterVo"/>
        <where>  
            <if test="storeId != null"> and c.store_id = #{storeId}</if>
            <if test="counterName != null and counterName != ''"> and c.counter_name like concat('%', #{counterName}, '%')</if>
        </where>
    </select>
    
    <select id="selectRetailCounterById" parameterType="Long" resultMap="RetailCounterResult">
        <include refid="selectRetailCounterVo"/>
        where c.id = #{id}
    </select>

    <insert id="insertRetailCounter" parameterType="RetailCounter" useGeneratedKeys="true" keyProperty="id">
        insert into retail_counter
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="storeId != null">store_id,</if>
            <if test="counterName != null">counter_name,</if>
            <if test="createBy != null and createBy != ''">create_by,</if>
            <if test="remark != null and remark != ''">remark,</if>
            create_time,
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="storeId != null">#{storeId},</if>
            <if test="counterName != null">#{counterName},</if>
            <if test="createBy != null and createBy != ''">#{createBy},</if>
            <if test="remark != null and remark != ''">#{remark},</if>
            sysdate(),
         </trim>
    </insert>

    <update id="updateRetailCounter" parameterType="RetailCounter">
        update retail_counter
        <trim prefix="SET" suffixOverrides=",">
            <if test="storeId != null">store_id = #{storeId},</if>
            <if test="counterName != null">counter_name = #{counterName},</if>
            <if test="updateBy != null and updateBy != ''">update_by = #{updateBy},</if>
            <if test="remark != null">remark = #{remark},</if>
            update_time = sysdate(),
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteRetailCounterById" parameterType="Long">
        delete from retail_counter where id = #{id}
    </delete>

    <delete id="deleteRetailCounterByIds" parameterType="String">
        delete from retail_counter where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>