<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.retail.project.retail.mapper.InventoryOutAuditMapper">

    <resultMap type="InventoryOutAudit" id="InventoryOutAuditResult">
        <result property="id"    column="id"    />
        <result property="orderId"    column="order_id"    />
        <result property="businessType"    column="business_type"    />
        <result property="processStatus"    column="process_status"    />
        <result property="reason"    column="reason"    />
        <result property="operator"    column="operator"    />
        <result property="operateTime"    column="operate_time"    />
        <result property="orderNumber"    column="order_number"    />
    </resultMap>

    <sql id="selectInventoryOutAuditVo">
        select a.id, a.order_id, a.business_type, a.process_status, a.reason, a.operator, a.operate_time, o.order_number
        from inventory_out_audit a
        left join inventory_out_order o on a.order_id = o.id
    </sql>

    <select id="selectInventoryOutAuditList" parameterType="InventoryOutAudit" resultMap="InventoryOutAuditResult">
        <include refid="selectInventoryOutAuditVo"/>
        <where>
            <if test="orderId != null "> and a.order_id = #{orderId}</if>
            <if test="businessType != null "> and a.business_type = #{businessType}</if>
            <if test="processStatus != null "> and a.process_status = #{processStatus}</if>
            <if test="operator != null  and operator != ''"> and a.operator = #{operator}</if>
            <if test="orderNumber != null  and orderNumber != ''"> and o.order_number like concat('%', #{orderNumber}, '%')</if>
        </where>
        order by a.operate_time desc
    </select>

    <select id="selectInventoryOutAuditById" parameterType="Long" resultMap="InventoryOutAuditResult">
        <include refid="selectInventoryOutAuditVo"/>
        where a.id = #{id}
    </select>

    <select id="selectInventoryOutAuditByOrderId" parameterType="Long" resultMap="InventoryOutAuditResult">
        <include refid="selectInventoryOutAuditVo"/>
        where a.order_id = #{orderId}
        order by a.operate_time asc
    </select>

    <insert id="insertInventoryOutAudit" parameterType="InventoryOutAudit" useGeneratedKeys="true" keyProperty="id">
        insert into inventory_out_audit
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="orderId != null">order_id,</if>
            <if test="businessType != null">business_type,</if>
            <if test="processStatus != null">process_status,</if>
            <if test="reason != null">reason,</if>
            <if test="operator != null">operator,</if>
            operate_time,
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="orderId != null">#{orderId},</if>
            <if test="businessType != null">#{businessType},</if>
            <if test="processStatus != null">#{processStatus},</if>
            <if test="reason != null">#{reason},</if>
            <if test="operator != null">#{operator},</if>
            sysdate(),
         </trim>
    </insert>

    <update id="updateInventoryOutAudit" parameterType="InventoryOutAudit">
        update inventory_out_audit
        <trim prefix="SET" suffixOverrides=",">
            <if test="orderId != null">order_id = #{orderId},</if>
            <if test="businessType != null">business_type = #{businessType},</if>
            <if test="processStatus != null">process_status = #{processStatus},</if>
            <if test="reason != null">reason = #{reason},</if>
            <if test="operator != null">operator = #{operator},</if>
            <if test="operateTime != null">operate_time = #{operateTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteInventoryOutAuditById" parameterType="Long">
        delete from inventory_out_audit where id = #{id}
    </delete>

    <delete id="deleteInventoryOutAuditByIds" parameterType="String">
        delete from inventory_out_audit where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>