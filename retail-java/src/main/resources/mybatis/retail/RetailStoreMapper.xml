<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.retail.project.retail.mapper.RetailStoreMapper">
    
    <resultMap type="RetailStore" id="RetailStoreResult">
        <result property="id"    column="id"    />
        <result property="storeName"    column="store_name"    />
        <result property="storeAddress"    column="store_address"    />
        <result property="storeContact"    column="store_contact"    />
        <result property="userId"    column="user_id"    />
        <result property="userName"    column="user_name"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="remark"    column="remark"    />
    </resultMap>

    <sql id="selectRetailStoreVo">
        select id, store_name, store_address, store_contact, user_id, user_name, create_by, create_time, update_by, update_time, remark from retail_store
    </sql>

    <select id="selectRetailStoreList" parameterType="RetailStore" resultMap="RetailStoreResult">
        <include refid="selectRetailStoreVo"/>
        <where>  
            <if test="storeName != null  and storeName != ''"> and store_name like concat('%', #{storeName}, '%')</if>
            <if test="userId != null  and userId != ''"> and user_id = #{userId}</if>
            <if test="userName != null  and userName != ''"> and user_name like concat('%', #{userName}, '%')</if>
        </where>
    </select>
    
    <select id="selectRetailStoreById" parameterType="Long" resultMap="RetailStoreResult">
        <include refid="selectRetailStoreVo"/>
        where id = #{id}
    </select>
    
    <select id="selectStoreUserIds" parameterType="Long" resultType="String">
        select user_id from retail_store where id = #{storeId}
    </select>
    
    <insert id="insertRetailStore" parameterType="RetailStore" useGeneratedKeys="true" keyProperty="id">
        insert into retail_store
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="storeName != null">store_name,</if>
            <if test="storeAddress != null">store_address,</if>
            <if test="storeContact != null">store_contact,</if>
            <if test="userId != null">user_id,</if>
            <if test="userName != null">user_name,</if>
            <if test="createBy != null and createBy != ''">create_by,</if>
            <if test="remark != null and remark != ''">remark,</if>
            create_time,
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="storeName != null">#{storeName},</if>
            <if test="storeAddress != null">#{storeAddress},</if>
            <if test="storeContact != null">#{storeContact},</if>
            <if test="userId != null">#{userId},</if>
            <if test="userName != null">#{userName},</if>
            <if test="createBy != null and createBy != ''">#{createBy},</if>
            <if test="remark != null and remark != ''">#{remark},</if>
            sysdate(),
         </trim>
    </insert>

    <update id="updateRetailStore" parameterType="RetailStore">
        update retail_store
        <trim prefix="SET" suffixOverrides=",">
            <if test="storeName != null">store_name = #{storeName},</if>
            <if test="storeAddress != null">store_address = #{storeAddress},</if>
            <if test="storeContact != null">store_contact = #{storeContact},</if>
            <if test="userId != null">user_id = #{userId},</if>
            <if test="userName != null">user_name = #{userName},</if>
            <if test="updateBy != null and updateBy != ''">update_by = #{updateBy},</if>
            <if test="remark != null">remark = #{remark},</if>
            update_time = sysdate(),
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteRetailStoreById" parameterType="Long">
        delete from retail_store where id = #{id}
    </delete>

    <delete id="deleteRetailStoreByIds" parameterType="String">
        delete from retail_store where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>