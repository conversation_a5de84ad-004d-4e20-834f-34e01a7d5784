-- MySQL dump 10.13  Distrib 9.3.0, for Linux (aarch64)
--
-- Host: localhost    Database: ruoyi
-- ------------------------------------------------------
-- Server version	9.3.0

/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET @OLD_CHARACTER_SET_RESULTS=@@CHARACTER_SET_RESULTS */;
/*!40101 SET @OLD_COLLATION_CONNECTION=@@COLLATION_CONNECTION */;
/*!50503 SET NAMES utf8mb4 */;
/*!40103 SET @OLD_TIME_ZONE=@@TIME_ZONE */;
/*!40103 SET TIME_ZONE='+00:00' */;
/*!40014 SET @OLD_UNIQUE_CHECKS=@@UNIQUE_CHECKS, UNIQUE_CHECKS=0 */;
/*!40014 SET @OLD_FOREIGN_KEY_CHECKS=@@FOREIGN_KEY_CHECKS, FOREIGN_KEY_CHECKS=0 */;
/*!40101 SET @OLD_SQL_MODE=@@SQL_MODE, SQL_MODE='NO_AUTO_VALUE_ON_ZERO' */;
/*!40111 SET @OLD_SQL_NOTES=@@SQL_NOTES, SQL_NOTES=0 */;

--
-- Table structure for table `QRTZ_BLOB_TRIGGERS`
--

DROP TABLE IF EXISTS `QRTZ_BLOB_TRIGGERS`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `QRTZ_BLOB_TRIGGERS` (
  `sched_name` varchar(120) NOT NULL COMMENT '调度名称',
  `trigger_name` varchar(200) NOT NULL COMMENT 'qrtz_triggers表trigger_name的外键',
  `trigger_group` varchar(200) NOT NULL COMMENT 'qrtz_triggers表trigger_group的外键',
  `blob_data` blob COMMENT '存放持久化Trigger对象',
  PRIMARY KEY (`sched_name`,`trigger_name`,`trigger_group`),
  CONSTRAINT `QRTZ_BLOB_TRIGGERS_ibfk_1` FOREIGN KEY (`sched_name`, `trigger_name`, `trigger_group`) REFERENCES `QRTZ_TRIGGERS` (`sched_name`, `trigger_name`, `trigger_group`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='Blob类型的触发器表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `QRTZ_BLOB_TRIGGERS`
--

LOCK TABLES `QRTZ_BLOB_TRIGGERS` WRITE;
/*!40000 ALTER TABLE `QRTZ_BLOB_TRIGGERS` DISABLE KEYS */;
/*!40000 ALTER TABLE `QRTZ_BLOB_TRIGGERS` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `QRTZ_CALENDARS`
--

DROP TABLE IF EXISTS `QRTZ_CALENDARS`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `QRTZ_CALENDARS` (
  `sched_name` varchar(120) NOT NULL COMMENT '调度名称',
  `calendar_name` varchar(200) NOT NULL COMMENT '日历名称',
  `calendar` blob NOT NULL COMMENT '存放持久化calendar对象',
  PRIMARY KEY (`sched_name`,`calendar_name`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='日历信息表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `QRTZ_CALENDARS`
--

LOCK TABLES `QRTZ_CALENDARS` WRITE;
/*!40000 ALTER TABLE `QRTZ_CALENDARS` DISABLE KEYS */;
/*!40000 ALTER TABLE `QRTZ_CALENDARS` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `QRTZ_CRON_TRIGGERS`
--

DROP TABLE IF EXISTS `QRTZ_CRON_TRIGGERS`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `QRTZ_CRON_TRIGGERS` (
  `sched_name` varchar(120) NOT NULL COMMENT '调度名称',
  `trigger_name` varchar(200) NOT NULL COMMENT 'qrtz_triggers表trigger_name的外键',
  `trigger_group` varchar(200) NOT NULL COMMENT 'qrtz_triggers表trigger_group的外键',
  `cron_expression` varchar(200) NOT NULL COMMENT 'cron表达式',
  `time_zone_id` varchar(80) DEFAULT NULL COMMENT '时区',
  PRIMARY KEY (`sched_name`,`trigger_name`,`trigger_group`),
  CONSTRAINT `QRTZ_CRON_TRIGGERS_ibfk_1` FOREIGN KEY (`sched_name`, `trigger_name`, `trigger_group`) REFERENCES `QRTZ_TRIGGERS` (`sched_name`, `trigger_name`, `trigger_group`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='Cron类型的触发器表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `QRTZ_CRON_TRIGGERS`
--

LOCK TABLES `QRTZ_CRON_TRIGGERS` WRITE;
/*!40000 ALTER TABLE `QRTZ_CRON_TRIGGERS` DISABLE KEYS */;
/*!40000 ALTER TABLE `QRTZ_CRON_TRIGGERS` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `QRTZ_FIRED_TRIGGERS`
--

DROP TABLE IF EXISTS `QRTZ_FIRED_TRIGGERS`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `QRTZ_FIRED_TRIGGERS` (
  `sched_name` varchar(120) NOT NULL COMMENT '调度名称',
  `entry_id` varchar(95) NOT NULL COMMENT '调度器实例id',
  `trigger_name` varchar(200) NOT NULL COMMENT 'qrtz_triggers表trigger_name的外键',
  `trigger_group` varchar(200) NOT NULL COMMENT 'qrtz_triggers表trigger_group的外键',
  `instance_name` varchar(200) NOT NULL COMMENT '调度器实例名',
  `fired_time` bigint NOT NULL COMMENT '触发的时间',
  `sched_time` bigint NOT NULL COMMENT '定时器制定的时间',
  `priority` int NOT NULL COMMENT '优先级',
  `state` varchar(16) NOT NULL COMMENT '状态',
  `job_name` varchar(200) DEFAULT NULL COMMENT '任务名称',
  `job_group` varchar(200) DEFAULT NULL COMMENT '任务组名',
  `is_nonconcurrent` varchar(1) DEFAULT NULL COMMENT '是否并发',
  `requests_recovery` varchar(1) DEFAULT NULL COMMENT '是否接受恢复执行',
  PRIMARY KEY (`sched_name`,`entry_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='已触发的触发器表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `QRTZ_FIRED_TRIGGERS`
--

LOCK TABLES `QRTZ_FIRED_TRIGGERS` WRITE;
/*!40000 ALTER TABLE `QRTZ_FIRED_TRIGGERS` DISABLE KEYS */;
/*!40000 ALTER TABLE `QRTZ_FIRED_TRIGGERS` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `QRTZ_JOB_DETAILS`
--

DROP TABLE IF EXISTS `QRTZ_JOB_DETAILS`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `QRTZ_JOB_DETAILS` (
  `sched_name` varchar(120) NOT NULL COMMENT '调度名称',
  `job_name` varchar(200) NOT NULL COMMENT '任务名称',
  `job_group` varchar(200) NOT NULL COMMENT '任务组名',
  `description` varchar(250) DEFAULT NULL COMMENT '相关介绍',
  `job_class_name` varchar(250) NOT NULL COMMENT '执行任务类名称',
  `is_durable` varchar(1) NOT NULL COMMENT '是否持久化',
  `is_nonconcurrent` varchar(1) NOT NULL COMMENT '是否并发',
  `is_update_data` varchar(1) NOT NULL COMMENT '是否更新数据',
  `requests_recovery` varchar(1) NOT NULL COMMENT '是否接受恢复执行',
  `job_data` blob COMMENT '存放持久化job对象',
  PRIMARY KEY (`sched_name`,`job_name`,`job_group`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='任务详细信息表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `QRTZ_JOB_DETAILS`
--

LOCK TABLES `QRTZ_JOB_DETAILS` WRITE;
/*!40000 ALTER TABLE `QRTZ_JOB_DETAILS` DISABLE KEYS */;
/*!40000 ALTER TABLE `QRTZ_JOB_DETAILS` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `QRTZ_LOCKS`
--

DROP TABLE IF EXISTS `QRTZ_LOCKS`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `QRTZ_LOCKS` (
  `sched_name` varchar(120) NOT NULL COMMENT '调度名称',
  `lock_name` varchar(40) NOT NULL COMMENT '悲观锁名称',
  PRIMARY KEY (`sched_name`,`lock_name`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='存储的悲观锁信息表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `QRTZ_LOCKS`
--

LOCK TABLES `QRTZ_LOCKS` WRITE;
/*!40000 ALTER TABLE `QRTZ_LOCKS` DISABLE KEYS */;
/*!40000 ALTER TABLE `QRTZ_LOCKS` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `QRTZ_PAUSED_TRIGGER_GRPS`
--

DROP TABLE IF EXISTS `QRTZ_PAUSED_TRIGGER_GRPS`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `QRTZ_PAUSED_TRIGGER_GRPS` (
  `sched_name` varchar(120) NOT NULL COMMENT '调度名称',
  `trigger_group` varchar(200) NOT NULL COMMENT 'qrtz_triggers表trigger_group的外键',
  PRIMARY KEY (`sched_name`,`trigger_group`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='暂停的触发器表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `QRTZ_PAUSED_TRIGGER_GRPS`
--

LOCK TABLES `QRTZ_PAUSED_TRIGGER_GRPS` WRITE;
/*!40000 ALTER TABLE `QRTZ_PAUSED_TRIGGER_GRPS` DISABLE KEYS */;
/*!40000 ALTER TABLE `QRTZ_PAUSED_TRIGGER_GRPS` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `QRTZ_SCHEDULER_STATE`
--

DROP TABLE IF EXISTS `QRTZ_SCHEDULER_STATE`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `QRTZ_SCHEDULER_STATE` (
  `sched_name` varchar(120) NOT NULL COMMENT '调度名称',
  `instance_name` varchar(200) NOT NULL COMMENT '实例名称',
  `last_checkin_time` bigint NOT NULL COMMENT '上次检查时间',
  `checkin_interval` bigint NOT NULL COMMENT '检查间隔时间',
  PRIMARY KEY (`sched_name`,`instance_name`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='调度器状态表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `QRTZ_SCHEDULER_STATE`
--

LOCK TABLES `QRTZ_SCHEDULER_STATE` WRITE;
/*!40000 ALTER TABLE `QRTZ_SCHEDULER_STATE` DISABLE KEYS */;
/*!40000 ALTER TABLE `QRTZ_SCHEDULER_STATE` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `QRTZ_SIMPLE_TRIGGERS`
--

DROP TABLE IF EXISTS `QRTZ_SIMPLE_TRIGGERS`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `QRTZ_SIMPLE_TRIGGERS` (
  `sched_name` varchar(120) NOT NULL COMMENT '调度名称',
  `trigger_name` varchar(200) NOT NULL COMMENT 'qrtz_triggers表trigger_name的外键',
  `trigger_group` varchar(200) NOT NULL COMMENT 'qrtz_triggers表trigger_group的外键',
  `repeat_count` bigint NOT NULL COMMENT '重复的次数统计',
  `repeat_interval` bigint NOT NULL COMMENT '重复的间隔时间',
  `times_triggered` bigint NOT NULL COMMENT '已经触发的次数',
  PRIMARY KEY (`sched_name`,`trigger_name`,`trigger_group`),
  CONSTRAINT `QRTZ_SIMPLE_TRIGGERS_ibfk_1` FOREIGN KEY (`sched_name`, `trigger_name`, `trigger_group`) REFERENCES `QRTZ_TRIGGERS` (`sched_name`, `trigger_name`, `trigger_group`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='简单触发器的信息表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `QRTZ_SIMPLE_TRIGGERS`
--

LOCK TABLES `QRTZ_SIMPLE_TRIGGERS` WRITE;
/*!40000 ALTER TABLE `QRTZ_SIMPLE_TRIGGERS` DISABLE KEYS */;
/*!40000 ALTER TABLE `QRTZ_SIMPLE_TRIGGERS` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `QRTZ_SIMPROP_TRIGGERS`
--

DROP TABLE IF EXISTS `QRTZ_SIMPROP_TRIGGERS`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `QRTZ_SIMPROP_TRIGGERS` (
  `sched_name` varchar(120) NOT NULL COMMENT '调度名称',
  `trigger_name` varchar(200) NOT NULL COMMENT 'qrtz_triggers表trigger_name的外键',
  `trigger_group` varchar(200) NOT NULL COMMENT 'qrtz_triggers表trigger_group的外键',
  `str_prop_1` varchar(512) DEFAULT NULL COMMENT 'String类型的trigger的第一个参数',
  `str_prop_2` varchar(512) DEFAULT NULL COMMENT 'String类型的trigger的第二个参数',
  `str_prop_3` varchar(512) DEFAULT NULL COMMENT 'String类型的trigger的第三个参数',
  `int_prop_1` int DEFAULT NULL COMMENT 'int类型的trigger的第一个参数',
  `int_prop_2` int DEFAULT NULL COMMENT 'int类型的trigger的第二个参数',
  `long_prop_1` bigint DEFAULT NULL COMMENT 'long类型的trigger的第一个参数',
  `long_prop_2` bigint DEFAULT NULL COMMENT 'long类型的trigger的第二个参数',
  `dec_prop_1` decimal(13,4) DEFAULT NULL COMMENT 'decimal类型的trigger的第一个参数',
  `dec_prop_2` decimal(13,4) DEFAULT NULL COMMENT 'decimal类型的trigger的第二个参数',
  `bool_prop_1` varchar(1) DEFAULT NULL COMMENT 'Boolean类型的trigger的第一个参数',
  `bool_prop_2` varchar(1) DEFAULT NULL COMMENT 'Boolean类型的trigger的第二个参数',
  PRIMARY KEY (`sched_name`,`trigger_name`,`trigger_group`),
  CONSTRAINT `QRTZ_SIMPROP_TRIGGERS_ibfk_1` FOREIGN KEY (`sched_name`, `trigger_name`, `trigger_group`) REFERENCES `QRTZ_TRIGGERS` (`sched_name`, `trigger_name`, `trigger_group`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='同步机制的行锁表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `QRTZ_SIMPROP_TRIGGERS`
--

LOCK TABLES `QRTZ_SIMPROP_TRIGGERS` WRITE;
/*!40000 ALTER TABLE `QRTZ_SIMPROP_TRIGGERS` DISABLE KEYS */;
/*!40000 ALTER TABLE `QRTZ_SIMPROP_TRIGGERS` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `QRTZ_TRIGGERS`
--

DROP TABLE IF EXISTS `QRTZ_TRIGGERS`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `QRTZ_TRIGGERS` (
  `sched_name` varchar(120) NOT NULL COMMENT '调度名称',
  `trigger_name` varchar(200) NOT NULL COMMENT '触发器的名字',
  `trigger_group` varchar(200) NOT NULL COMMENT '触发器所属组的名字',
  `job_name` varchar(200) NOT NULL COMMENT 'qrtz_job_details表job_name的外键',
  `job_group` varchar(200) NOT NULL COMMENT 'qrtz_job_details表job_group的外键',
  `description` varchar(250) DEFAULT NULL COMMENT '相关介绍',
  `next_fire_time` bigint DEFAULT NULL COMMENT '上一次触发时间（毫秒）',
  `prev_fire_time` bigint DEFAULT NULL COMMENT '下一次触发时间（默认为-1表示不触发）',
  `priority` int DEFAULT NULL COMMENT '优先级',
  `trigger_state` varchar(16) NOT NULL COMMENT '触发器状态',
  `trigger_type` varchar(8) NOT NULL COMMENT '触发器的类型',
  `start_time` bigint NOT NULL COMMENT '开始时间',
  `end_time` bigint DEFAULT NULL COMMENT '结束时间',
  `calendar_name` varchar(200) DEFAULT NULL COMMENT '日程表名称',
  `misfire_instr` smallint DEFAULT NULL COMMENT '补偿执行的策略',
  `job_data` blob COMMENT '存放持久化job对象',
  PRIMARY KEY (`sched_name`,`trigger_name`,`trigger_group`),
  KEY `sched_name` (`sched_name`,`job_name`,`job_group`),
  CONSTRAINT `QRTZ_TRIGGERS_ibfk_1` FOREIGN KEY (`sched_name`, `job_name`, `job_group`) REFERENCES `QRTZ_JOB_DETAILS` (`sched_name`, `job_name`, `job_group`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='触发器详细信息表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `QRTZ_TRIGGERS`
--

LOCK TABLES `QRTZ_TRIGGERS` WRITE;
/*!40000 ALTER TABLE `QRTZ_TRIGGERS` DISABLE KEYS */;
/*!40000 ALTER TABLE `QRTZ_TRIGGERS` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `gen_table`
--

DROP TABLE IF EXISTS `gen_table`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `gen_table` (
  `table_id` bigint NOT NULL AUTO_INCREMENT COMMENT '编号',
  `table_name` varchar(200) DEFAULT '' COMMENT '表名称',
  `table_comment` varchar(500) DEFAULT '' COMMENT '表描述',
  `sub_table_name` varchar(64) DEFAULT NULL COMMENT '关联子表的表名',
  `sub_table_fk_name` varchar(64) DEFAULT NULL COMMENT '子表关联的外键名',
  `class_name` varchar(100) DEFAULT '' COMMENT '实体类名称',
  `tpl_category` varchar(200) DEFAULT 'crud' COMMENT '使用的模板（crud单表操作 tree树表操作）',
  `tpl_web_type` varchar(30) DEFAULT '' COMMENT '前端模板类型（element-ui模版 element-plus模版）',
  `package_name` varchar(100) DEFAULT NULL COMMENT '生成包路径',
  `module_name` varchar(30) DEFAULT NULL COMMENT '生成模块名',
  `business_name` varchar(30) DEFAULT NULL COMMENT '生成业务名',
  `function_name` varchar(50) DEFAULT NULL COMMENT '生成功能名',
  `function_author` varchar(50) DEFAULT NULL COMMENT '生成功能作者',
  `gen_type` char(1) DEFAULT '0' COMMENT '生成代码方式（0zip压缩包 1自定义路径）',
  `gen_path` varchar(200) DEFAULT '/' COMMENT '生成路径（不填默认项目路径）',
  `options` varchar(1000) DEFAULT NULL COMMENT '其它生成选项',
  `create_by` varchar(64) DEFAULT '' COMMENT '创建者',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(64) DEFAULT '' COMMENT '更新者',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  `remark` varchar(500) DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`table_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='代码生成业务表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `gen_table`
--

LOCK TABLES `gen_table` WRITE;
/*!40000 ALTER TABLE `gen_table` DISABLE KEYS */;
/*!40000 ALTER TABLE `gen_table` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `gen_table_column`
--

DROP TABLE IF EXISTS `gen_table_column`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `gen_table_column` (
  `column_id` bigint NOT NULL AUTO_INCREMENT COMMENT '编号',
  `table_id` bigint DEFAULT NULL COMMENT '归属表编号',
  `column_name` varchar(200) DEFAULT NULL COMMENT '列名称',
  `column_comment` varchar(500) DEFAULT NULL COMMENT '列描述',
  `column_type` varchar(100) DEFAULT NULL COMMENT '列类型',
  `java_type` varchar(500) DEFAULT NULL COMMENT 'JAVA类型',
  `java_field` varchar(200) DEFAULT NULL COMMENT 'JAVA字段名',
  `is_pk` char(1) DEFAULT NULL COMMENT '是否主键（1是）',
  `is_increment` char(1) DEFAULT NULL COMMENT '是否自增（1是）',
  `is_required` char(1) DEFAULT NULL COMMENT '是否必填（1是）',
  `is_insert` char(1) DEFAULT NULL COMMENT '是否为插入字段（1是）',
  `is_edit` char(1) DEFAULT NULL COMMENT '是否编辑字段（1是）',
  `is_list` char(1) DEFAULT NULL COMMENT '是否列表字段（1是）',
  `is_query` char(1) DEFAULT NULL COMMENT '是否查询字段（1是）',
  `query_type` varchar(200) DEFAULT 'EQ' COMMENT '查询方式（等于、不等于、大于、小于、范围）',
  `html_type` varchar(200) DEFAULT NULL COMMENT '显示类型（文本框、文本域、下拉框、复选框、单选框、日期控件）',
  `dict_type` varchar(200) DEFAULT '' COMMENT '字典类型',
  `sort` int DEFAULT NULL COMMENT '排序',
  `create_by` varchar(64) DEFAULT '' COMMENT '创建者',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(64) DEFAULT '' COMMENT '更新者',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`column_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='代码生成业务表字段';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `gen_table_column`
--

LOCK TABLES `gen_table_column` WRITE;
/*!40000 ALTER TABLE `gen_table_column` DISABLE KEYS */;
/*!40000 ALTER TABLE `gen_table_column` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `inventory_in_detail`
--

DROP TABLE IF EXISTS `inventory_in_detail`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `inventory_in_detail` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '明细ID',
  `order_id` bigint NOT NULL COMMENT '入库单ID',
  `product_id` bigint NOT NULL COMMENT '商品ID',
  `store_id` bigint NOT NULL COMMENT '门店ID',
  `counter_id` bigint DEFAULT NULL COMMENT '柜台ID',
  `quantity` int NOT NULL COMMENT '入库数量',
  `purchase_price` decimal(10,2) NOT NULL COMMENT '进货价',
  `batch_number` varchar(50) NOT NULL COMMENT '批次号',
  `product_name` varchar(100) DEFAULT NULL COMMENT '冗余商品名称',
  `create_by` varchar(64) DEFAULT '' COMMENT '创建者',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(64) DEFAULT '' COMMENT '更新者',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  `remark` varchar(500) DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`id`),
  KEY `idx_order_id` (`order_id`) COMMENT '入库单ID索引',
  KEY `idx_product_id` (`product_id`) COMMENT '商品ID索引',
  KEY `idx_store_id` (`store_id`) COMMENT '门店ID索引',
  KEY `idx_counter_id` (`counter_id`) COMMENT '柜台ID索引',
  KEY `idx_batch_number` (`batch_number`) COMMENT '批次号索引',
  CONSTRAINT `fk_in_detail_counter` FOREIGN KEY (`counter_id`) REFERENCES `retail_counter` (`id`) ON DELETE RESTRICT ON UPDATE CASCADE,
  CONSTRAINT `fk_in_detail_order` FOREIGN KEY (`order_id`) REFERENCES `inventory_in_order` (`id`) ON DELETE CASCADE ON UPDATE CASCADE,
  CONSTRAINT `fk_in_detail_product` FOREIGN KEY (`product_id`) REFERENCES `retail_products` (`id`) ON DELETE RESTRICT ON UPDATE CASCADE,
  CONSTRAINT `fk_in_detail_store` FOREIGN KEY (`store_id`) REFERENCES `retail_store` (`id`) ON DELETE RESTRICT ON UPDATE CASCADE
) ENGINE=InnoDB AUTO_INCREMENT=17 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='入库明细表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `inventory_in_detail`
--

LOCK TABLES `inventory_in_detail` WRITE;
/*!40000 ALTER TABLE `inventory_in_detail` DISABLE KEYS */;
INSERT INTO `inventory_in_detail` VALUES (11,5,1,1,1,10,2800.00,'c02dda4a18','COACH 背提包-2525001','finance','2025-04-29 12:20:32','finance','2025-04-29 12:21:22',NULL),(12,5,2,2,2,5,2900.00,'c02dda4a18','COACH 背提包-2524996','finance','2025-04-29 12:20:32','finance','2025-04-29 12:21:22',NULL),(13,5,1,2,2,5,2700.00,'75baad55fd','COACH 背提包-2525001','finance','2025-04-29 12:21:22','',NULL,NULL),(14,5,2,1,1,10,2800.00,'40d6bea597','COACH 背提包-2524996','finance','2025-04-29 12:21:22','',NULL,NULL),(15,6,1,1,1,5,2600.00,'57f8b210a3','COACH 背提包-2525001','finance','2025-04-29 12:22:59','',NULL,NULL),(16,6,2,1,1,5,2600.00,'57f8b210a3','COACH 背提包-2524996','finance','2025-04-29 12:22:59','',NULL,NULL);
/*!40000 ALTER TABLE `inventory_in_detail` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `inventory_in_order`
--

DROP TABLE IF EXISTS `inventory_in_order`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `inventory_in_order` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '入库单ID',
  `order_number` varchar(50) NOT NULL COMMENT '入库单号',
  `channel_id` bigint NOT NULL COMMENT '渠道ID',
  `is_futures` tinyint(1) DEFAULT '0' COMMENT '是否期货',
  `futures_logistics` varchar(255) DEFAULT NULL COMMENT '期货物流',
  `create_by` varchar(64) DEFAULT '' COMMENT '创建者',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(64) DEFAULT '' COMMENT '更新者',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  `remark` varchar(500) DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`id`),
  UNIQUE KEY `idx_order_number` (`order_number`) COMMENT '入库单号唯一索引',
  KEY `idx_channel_id` (`channel_id`) COMMENT '渠道ID索引',
  CONSTRAINT `fk_in_order_channel` FOREIGN KEY (`channel_id`) REFERENCES `retail_channel` (`id`) ON DELETE RESTRICT ON UPDATE CASCADE
) ENGINE=InnoDB AUTO_INCREMENT=7 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='入库单表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `inventory_in_order`
--

LOCK TABLES `inventory_in_order` WRITE;
/*!40000 ALTER TABLE `inventory_in_order` DISABLE KEYS */;
INSERT INTO `inventory_in_order` VALUES (5,'IN9331212428',1,0,NULL,'finance','2025-04-29 12:20:32','finance','2025-04-29 12:21:22',NULL),(6,'IN1600505038',2,1,'1122334455','finance','2025-04-29 12:22:59','',NULL,NULL);
/*!40000 ALTER TABLE `inventory_in_order` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `inventory_out_audit`
--

DROP TABLE IF EXISTS `inventory_out_audit`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `inventory_out_audit` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '审核记录ID',
  `order_id` bigint NOT NULL COMMENT '出库单ID',
  `reason` varchar(500) DEFAULT NULL COMMENT '操作理由/意见',
  `operator` varchar(64) NOT NULL COMMENT '操作人',
  `operate_time` datetime NOT NULL COMMENT '操作时间',
  `business_type` tinyint(1) DEFAULT NULL COMMENT '业务场景（0售出 1预订 2退货 3取消 4交货 5库存删除 6库存迁移）',
  `process_status` tinyint(1) DEFAULT NULL COMMENT '流程节点（0待审核 1已审核 2已驳回 3已撤销）',
  PRIMARY KEY (`id`),
  KEY `idx_order_id` (`order_id`) COMMENT '出库单ID索引',
  CONSTRAINT `fk_audit_order` FOREIGN KEY (`order_id`) REFERENCES `inventory_out_order` (`id`) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE=InnoDB AUTO_INCREMENT=100 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='出库单审核记录表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `inventory_out_audit`
--

LOCK TABLES `inventory_out_audit` WRITE;
/*!40000 ALTER TABLE `inventory_out_audit` DISABLE KEYS */;
INSERT INTO `inventory_out_audit` VALUES (62,27,NULL,'store1','2025-04-30 14:02:15',1,0),(63,27,'11','store1','2025-04-30 14:02:46',1,3),(64,28,NULL,'store1','2025-04-30 14:03:24',1,0),(65,28,'11','finance','2025-04-30 14:06:40',1,2),(66,29,NULL,'store1','2025-04-30 14:07:14',1,0),(67,29,'22','finance','2025-04-30 14:07:33',1,1),(68,29,'22','store1','2025-04-30 14:21:48',3,1),(69,30,NULL,'store1','2025-04-30 15:09:25',1,0),(70,30,'33','finance','2025-04-30 15:10:28',1,1),(71,30,'error','store1','2025-04-30 15:18:42',3,0),(72,30,'no','finance','2025-04-30 15:19:00',3,2),(73,31,NULL,'store1','2025-04-30 15:20:29',1,0),(74,31,'44','finance','2025-04-30 15:20:46',1,1),(75,31,'44','store1','2025-04-30 15:21:08',3,0),(76,31,'44','finance','2025-04-30 15:21:22',3,1),(77,32,NULL,'store1','2025-04-30 15:31:39',1,0),(78,32,'55','finance','2025-04-30 15:33:41',1,1),(79,32,'55','finance','2025-04-30 15:34:12',3,0),(80,32,'55','finance','2025-04-30 15:34:43',3,1),(81,33,NULL,'store1','2025-04-30 15:55:55',1,0),(82,33,'66','finance','2025-04-30 15:56:12',1,1),(83,33,'66','store1','2025-04-30 15:56:36',3,0),(84,33,'66','finance','2025-04-30 15:56:50',3,1),(85,34,NULL,'store1','2025-04-30 15:57:25',1,0),(86,34,'77','finance','2025-04-30 15:57:35',1,1),(87,34,'预订单交货完成','finance','2025-04-30 16:50:27',4,1),(88,35,NULL,'store1','2025-04-30 17:00:11',1,0),(89,35,'88','finance','2025-04-30 17:00:23',1,1),(90,35,'null | 交货备注: 88交货','finance','2025-04-30 17:00:52',4,0),(91,35,'88信息错误','finance','2025-04-30 17:09:43',4,2),(92,35,'88交货','finance','2025-04-30 17:10:16',4,0),(93,35,'88信息错误1','finance','2025-04-30 17:10:51',4,2),(94,35,'88交货','finance','2025-04-30 17:11:12',4,0),(95,35,'88交货完成','finance','2025-04-30 17:17:51',4,1),(96,36,NULL,'store1','2025-05-06 03:41:36',0,0),(97,36,'1122','finance','2025-05-06 03:42:03',0,1),(98,36,'112233','store1','2025-05-06 03:52:46',2,0),(99,36,'11223344','finance','2025-05-06 03:57:10',2,2);
/*!40000 ALTER TABLE `inventory_out_audit` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `inventory_out_detail`
--

DROP TABLE IF EXISTS `inventory_out_detail`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `inventory_out_detail` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '明细ID',
  `order_id` bigint NOT NULL COMMENT '出库单ID',
  `product_id` bigint NOT NULL COMMENT '商品ID',
  `counter_id` bigint DEFAULT NULL COMMENT '柜台ID',
  `channel_id` bigint DEFAULT NULL COMMENT '渠道ID',
  `quantity` int NOT NULL COMMENT '出库数量',
  `sale_price` decimal(10,2) NOT NULL COMMENT '销售价',
  `batch_number` varchar(50) NOT NULL COMMENT '批次号',
  `product_name` varchar(100) DEFAULT NULL COMMENT '冗余商品名称',
  `create_by` varchar(64) DEFAULT '' COMMENT '创建者',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(64) DEFAULT '' COMMENT '更新者',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  `remark` varchar(500) DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`id`),
  KEY `idx_order_id` (`order_id`) COMMENT '出库单ID索引',
  KEY `idx_product_id` (`product_id`) COMMENT '商品ID索引',
  KEY `idx_counter_id` (`counter_id`) COMMENT '柜台ID索引',
  KEY `idx_channel_id` (`channel_id`) COMMENT '渠道ID索引',
  KEY `idx_batch_number` (`batch_number`) COMMENT '批次号索引',
  CONSTRAINT `fk_out_detail_channel` FOREIGN KEY (`channel_id`) REFERENCES `retail_channel` (`id`) ON DELETE RESTRICT ON UPDATE CASCADE,
  CONSTRAINT `fk_out_detail_counter` FOREIGN KEY (`counter_id`) REFERENCES `retail_counter` (`id`) ON DELETE RESTRICT ON UPDATE CASCADE,
  CONSTRAINT `fk_out_detail_order` FOREIGN KEY (`order_id`) REFERENCES `inventory_out_order` (`id`) ON DELETE CASCADE ON UPDATE CASCADE,
  CONSTRAINT `fk_out_detail_product` FOREIGN KEY (`product_id`) REFERENCES `retail_products` (`id`) ON DELETE RESTRICT ON UPDATE CASCADE
) ENGINE=InnoDB AUTO_INCREMENT=38 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='出库明细表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `inventory_out_detail`
--

LOCK TABLES `inventory_out_detail` WRITE;
/*!40000 ALTER TABLE `inventory_out_detail` DISABLE KEYS */;
INSERT INTO `inventory_out_detail` VALUES (28,27,1,NULL,NULL,1,4980.00,'R1745992935213',NULL,'store1','2025-04-30 14:02:15','',NULL,NULL),(29,28,1,NULL,NULL,1,4980.00,'R1745993004744',NULL,'store1','2025-04-30 14:03:24','',NULL,NULL),(30,29,1,NULL,NULL,1,4980.00,'R1745993234304',NULL,'store1','2025-04-30 14:07:14','',NULL,NULL),(31,30,1,NULL,NULL,1,4980.00,'R1745996965050',NULL,'store1','2025-04-30 15:09:25','',NULL,NULL),(32,31,1,NULL,NULL,1,4980.00,'R1745997629832',NULL,'store1','2025-04-30 15:20:29','',NULL,NULL),(33,32,1,NULL,NULL,1,4980.00,'R1745998299431',NULL,'store1','2025-04-30 15:31:39','',NULL,NULL),(34,33,1,NULL,NULL,1,4980.00,'R1745999755691',NULL,'store1','2025-04-30 15:55:55','',NULL,NULL),(35,34,1,1,2,1,4980.00,'57f8b210a3','COACH 背提包-2525001','store1','2025-04-30 15:57:25','finance','2025-04-30 16:50:27',NULL),(36,35,1,1,2,1,4980.00,'57f8b210a3','COACH 背提包-2525001','store1','2025-04-30 17:00:11','finance','2025-04-30 17:11:12',NULL),(37,36,2,1,1,1,3300.00,'40d6bea597','COACH 背提包-2524996','store1','2025-05-06 03:41:36','',NULL,NULL);
/*!40000 ALTER TABLE `inventory_out_detail` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `inventory_out_flow`
--

DROP TABLE IF EXISTS `inventory_out_flow`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `inventory_out_flow` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '流程ID',
  `name` varchar(100) NOT NULL COMMENT '流程名称',
  `description` varchar(255) DEFAULT NULL COMMENT '流程描述',
  `store_id` bigint DEFAULT NULL COMMENT '门店ID',
  `approver_roles` varchar(255) DEFAULT NULL COMMENT '审批角色，多个用逗号分隔',
  `status` tinyint(1) DEFAULT '1' COMMENT '状态（0停用 1启用）',
  `create_by` varchar(64) DEFAULT '' COMMENT '创建者',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(64) DEFAULT '' COMMENT '更新者',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_store_id` (`store_id`) COMMENT '门店ID索引',
  CONSTRAINT `fk_flow_store` FOREIGN KEY (`store_id`) REFERENCES `retail_store` (`id`) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE=InnoDB AUTO_INCREMENT=2 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='出库审核流程表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `inventory_out_flow`
--

LOCK TABLES `inventory_out_flow` WRITE;
/*!40000 ALTER TABLE `inventory_out_flow` DISABLE KEYS */;
INSERT INTO `inventory_out_flow` VALUES (1,'默认审核','全流程审核',NULL,'admin,manager,finance',1,'admin','2025-04-27 09:12:18','',NULL);
/*!40000 ALTER TABLE `inventory_out_flow` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `inventory_out_order`
--

DROP TABLE IF EXISTS `inventory_out_order`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `inventory_out_order` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '出库单ID',
  `order_number` varchar(50) NOT NULL COMMENT '出库单号',
  `order_type` tinyint(1) DEFAULT '0' COMMENT '订单类型（0售出单 1预订单）',
  `member_id` bigint DEFAULT NULL COMMENT '会员ID',
  `store_id` bigint NOT NULL COMMENT '门店ID',
  `total_amount` decimal(10,2) DEFAULT '0.00' COMMENT '出库总金额',
  `pay_type` tinyint(1) DEFAULT NULL COMMENT '支付方式（字典值）',
  `deposit_type` tinyint(1) DEFAULT NULL COMMENT '预订支付类型（0定金 1全款）',
  `deposit_amount` decimal(10,2) DEFAULT '0.00' COMMENT '预订定金金额',
  `balance_amount` decimal(10,2) DEFAULT '0.00' COMMENT '预订尾款金额',
  `balance_pay_type` tinyint(1) DEFAULT NULL COMMENT '尾款支付方式（字典值）',
  `create_by` varchar(64) DEFAULT '' COMMENT '创建者',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(64) DEFAULT '' COMMENT '更新者',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  `remark` varchar(500) DEFAULT NULL COMMENT '备注',
  `business_type` tinyint(1) DEFAULT '0' COMMENT '业务场景（0售出 1预订 2退货 3取消 4交货 5库存删除 6库存迁移）',
  `process_status` tinyint(1) DEFAULT '0' COMMENT '流程节点（0待审核 1已审核 2已驳回 3已撤销）',
  PRIMARY KEY (`id`),
  UNIQUE KEY `idx_order_number` (`order_number`) COMMENT '出库单号唯一索引',
  KEY `idx_store_id` (`store_id`) COMMENT '门店ID索引',
  KEY `idx_inventory_out_order_member_id` (`member_id`),
  CONSTRAINT `fk_inventory_out_order_member` FOREIGN KEY (`member_id`) REFERENCES `member` (`member_id`) ON DELETE SET NULL ON UPDATE CASCADE,
  CONSTRAINT `fk_out_order_store` FOREIGN KEY (`store_id`) REFERENCES `retail_store` (`id`) ON DELETE RESTRICT ON UPDATE CASCADE
) ENGINE=InnoDB AUTO_INCREMENT=37 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='出库单表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `inventory_out_order`
--

LOCK TABLES `inventory_out_order` WRITE;
/*!40000 ALTER TABLE `inventory_out_order` DISABLE KEYS */;
INSERT INTO `inventory_out_order` VALUES (27,'OUT17459929352838567',1,2504277155,1,4980.00,1,0,2000.00,0.00,NULL,'store1','2025-04-30 14:02:15','store1','2025-04-30 14:02:46',NULL,1,3),(28,'OUT17459930048073386',1,2504277155,1,4980.00,1,0,2000.00,0.00,NULL,'store1','2025-04-30 14:03:24','finance','2025-04-30 14:06:40','11',1,2),(29,'OUT17459932343271517',1,2504277155,1,4980.00,1,0,2000.00,0.00,NULL,'store1','2025-04-30 14:07:14','store1','2025-04-30 14:21:48','22',3,1),(30,'OUT17459969651128145',1,2504277155,1,4980.00,1,0,2000.00,0.00,NULL,'store1','2025-04-30 15:09:25','finance','2025-04-30 15:19:00','33',3,2),(31,'OUT17459976299327157',1,2504277155,1,4980.00,1,0,1980.00,0.00,NULL,'store1','2025-04-30 15:20:29','finance','2025-04-30 15:21:22','44',3,1),(32,'OUT17459982994626765',1,2504277155,1,4980.00,1,0,1900.00,0.00,NULL,'store1','2025-04-30 15:31:39','finance','2025-04-30 15:34:43','55',3,1),(33,'OUT17459997557648342',1,2504277155,1,4980.00,1,0,2100.00,0.00,NULL,'store1','2025-04-30 15:55:55','finance','2025-04-30 15:56:50',NULL,3,1),(34,'OUT17459998454002603',1,2504277155,1,4980.00,1,0,1980.00,3000.00,2,'store1','2025-04-30 15:57:25','finance','2025-04-30 16:50:27','null | 交货备注: 77交货',4,1),(35,'OUT17460036115473658',1,2504277155,1,4980.00,1,0,1999.00,2981.00,3,'store1','2025-04-30 17:00:11','finance','2025-04-30 17:17:51','null | 交货备注: 88交货',4,1),(36,'OUT17465028959854159',0,2504277155,1,3300.00,1,NULL,0.00,0.00,NULL,'store1','2025-05-06 03:41:35','finance','2025-05-06 03:57:10',NULL,2,2);
/*!40000 ALTER TABLE `inventory_out_order` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `inventory_stock`
--

DROP TABLE IF EXISTS `inventory_stock`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `inventory_stock` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '库存ID',
  `product_id` bigint NOT NULL COMMENT '商品ID',
  `channel_id` bigint NOT NULL COMMENT '渠道ID',
  `store_id` bigint NOT NULL COMMENT '门店ID',
  `counter_id` bigint DEFAULT NULL COMMENT '柜台ID',
  `batch_number` varchar(50) NOT NULL COMMENT '批次号',
  `purchase_price` decimal(10,2) NOT NULL COMMENT '进货价',
  `quantity` int NOT NULL DEFAULT '0' COMMENT '库存数量',
  `is_futures` tinyint(1) DEFAULT '0' COMMENT '期货状态',
  `create_by` varchar(64) DEFAULT '' COMMENT '创建者',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(64) DEFAULT '' COMMENT '更新者',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  `remark` varchar(500) DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`id`),
  UNIQUE KEY `idx_stock_unique` (`product_id`,`channel_id`,`store_id`,`counter_id`,`batch_number`) COMMENT '库存唯一索引',
  KEY `idx_product_id` (`product_id`) COMMENT '商品ID索引',
  KEY `idx_channel_id` (`channel_id`) COMMENT '渠道ID索引',
  KEY `idx_store_id` (`store_id`) COMMENT '门店ID索引',
  KEY `idx_counter_id` (`counter_id`) COMMENT '柜台ID索引',
  KEY `idx_batch_number` (`batch_number`) COMMENT '批次号索引',
  CONSTRAINT `fk_stock_channel` FOREIGN KEY (`channel_id`) REFERENCES `retail_channel` (`id`) ON DELETE RESTRICT ON UPDATE CASCADE,
  CONSTRAINT `fk_stock_counter` FOREIGN KEY (`counter_id`) REFERENCES `retail_counter` (`id`) ON DELETE RESTRICT ON UPDATE CASCADE,
  CONSTRAINT `fk_stock_product` FOREIGN KEY (`product_id`) REFERENCES `retail_products` (`id`) ON DELETE RESTRICT ON UPDATE CASCADE,
  CONSTRAINT `fk_stock_store` FOREIGN KEY (`store_id`) REFERENCES `retail_store` (`id`) ON DELETE RESTRICT ON UPDATE CASCADE
) ENGINE=InnoDB AUTO_INCREMENT=17 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='库存表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `inventory_stock`
--

LOCK TABLES `inventory_stock` WRITE;
/*!40000 ALTER TABLE `inventory_stock` DISABLE KEYS */;
INSERT INTO `inventory_stock` VALUES (11,1,1,1,1,'c02dda4a18',2800.00,0,0,'finance','2025-04-29 12:20:32','finance','2025-04-30 17:11:12',NULL),(12,2,1,2,2,'c02dda4a18',2900.00,5,0,'finance','2025-04-29 12:20:32','finance','2025-04-29 12:21:22',NULL),(13,1,1,2,2,'75baad55fd',2700.00,5,0,'finance','2025-04-29 12:21:22','',NULL,NULL),(14,2,1,1,1,'40d6bea597',2800.00,9,0,'finance','2025-04-29 12:21:22','store1','2025-05-06 03:41:36',NULL),(15,1,2,1,1,'57f8b210a3',2600.00,1,1,'finance','2025-04-29 12:22:59','finance','2025-04-30 17:11:12',NULL),(16,2,2,1,1,'57f8b210a3',2600.00,5,1,'finance','2025-04-29 12:22:59','',NULL,NULL);
/*!40000 ALTER TABLE `inventory_stock` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `member`
--

DROP TABLE IF EXISTS `member`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `member` (
  `member_id` bigint NOT NULL AUTO_INCREMENT COMMENT '会员ID',
  `member_name` varchar(50) NOT NULL COMMENT '会员姓名',
  `phone_number` varchar(11) NOT NULL COMMENT '手机号码',
  `gender` tinyint(1) DEFAULT '0' COMMENT '性别（0女 1男）',
  `birthday` date DEFAULT NULL COMMENT '生日',
  `total_amount` decimal(10,2) DEFAULT '0.00' COMMENT '累计消费金额',
  `member_level` int DEFAULT '1' COMMENT '会员等级（1普通会员 2银卡会员 3金卡会员 4VIP会员 5钻石会员）',
  `create_by` varchar(64) DEFAULT '' COMMENT '创建者',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(64) DEFAULT '' COMMENT '更新者',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  `remark` varchar(500) DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`member_id`),
  UNIQUE KEY `idx_phone_number` (`phone_number`) COMMENT '手机号码唯一索引'
) ENGINE=InnoDB AUTO_INCREMENT=2504277196 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='会员表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `member`
--

LOCK TABLES `member` WRITE;
/*!40000 ALTER TABLE `member` DISABLE KEYS */;
INSERT INTO `member` VALUES (2504277155,'张先生','13344445555',1,'1996-01-01',13260.00,4,'store1','2025-04-27 16:37:12','','2025-05-06 03:42:03',NULL),(2504277195,'张女士','13355556666',0,'1996-01-01',0.00,1,'store1','2025-04-27 17:56:17','','2025-04-28 16:56:21',NULL);
/*!40000 ALTER TABLE `member` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `member_level`
--

DROP TABLE IF EXISTS `member_level`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `member_level` (
  `level_id` bigint NOT NULL AUTO_INCREMENT COMMENT '等级ID',
  `level_name` varchar(50) NOT NULL COMMENT '等级名称',
  `min_amount` decimal(10,2) NOT NULL COMMENT '最小消费金额',
  `max_amount` decimal(10,2) NOT NULL COMMENT '最大消费金额',
  `discount_rate` decimal(3,2) DEFAULT '1.00' COMMENT '折扣率',
  `tag_type` varchar(20) DEFAULT '' COMMENT '标签类型',
  `status` tinyint(1) DEFAULT '0' COMMENT '状态（0停用 1正常）',
  `create_by` varchar(64) DEFAULT '' COMMENT '创建者',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(64) DEFAULT '' COMMENT '更新者',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  `remark` varchar(500) DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`level_id`)
) ENGINE=InnoDB AUTO_INCREMENT=6 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='会员等级表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `member_level`
--

LOCK TABLES `member_level` WRITE;
/*!40000 ALTER TABLE `member_level` DISABLE KEYS */;
INSERT INTO `member_level` VALUES (1,'普通会员',0.00,999.99,1.00,'',1,'admin','2025-04-27 03:55:44','',NULL,'消费0-999元'),(2,'银卡会员',1000.00,4999.99,1.00,'info',1,'admin','2025-04-27 03:55:44','',NULL,'消费1000-4999元'),(3,'金卡会员',5000.00,9999.99,1.00,'warning',1,'admin','2025-04-27 03:55:44','',NULL,'消费5000-9999元'),(4,'VIP会员',10000.00,49999.99,1.00,'success',1,'admin','2025-04-27 03:55:44','',NULL,'消费10000-49999元'),(5,'钻石会员',50000.00,999999.99,1.00,'danger',1,'admin','2025-04-27 03:55:44','',NULL,'消费50000元以上');
/*!40000 ALTER TABLE `member_level` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `retail_channel`
--

DROP TABLE IF EXISTS `retail_channel`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `retail_channel` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '渠道ID',
  `channel_name` varchar(100) NOT NULL COMMENT '渠道名称',
  `user_id` varchar(100) DEFAULT NULL COMMENT '授权用户ID，多个用逗号分隔',
  `user_name` varchar(100) DEFAULT NULL COMMENT '授权用户名称，多个用逗号分隔',
  `create_by` varchar(64) DEFAULT '' COMMENT '创建者',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(64) DEFAULT '' COMMENT '更新者',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  `remark` varchar(500) DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=4 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='渠道表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `retail_channel`
--

LOCK TABLES `retail_channel` WRITE;
/*!40000 ALTER TABLE `retail_channel` DISABLE KEYS */;
INSERT INTO `retail_channel` VALUES (1,'海外采购','','','finance','2025-04-25 10:06:43','',NULL,NULL),(2,'供应商1','104','供应商1','finance','2025-04-25 10:07:03','',NULL,NULL),(3,'供应商2','105','供应商2','finance','2025-04-25 10:07:11','',NULL,NULL);
/*!40000 ALTER TABLE `retail_channel` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `retail_counter`
--

DROP TABLE IF EXISTS `retail_counter`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `retail_counter` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '柜台ID',
  `store_id` bigint NOT NULL COMMENT '门店ID',
  `counter_name` varchar(100) NOT NULL COMMENT '柜台名称',
  `create_by` varchar(64) DEFAULT '' COMMENT '创建者',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(64) DEFAULT '' COMMENT '更新者',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  `remark` varchar(500) DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`id`),
  KEY `idx_store_id` (`store_id`) COMMENT '门店ID索引',
  CONSTRAINT `fk_counter_store` FOREIGN KEY (`store_id`) REFERENCES `retail_store` (`id`) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE=InnoDB AUTO_INCREMENT=3 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='柜台表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `retail_counter`
--

LOCK TABLES `retail_counter` WRITE;
/*!40000 ALTER TABLE `retail_counter` DISABLE KEYS */;
INSERT INTO `retail_counter` VALUES (1,1,'AA','finance','2025-04-25 10:06:12','',NULL,NULL),(2,2,'BB','finance','2025-04-25 10:06:20','',NULL,NULL);
/*!40000 ALTER TABLE `retail_counter` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `retail_products`
--

DROP TABLE IF EXISTS `retail_products`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `retail_products` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `product_number` varchar(50) NOT NULL COMMENT '货号',
  `product_name` varchar(100) NOT NULL COMMENT '商品名称',
  `product_image` varchar(255) DEFAULT NULL COMMENT '商品图',
  `brand` varchar(50) DEFAULT NULL COMMENT '品牌',
  `category` varchar(50) DEFAULT NULL COMMENT '品类',
  `specification` varchar(100) DEFAULT NULL COMMENT '规格',
  `color` varchar(50) DEFAULT NULL COMMENT '颜色',
  `material` varchar(50) DEFAULT NULL COMMENT '材质',
  `origin` varchar(50) DEFAULT NULL COMMENT '原产地',
  `standard` varchar(100) DEFAULT NULL COMMENT '执行标准',
  `safety_category` varchar(50) DEFAULT NULL COMMENT '安全类别',
  `product_grade` varchar(50) DEFAULT NULL COMMENT '产品等级',
  `reference_price` decimal(10,2) DEFAULT NULL COMMENT '国内参考价',
  `retail_price` decimal(10,2) DEFAULT NULL COMMENT '门店零售价',
  `create_by` varchar(64) DEFAULT '' COMMENT '创建者',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(64) DEFAULT '' COMMENT '更新者',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  `remark` varchar(500) DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`id`),
  UNIQUE KEY `idx_product_number` (`product_number`) COMMENT '货号唯一索引'
) ENGINE=InnoDB AUTO_INCREMENT=3 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='商品表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `retail_products`
--

LOCK TABLES `retail_products` WRITE;
/*!40000 ALTER TABLE `retail_products` DISABLE KEYS */;
INSERT INTO `retail_products` VALUES (1,'2736QBAF4','COACH 背提包-2525001','/profile/upload/2025/04/25/bag1_20250425180819A001.jpg','COACH','背提包','35x15x44cm','炭黑色/黑色','人造革配牛皮革配织物','见内标','QB/T 1333-2018','C类','合格品',8300.00,4980.00,'finance','2025-04-25 10:09:44','',NULL,NULL),(2,'78277IMCBI','COACH 背提包-2524996','/profile/upload/2025/04/27/bag2_20250427092057A001.jpg','COACH','背提包','19.5x10x13.5cm','卡其色/黑色','人造革配剖层牛皮革','见内标','QB/T 1333-2018','C类','合格品',5500.00,3300.00,'boss','2025-04-27 01:22:14','',NULL,NULL);
/*!40000 ALTER TABLE `retail_products` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `retail_store`
--

DROP TABLE IF EXISTS `retail_store`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `retail_store` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '门店ID',
  `store_name` varchar(100) NOT NULL COMMENT '门店名称',
  `store_address` varchar(255) DEFAULT NULL COMMENT '门店地址',
  `store_contact` varchar(50) DEFAULT NULL COMMENT '联系方式',
  `user_id` varchar(100) DEFAULT NULL COMMENT '授权用户ID，多个用逗号分隔',
  `user_name` varchar(100) DEFAULT NULL COMMENT '授权用户名称，多个用逗号分隔',
  `create_by` varchar(64) DEFAULT '' COMMENT '创建者',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(64) DEFAULT '' COMMENT '更新者',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  `remark` varchar(500) DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=3 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='门店表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `retail_store`
--

LOCK TABLES `retail_store` WRITE;
/*!40000 ALTER TABLE `retail_store` DISABLE KEYS */;
INSERT INTO `retail_store` VALUES (1,'北京门店','北京市故宫1','13344445555','102','门店1','finance','2025-04-25 09:09:56','finance','2025-04-25 09:15:41',NULL),(2,'成都门店','成都武侯区2','15566667777','103','门店2','finance','2025-04-25 09:12:51','finance','2025-04-25 09:15:44',NULL);
/*!40000 ALTER TABLE `retail_store` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `sys_config`
--

DROP TABLE IF EXISTS `sys_config`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `sys_config` (
  `config_id` int NOT NULL AUTO_INCREMENT COMMENT '参数主键',
  `config_name` varchar(100) DEFAULT '' COMMENT '参数名称',
  `config_key` varchar(100) DEFAULT '' COMMENT '参数键名',
  `config_value` varchar(500) DEFAULT '' COMMENT '参数键值',
  `config_type` char(1) DEFAULT 'N' COMMENT '系统内置（Y是 N否）',
  `create_by` varchar(64) DEFAULT '' COMMENT '创建者',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(64) DEFAULT '' COMMENT '更新者',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  `remark` varchar(500) DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`config_id`)
) ENGINE=InnoDB AUTO_INCREMENT=100 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='参数配置表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `sys_config`
--

LOCK TABLES `sys_config` WRITE;
/*!40000 ALTER TABLE `sys_config` DISABLE KEYS */;
INSERT INTO `sys_config` VALUES (1,'主框架页-默认皮肤样式名称','sys.index.skinName','skin-blue','Y','admin','2025-04-25 08:01:03','',NULL,'蓝色 skin-blue、绿色 skin-green、紫色 skin-purple、红色 skin-red、黄色 skin-yellow'),(2,'用户管理-账号初始密码','sys.user.initPassword','123456','Y','admin','2025-04-25 08:01:03','',NULL,'初始化密码 123456'),(3,'主框架页-侧边栏主题','sys.index.sideTheme','theme-dark','Y','admin','2025-04-25 08:01:03','',NULL,'深色主题theme-dark，浅色主题theme-light'),(4,'账号自助-验证码开关','sys.account.captchaEnabled','true','Y','admin','2025-04-25 08:01:03','',NULL,'是否开启验证码功能（true开启，false关闭）'),(5,'账号自助-是否开启用户注册功能','sys.account.registerUser','false','Y','admin','2025-04-25 08:01:03','',NULL,'是否开启注册用户功能（true开启，false关闭）'),(6,'用户登录-黑名单列表','sys.login.blackIPList','','Y','admin','2025-04-25 08:01:03','',NULL,'设置登录IP黑名单限制，多个匹配项以;分隔，支持匹配（*通配、网段）');
/*!40000 ALTER TABLE `sys_config` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `sys_dept`
--

DROP TABLE IF EXISTS `sys_dept`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `sys_dept` (
  `dept_id` bigint NOT NULL AUTO_INCREMENT COMMENT '部门id',
  `parent_id` bigint DEFAULT '0' COMMENT '父部门id',
  `ancestors` varchar(50) DEFAULT '' COMMENT '祖级列表',
  `dept_name` varchar(30) DEFAULT '' COMMENT '部门名称',
  `order_num` int DEFAULT '0' COMMENT '显示顺序',
  `leader` varchar(20) DEFAULT NULL COMMENT '负责人',
  `phone` varchar(11) DEFAULT NULL COMMENT '联系电话',
  `email` varchar(50) DEFAULT NULL COMMENT '邮箱',
  `status` char(1) DEFAULT '0' COMMENT '部门状态（0正常 1停用）',
  `del_flag` char(1) DEFAULT '0' COMMENT '删除标志（0代表存在 2代表删除）',
  `create_by` varchar(64) DEFAULT '' COMMENT '创建者',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(64) DEFAULT '' COMMENT '更新者',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`dept_id`)
) ENGINE=InnoDB AUTO_INCREMENT=202 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='部门表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `sys_dept`
--

LOCK TABLES `sys_dept` WRITE;
/*!40000 ALTER TABLE `sys_dept` DISABLE KEYS */;
INSERT INTO `sys_dept` VALUES (100,0,'0','奢胜',0,'boss','15888888888','<EMAIL>','0','0','admin','2025-04-25 08:01:00','admin','2025-04-25 08:53:25'),(101,100,'0,100','总部',1,'boss','15888888888','<EMAIL>','0','0','admin','2025-04-25 08:01:00','admin','2025-04-25 08:53:45'),(200,100,'0,100','门店',2,NULL,NULL,NULL,'0','0','admin','2025-04-25 08:57:02','',NULL),(201,100,'0,100','供应商',3,NULL,NULL,NULL,'0','0','admin','2025-04-25 08:57:18','',NULL);
/*!40000 ALTER TABLE `sys_dept` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `sys_dict_data`
--

DROP TABLE IF EXISTS `sys_dict_data`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `sys_dict_data` (
  `dict_code` bigint NOT NULL AUTO_INCREMENT COMMENT '字典编码',
  `dict_sort` int DEFAULT '0' COMMENT '字典排序',
  `dict_label` varchar(100) DEFAULT '' COMMENT '字典标签',
  `dict_value` varchar(100) DEFAULT '' COMMENT '字典键值',
  `dict_type` varchar(100) DEFAULT '' COMMENT '字典类型',
  `css_class` varchar(100) DEFAULT NULL COMMENT '样式属性（其他样式扩展）',
  `list_class` varchar(100) DEFAULT NULL COMMENT '表格回显样式',
  `is_default` char(1) DEFAULT 'N' COMMENT '是否默认（Y是 N否）',
  `status` char(1) DEFAULT '0' COMMENT '状态（0正常 1停用）',
  `create_by` varchar(64) DEFAULT '' COMMENT '创建者',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(64) DEFAULT '' COMMENT '更新者',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  `remark` varchar(500) DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`dict_code`)
) ENGINE=InnoDB AUTO_INCREMENT=103 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='字典数据表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `sys_dict_data`
--

LOCK TABLES `sys_dict_data` WRITE;
/*!40000 ALTER TABLE `sys_dict_data` DISABLE KEYS */;
INSERT INTO `sys_dict_data` VALUES (1,1,'男','1','sys_user_sex','','','Y','0','admin','2025-04-25 08:01:03','admin','2025-04-27 08:20:18','性别男'),(2,0,'女','0','sys_user_sex','','','N','0','admin','2025-04-25 08:01:03','admin','2025-04-27 08:20:42','性别女'),(4,1,'显示','0','sys_show_hide','','primary','Y','0','admin','2025-04-25 08:01:03','',NULL,'显示菜单'),(5,2,'隐藏','1','sys_show_hide','','danger','N','0','admin','2025-04-25 08:01:03','',NULL,'隐藏菜单'),(6,1,'正常','0','sys_normal_disable','','primary','Y','0','admin','2025-04-25 08:01:03','',NULL,'正常状态'),(7,2,'停用','1','sys_normal_disable','','danger','N','0','admin','2025-04-25 08:01:03','',NULL,'停用状态'),(8,1,'正常','0','sys_job_status','','primary','Y','0','admin','2025-04-25 08:01:03','',NULL,'正常状态'),(9,2,'暂停','1','sys_job_status','','danger','N','0','admin','2025-04-25 08:01:03','',NULL,'停用状态'),(10,1,'默认','DEFAULT','sys_job_group','','','Y','0','admin','2025-04-25 08:01:03','',NULL,'默认分组'),(11,2,'系统','SYSTEM','sys_job_group','','','N','0','admin','2025-04-25 08:01:03','',NULL,'系统分组'),(12,1,'是','Y','sys_yes_no','','primary','Y','0','admin','2025-04-25 08:01:03','',NULL,'系统默认是'),(13,2,'否','N','sys_yes_no','','danger','N','0','admin','2025-04-25 08:01:03','',NULL,'系统默认否'),(14,1,'通知','1','sys_notice_type','','warning','Y','0','admin','2025-04-25 08:01:03','',NULL,'通知'),(15,2,'公告','2','sys_notice_type','','success','N','0','admin','2025-04-25 08:01:03','',NULL,'公告'),(16,1,'正常','0','sys_notice_status','','primary','Y','0','admin','2025-04-25 08:01:03','',NULL,'正常状态'),(17,2,'关闭','1','sys_notice_status','','danger','N','0','admin','2025-04-25 08:01:03','',NULL,'关闭状态'),(18,99,'其他','0','sys_oper_type','','info','N','0','admin','2025-04-25 08:01:03','',NULL,'其他操作'),(19,1,'新增','1','sys_oper_type','','info','N','0','admin','2025-04-25 08:01:03','',NULL,'新增操作'),(20,2,'修改','2','sys_oper_type','','info','N','0','admin','2025-04-25 08:01:03','',NULL,'修改操作'),(21,3,'删除','3','sys_oper_type','','danger','N','0','admin','2025-04-25 08:01:03','',NULL,'删除操作'),(22,4,'授权','4','sys_oper_type','','primary','N','0','admin','2025-04-25 08:01:03','',NULL,'授权操作'),(23,5,'导出','5','sys_oper_type','','warning','N','0','admin','2025-04-25 08:01:03','',NULL,'导出操作'),(24,6,'导入','6','sys_oper_type','','warning','N','0','admin','2025-04-25 08:01:03','',NULL,'导入操作'),(25,7,'强退','7','sys_oper_type','','danger','N','0','admin','2025-04-25 08:01:03','',NULL,'强退操作'),(26,8,'生成代码','8','sys_oper_type','','warning','N','0','admin','2025-04-25 08:01:03','',NULL,'生成操作'),(27,9,'清空数据','9','sys_oper_type','','danger','N','0','admin','2025-04-25 08:01:03','',NULL,'清空操作'),(28,1,'成功','0','sys_common_status','','primary','N','0','admin','2025-04-25 08:01:03','',NULL,'正常状态'),(29,2,'失败','1','sys_common_status','','danger','N','0','admin','2025-04-25 08:01:03','',NULL,'停用状态'),(100,1,'支付宝','1','retail_pay_type',NULL,'primary','N','0','boss','2025-04-27 01:27:14','boss','2025-04-27 01:27:48',NULL),(101,2,'微信','2','retail_pay_type',NULL,'success','N','0','boss','2025-04-27 01:27:23','boss','2025-04-27 01:27:39',NULL),(102,3,'银行卡','3','retail_pay_type',NULL,'info','N','0','boss','2025-04-27 01:28:05','',NULL,NULL);
/*!40000 ALTER TABLE `sys_dict_data` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `sys_dict_type`
--

DROP TABLE IF EXISTS `sys_dict_type`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `sys_dict_type` (
  `dict_id` bigint NOT NULL AUTO_INCREMENT COMMENT '字典主键',
  `dict_name` varchar(100) DEFAULT '' COMMENT '字典名称',
  `dict_type` varchar(100) DEFAULT '' COMMENT '字典类型',
  `status` char(1) DEFAULT '0' COMMENT '状态（0正常 1停用）',
  `create_by` varchar(64) DEFAULT '' COMMENT '创建者',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(64) DEFAULT '' COMMENT '更新者',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  `remark` varchar(500) DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`dict_id`),
  UNIQUE KEY `dict_type` (`dict_type`)
) ENGINE=InnoDB AUTO_INCREMENT=101 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='字典类型表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `sys_dict_type`
--

LOCK TABLES `sys_dict_type` WRITE;
/*!40000 ALTER TABLE `sys_dict_type` DISABLE KEYS */;
INSERT INTO `sys_dict_type` VALUES (1,'用户性别','sys_user_sex','0','admin','2025-04-25 08:01:02','',NULL,'用户性别列表'),(2,'菜单状态','sys_show_hide','0','admin','2025-04-25 08:01:02','',NULL,'菜单状态列表'),(3,'系统开关','sys_normal_disable','0','admin','2025-04-25 08:01:02','',NULL,'系统开关列表'),(4,'任务状态','sys_job_status','0','admin','2025-04-25 08:01:02','',NULL,'任务状态列表'),(5,'任务分组','sys_job_group','0','admin','2025-04-25 08:01:02','',NULL,'任务分组列表'),(6,'系统是否','sys_yes_no','0','admin','2025-04-25 08:01:03','',NULL,'系统是否列表'),(7,'通知类型','sys_notice_type','0','admin','2025-04-25 08:01:03','',NULL,'通知类型列表'),(8,'通知状态','sys_notice_status','0','admin','2025-04-25 08:01:03','',NULL,'通知状态列表'),(9,'操作类型','sys_oper_type','0','admin','2025-04-25 08:01:03','',NULL,'操作类型列表'),(10,'系统状态','sys_common_status','0','admin','2025-04-25 08:01:03','',NULL,'登录状态列表'),(100,'支付方式','retail_pay_type','0','boss','2025-04-27 01:26:49','',NULL,'客户支付方式');
/*!40000 ALTER TABLE `sys_dict_type` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `sys_job`
--

DROP TABLE IF EXISTS `sys_job`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `sys_job` (
  `job_id` bigint NOT NULL AUTO_INCREMENT COMMENT '任务ID',
  `job_name` varchar(64) NOT NULL DEFAULT '' COMMENT '任务名称',
  `job_group` varchar(64) NOT NULL DEFAULT 'DEFAULT' COMMENT '任务组名',
  `invoke_target` varchar(500) NOT NULL COMMENT '调用目标字符串',
  `cron_expression` varchar(255) DEFAULT '' COMMENT 'cron执行表达式',
  `misfire_policy` varchar(20) DEFAULT '3' COMMENT '计划执行错误策略（1立即执行 2执行一次 3放弃执行）',
  `concurrent` char(1) DEFAULT '1' COMMENT '是否并发执行（0允许 1禁止）',
  `status` char(1) DEFAULT '0' COMMENT '状态（0正常 1暂停）',
  `create_by` varchar(64) DEFAULT '' COMMENT '创建者',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(64) DEFAULT '' COMMENT '更新者',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  `remark` varchar(500) DEFAULT '' COMMENT '备注信息',
  PRIMARY KEY (`job_id`,`job_name`,`job_group`)
) ENGINE=InnoDB AUTO_INCREMENT=100 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='定时任务调度表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `sys_job`
--

LOCK TABLES `sys_job` WRITE;
/*!40000 ALTER TABLE `sys_job` DISABLE KEYS */;
INSERT INTO `sys_job` VALUES (1,'系统默认（无参）','DEFAULT','ryTask.ryNoParams','0/10 * * * * ?','3','1','1','admin','2025-04-25 08:01:03','',NULL,''),(2,'系统默认（有参）','DEFAULT','ryTask.ryParams(\'ry\')','0/15 * * * * ?','3','1','1','admin','2025-04-25 08:01:03','',NULL,''),(3,'系统默认（多参）','DEFAULT','ryTask.ryMultipleParams(\'ry\', true, 2000L, 316.50D, 100)','0/20 * * * * ?','3','1','1','admin','2025-04-25 08:01:03','',NULL,'');
/*!40000 ALTER TABLE `sys_job` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `sys_job_log`
--

DROP TABLE IF EXISTS `sys_job_log`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `sys_job_log` (
  `job_log_id` bigint NOT NULL AUTO_INCREMENT COMMENT '任务日志ID',
  `job_name` varchar(64) NOT NULL COMMENT '任务名称',
  `job_group` varchar(64) NOT NULL COMMENT '任务组名',
  `invoke_target` varchar(500) NOT NULL COMMENT '调用目标字符串',
  `job_message` varchar(500) DEFAULT NULL COMMENT '日志信息',
  `status` char(1) DEFAULT '0' COMMENT '执行状态（0正常 1失败）',
  `exception_info` varchar(2000) DEFAULT '' COMMENT '异常信息',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  PRIMARY KEY (`job_log_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='定时任务调度日志表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `sys_job_log`
--

LOCK TABLES `sys_job_log` WRITE;
/*!40000 ALTER TABLE `sys_job_log` DISABLE KEYS */;
/*!40000 ALTER TABLE `sys_job_log` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `sys_logininfor`
--

DROP TABLE IF EXISTS `sys_logininfor`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `sys_logininfor` (
  `info_id` bigint NOT NULL AUTO_INCREMENT COMMENT '访问ID',
  `user_name` varchar(50) DEFAULT '' COMMENT '用户账号',
  `ipaddr` varchar(128) DEFAULT '' COMMENT '登录IP地址',
  `login_location` varchar(255) DEFAULT '' COMMENT '登录地点',
  `browser` varchar(50) DEFAULT '' COMMENT '浏览器类型',
  `os` varchar(50) DEFAULT '' COMMENT '操作系统',
  `status` char(1) DEFAULT '0' COMMENT '登录状态（0成功 1失败）',
  `msg` varchar(255) DEFAULT '' COMMENT '提示消息',
  `login_time` datetime DEFAULT NULL COMMENT '访问时间',
  PRIMARY KEY (`info_id`),
  KEY `idx_sys_logininfor_s` (`status`),
  KEY `idx_sys_logininfor_lt` (`login_time`)
) ENGINE=InnoDB AUTO_INCREMENT=181 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='系统访问记录';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `sys_logininfor`
--

LOCK TABLES `sys_logininfor` WRITE;
/*!40000 ALTER TABLE `sys_logininfor` DISABLE KEYS */;
INSERT INTO `sys_logininfor` VALUES (100,'admin','127.0.0.1','内网IP','Chrome 13','Mac OS X','0','登录成功','2025-04-25 08:30:29'),(101,'boss','127.0.0.1','内网IP','Chrome 13','Mac OS X','1','验证码已失效','2025-04-25 09:04:14'),(102,'boss','127.0.0.1','内网IP','Chrome 13','Mac OS X','0','登录成功','2025-04-25 09:04:19'),(103,'boss','127.0.0.1','内网IP','Chrome 13','Mac OS X','0','退出成功','2025-04-25 09:05:36'),(104,'finance','127.0.0.1','内网IP','Chrome 13','Mac OS X','0','登录成功','2025-04-25 09:05:44'),(105,'admin','127.0.0.1','内网IP','Chrome 13','Mac OS X','0','退出成功','2025-04-25 10:10:44'),(106,'boss','127.0.0.1','内网IP','Chrome 13','Mac OS X','0','登录成功','2025-04-25 10:10:51'),(107,'admin','127.0.0.1','内网IP','Chrome 13','Mac OS X','0','登录成功','2025-04-27 01:11:06'),(108,'boss','127.0.0.1','内网IP','Chrome 13','Mac OS X','0','登录成功','2025-04-27 01:13:48'),(109,'finance','127.0.0.1','内网IP','Safari','Mac OS X','0','登录成功','2025-04-27 01:15:56'),(110,'storeA','127.0.0.1','内网IP','Safari','Mac OS X','1','用户不存在/密码错误','2025-04-27 01:18:22'),(111,'store1','127.0.0.1','内网IP','Safari','Mac OS X','0','登录成功','2025-04-27 01:18:44'),(112,'admin','127.0.0.1','内网IP','Chrome 13','Mac OS X','0','登录成功','2025-04-27 02:18:46'),(113,'store1','127.0.0.1','内网IP','Safari','Mac OS X','0','登录成功','2025-04-27 02:23:23'),(114,'boss','127.0.0.1','内网IP','Chrome 13','Mac OS X','0','登录成功','2025-04-27 03:02:02'),(115,'admin','127.0.0.1','内网IP','Chrome 13','Mac OS X','0','退出成功','2025-04-27 03:03:01'),(116,'boss','127.0.0.1','内网IP','Chrome 13','Mac OS X','0','登录成功','2025-04-27 03:03:06'),(117,'boss','127.0.0.1','内网IP','Chrome 13','Mac OS X','0','退出成功','2025-04-27 03:03:17'),(118,'finance','127.0.0.1','内网IP','Chrome 13','Mac OS X','0','登录成功','2025-04-27 03:03:31'),(119,'boss','127.0.0.1','内网IP','Chrome 13','Mac OS X','0','退出成功','2025-04-27 03:05:05'),(120,'admin','127.0.0.1','内网IP','Chrome 13','Mac OS X','0','登录成功','2025-04-27 03:05:12'),(121,'admin','127.0.0.1','内网IP','Chrome 13','Mac OS X','0','退出成功','2025-04-27 03:05:31'),(122,'boss','127.0.0.1','内网IP','Chrome 13','Mac OS X','0','登录成功','2025-04-27 03:05:37'),(123,'boss','127.0.0.1','内网IP','Chrome 13','Mac OS X','0','退出成功','2025-04-27 03:09:51'),(124,'admin','127.0.0.1','内网IP','Chrome 13','Mac OS X','0','登录成功','2025-04-27 03:10:02'),(125,'admin','127.0.0.1','内网IP','Chrome 13','Mac OS X','0','退出成功','2025-04-27 03:11:32'),(126,'boss','127.0.0.1','内网IP','Chrome 13','Mac OS X','0','登录成功','2025-04-27 03:11:37'),(127,'boss','127.0.0.1','内网IP','Chrome 13','Mac OS X','0','退出成功','2025-04-27 03:58:42'),(128,'admin','127.0.0.1','内网IP','Chrome 13','Mac OS X','1','验证码错误','2025-04-27 03:58:47'),(129,'admin','127.0.0.1','内网IP','Chrome 13','Mac OS X','0','登录成功','2025-04-27 03:58:49'),(130,'admin','127.0.0.1','内网IP','Chrome 13','Mac OS X','0','登录成功','2025-04-27 06:36:52'),(131,'finance','127.0.0.1','内网IP','Chrome 13','Mac OS X','0','登录成功','2025-04-27 06:37:16'),(132,'store1','127.0.0.1','内网IP','Safari','Mac OS X','0','登录成功','2025-04-27 06:37:41'),(133,'admin','127.0.0.1','内网IP','Chrome 13','Mac OS X','0','登录成功','2025-04-27 17:49:24'),(134,'boss','127.0.0.1','内网IP','Chrome 13','Mac OS X','0','登录成功','2025-04-28 09:11:29'),(135,'finance','127.0.0.1','内网IP','Chrome 13','Mac OS X','0','登录成功','2025-04-28 09:12:18'),(136,'store1','127.0.0.1','内网IP','Safari','Mac OS X','0','登录成功','2025-04-28 09:12:54'),(137,'admin','127.0.0.1','内网IP','Chrome 13','Mac OS X','1','用户不存在/密码错误','2025-04-28 13:40:24'),(138,'admin','127.0.0.1','内网IP','Chrome 13','Mac OS X','1','用户不存在/密码错误','2025-04-28 13:40:31'),(139,'admin','127.0.0.1','内网IP','Chrome 13','Mac OS X','0','登录成功','2025-04-28 13:40:37'),(140,'finance','127.0.0.1','内网IP','Chrome 13','Mac OS X','0','登录成功','2025-04-28 13:45:45'),(141,'store1','127.0.0.1','内网IP','Safari','Mac OS X','1','用户不存在/密码错误','2025-04-28 13:49:05'),(142,'store1','127.0.0.1','内网IP','Safari','Mac OS X','0','登录成功','2025-04-28 13:49:11'),(143,'store1','127.0.0.1','内网IP','Safari','Mac OS X','0','退出成功','2025-04-28 14:14:40'),(144,'store1','127.0.0.1','内网IP','Safari','Mac OS X','0','登录成功','2025-04-28 14:14:51'),(145,'store2','127.0.0.1','内网IP','Safari','Mac OS X','0','登录成功','2025-04-28 14:26:32'),(146,'admin','127.0.0.1','内网IP','Chrome 13','Mac OS X','0','退出成功','2025-04-28 16:04:56'),(147,'store1','127.0.0.1','内网IP','Chrome 13','Mac OS X','0','登录成功','2025-04-28 16:05:08'),(148,'finance','127.0.0.1','内网IP','Chrome 13','Mac OS X','0','登录成功','2025-04-29 11:30:03'),(149,'store1','127.0.0.1','内网IP','Chrome 13','Mac OS X','0','登录成功','2025-04-29 12:22:07'),(150,'finance','127.0.0.1','内网IP','Chrome 13','Mac OS X','0','登录成功','2025-04-29 14:03:24'),(151,'finance','127.0.0.1','内网IP','Chrome 13','Mac OS X','0','登录成功','2025-04-29 17:30:04'),(152,'finance','127.0.0.1','内网IP','Chrome 13','Mac OS X','0','退出成功','2025-04-29 18:03:37'),(153,'admin','127.0.0.1','内网IP','Chrome 13','Mac OS X','0','登录成功','2025-04-29 18:03:43'),(154,'store1','127.0.0.1','内网IP','Chrome 13','Mac OS X','0','登录成功','2025-04-30 09:23:15'),(155,'finance','127.0.0.1','内网IP','Chrome 13','Mac OS X','0','登录成功','2025-04-30 09:23:25'),(156,'finance','127.0.0.1','内网IP','Chrome 13','Mac OS X','0','退出成功','2025-04-30 09:23:39'),(157,'admin','127.0.0.1','内网IP','Chrome 13','Mac OS X','0','登录成功','2025-04-30 09:23:44'),(158,'store1','127.0.0.1','内网IP','Chrome 13','Mac OS X','0','退出成功','2025-04-30 09:34:43'),(159,'finance','127.0.0.1','内网IP','Chrome 13','Mac OS X','0','登录成功','2025-04-30 09:34:52'),(160,'store1','127.0.0.1','内网IP','Safari','Mac OS X','0','登录成功','2025-04-30 09:55:11'),(161,'finance','127.0.0.1','内网IP','Chrome 13','Mac OS X','0','登录成功','2025-04-30 10:51:43'),(162,'admin','127.0.0.1','内网IP','Chrome 13','Mac OS X','0','退出成功','2025-04-30 10:53:42'),(163,'finance','127.0.0.1','内网IP','Chrome 13','Mac OS X','0','登录成功','2025-04-30 10:53:52'),(164,'finance','127.0.0.1','内网IP','Chrome 13','Mac OS X','0','退出成功','2025-04-30 10:54:05'),(165,'store1','127.0.0.1','内网IP','Chrome 13','Mac OS X','0','登录成功','2025-04-30 10:54:14'),(166,'finance','127.0.0.1','内网IP','Chrome 13','Mac OS X','0','登录成功','2025-04-30 13:43:58'),(167,'finance','127.0.0.1','内网IP','Chrome 13','Mac OS X','0','退出成功','2025-04-30 13:44:09'),(168,'boss','127.0.0.1','内网IP','Chrome 13','Mac OS X','0','登录成功','2025-04-30 13:44:17'),(169,'finance','127.0.0.1','内网IP','Chrome 13','Mac OS X','0','登录成功','2025-04-30 13:44:40'),(170,'store1','127.0.0.1','内网IP','Safari','Mac OS X','0','登录成功','2025-04-30 14:01:16'),(171,'store1','127.0.0.1','内网IP','Safari','Mac OS X','0','登录成功','2025-04-30 15:08:23'),(172,'finance','127.0.0.1','内网IP','Chrome 13','Mac OS X','0','登录成功','2025-04-30 15:10:01'),(173,'finance','127.0.0.1','内网IP','Chrome 13','Mac OS X','0','登录成功','2025-05-06 03:38:33'),(174,'store1','127.0.0.1','内网IP','Chrome 13','Mac OS X','0','登录成功','2025-05-06 03:40:46'),(175,'admin','127.0.0.1','内网IP','Chrome 13','Mac OS X','1','验证码错误','2025-05-06 05:19:32'),(176,'finance','127.0.0.1','内网IP','Chrome 13','Mac OS X','0','登录成功','2025-05-06 05:19:44'),(177,'store1','127.0.0.1','内网IP','Chrome 13','Mac OS X','0','登录成功','2025-05-06 05:21:16'),(178,'finance','127.0.0.1','内网IP','Chrome 13','Mac OS X','0','退出成功','2025-05-06 05:21:54'),(179,'boss','127.0.0.1','内网IP','Chrome 13','Mac OS X','0','登录成功','2025-05-06 05:22:01'),(180,'boss','127.0.0.1','内网IP','Chrome 13','Mac OS X','0','登录成功','2025-05-06 09:40:54');
/*!40000 ALTER TABLE `sys_logininfor` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `sys_menu`
--

DROP TABLE IF EXISTS `sys_menu`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `sys_menu` (
  `menu_id` bigint NOT NULL AUTO_INCREMENT COMMENT '菜单ID',
  `menu_name` varchar(50) NOT NULL COMMENT '菜单名称',
  `parent_id` bigint DEFAULT '0' COMMENT '父菜单ID',
  `order_num` int DEFAULT '0' COMMENT '显示顺序',
  `path` varchar(200) DEFAULT '' COMMENT '路由地址',
  `component` varchar(255) DEFAULT NULL COMMENT '组件路径',
  `query` varchar(255) DEFAULT NULL COMMENT '路由参数',
  `route_name` varchar(50) DEFAULT '' COMMENT '路由名称',
  `is_frame` int DEFAULT '1' COMMENT '是否为外链（0是 1否）',
  `is_cache` int DEFAULT '0' COMMENT '是否缓存（0缓存 1不缓存）',
  `menu_type` char(1) DEFAULT '' COMMENT '菜单类型（M目录 C菜单 F按钮）',
  `visible` char(1) DEFAULT '0' COMMENT '菜单状态（0显示 1隐藏）',
  `status` char(1) DEFAULT '0' COMMENT '菜单状态（0正常 1停用）',
  `perms` varchar(100) DEFAULT NULL COMMENT '权限标识',
  `icon` varchar(100) DEFAULT '#' COMMENT '菜单图标',
  `create_by` varchar(64) DEFAULT '' COMMENT '创建者',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(64) DEFAULT '' COMMENT '更新者',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  `remark` varchar(500) DEFAULT '' COMMENT '备注',
  PRIMARY KEY (`menu_id`)
) ENGINE=InnoDB AUTO_INCREMENT=2067 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='菜单权限表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `sys_menu`
--

LOCK TABLES `sys_menu` WRITE;
/*!40000 ALTER TABLE `sys_menu` DISABLE KEYS */;
INSERT INTO `sys_menu` VALUES (1,'系统管理',0,1,'system',NULL,'','',1,0,'M','0','0','','system','admin','2025-04-25 08:01:01','',NULL,'系统管理目录'),(2,'系统监控',0,2,'monitor',NULL,'','',1,0,'M','0','0','','monitor','admin','2025-04-25 08:01:01','',NULL,'系统监控目录'),(3,'系统工具',0,3,'tool',NULL,'','',1,0,'M','0','0','','tool','admin','2025-04-25 08:01:01','',NULL,'系统工具目录'),(100,'用户管理',1,1,'user','system/user/index','','',1,0,'C','0','0','system:user:list','user','admin','2025-04-25 08:01:01','',NULL,'用户管理菜单'),(101,'角色管理',1,2,'role','system/role/index','','',1,0,'C','0','0','system:role:list','peoples','admin','2025-04-25 08:01:01','',NULL,'角色管理菜单'),(102,'菜单管理',1,3,'menu','system/menu/index','','',1,0,'C','0','0','system:menu:list','tree-table','admin','2025-04-25 08:01:01','',NULL,'菜单管理菜单'),(103,'部门管理',1,4,'dept','system/dept/index','','',1,0,'C','0','0','system:dept:list','tree','admin','2025-04-25 08:01:01','',NULL,'部门管理菜单'),(104,'岗位管理',1,5,'post','system/post/index','','',1,0,'C','0','0','system:post:list','post','admin','2025-04-25 08:01:01','',NULL,'岗位管理菜单'),(105,'字典管理',1,6,'dict','system/dict/index','','',1,0,'C','0','0','system:dict:list','dict','admin','2025-04-25 08:01:01','',NULL,'字典管理菜单'),(106,'参数设置',1,7,'config','system/config/index','','',1,0,'C','0','0','system:config:list','edit','admin','2025-04-25 08:01:01','',NULL,'参数设置菜单'),(107,'通知公告',1,8,'notice','system/notice/index','','',1,0,'C','0','0','system:notice:list','message','admin','2025-04-25 08:01:01','',NULL,'通知公告菜单'),(108,'日志管理',1,9,'log','','','',1,0,'M','0','0','','log','admin','2025-04-25 08:01:01','',NULL,'日志管理菜单'),(109,'在线用户',2,1,'online','monitor/online/index','','',1,0,'C','0','0','monitor:online:list','online','admin','2025-04-25 08:01:01','',NULL,'在线用户菜单'),(110,'定时任务',2,2,'job','monitor/job/index','','',1,0,'C','0','0','monitor:job:list','job','admin','2025-04-25 08:01:01','',NULL,'定时任务菜单'),(111,'数据监控',2,3,'druid','monitor/druid/index','','',1,0,'C','0','0','monitor:druid:list','druid','admin','2025-04-25 08:01:01','',NULL,'数据监控菜单'),(112,'服务监控',2,4,'server','monitor/server/index','','',1,0,'C','0','0','monitor:server:list','server','admin','2025-04-25 08:01:01','',NULL,'服务监控菜单'),(113,'缓存监控',2,5,'cache','monitor/cache/index','','',1,0,'C','0','0','monitor:cache:list','redis','admin','2025-04-25 08:01:01','',NULL,'缓存监控菜单'),(114,'缓存列表',2,6,'cacheList','monitor/cache/list','','',1,0,'C','0','0','monitor:cache:list','redis-list','admin','2025-04-25 08:01:01','',NULL,'缓存列表菜单'),(115,'表单构建',3,1,'build','tool/build/index','','',1,0,'C','0','0','tool:build:list','build','admin','2025-04-25 08:01:01','',NULL,'表单构建菜单'),(116,'代码生成',3,2,'gen','tool/gen/index','','',1,0,'C','0','0','tool:gen:list','code','admin','2025-04-25 08:01:01','',NULL,'代码生成菜单'),(117,'系统接口',3,3,'swagger','tool/swagger/index','','',1,0,'C','0','0','tool:swagger:list','swagger','admin','2025-04-25 08:01:01','',NULL,'系统接口菜单'),(500,'操作日志',108,1,'operlog','monitor/operlog/index','','',1,0,'C','0','0','monitor:operlog:list','form','admin','2025-04-25 08:01:01','',NULL,'操作日志菜单'),(501,'登录日志',108,2,'logininfor','monitor/logininfor/index','','',1,0,'C','0','0','monitor:logininfor:list','logininfor','admin','2025-04-25 08:01:01','',NULL,'登录日志菜单'),(1000,'用户查询',100,1,'','','','',1,0,'F','0','0','system:user:query','#','admin','2025-04-25 08:01:01','',NULL,''),(1001,'用户新增',100,2,'','','','',1,0,'F','0','0','system:user:add','#','admin','2025-04-25 08:01:01','',NULL,''),(1002,'用户修改',100,3,'','','','',1,0,'F','0','0','system:user:edit','#','admin','2025-04-25 08:01:01','',NULL,''),(1003,'用户删除',100,4,'','','','',1,0,'F','0','0','system:user:remove','#','admin','2025-04-25 08:01:01','',NULL,''),(1004,'用户导出',100,5,'','','','',1,0,'F','0','0','system:user:export','#','admin','2025-04-25 08:01:01','',NULL,''),(1005,'用户导入',100,6,'','','','',1,0,'F','0','0','system:user:import','#','admin','2025-04-25 08:01:01','',NULL,''),(1006,'重置密码',100,7,'','','','',1,0,'F','0','0','system:user:resetPwd','#','admin','2025-04-25 08:01:01','',NULL,''),(1007,'角色查询',101,1,'','','','',1,0,'F','0','0','system:role:query','#','admin','2025-04-25 08:01:01','',NULL,''),(1008,'角色新增',101,2,'','','','',1,0,'F','0','0','system:role:add','#','admin','2025-04-25 08:01:01','',NULL,''),(1009,'角色修改',101,3,'','','','',1,0,'F','0','0','system:role:edit','#','admin','2025-04-25 08:01:01','',NULL,''),(1010,'角色删除',101,4,'','','','',1,0,'F','0','0','system:role:remove','#','admin','2025-04-25 08:01:01','',NULL,''),(1011,'角色导出',101,5,'','','','',1,0,'F','0','0','system:role:export','#','admin','2025-04-25 08:01:01','',NULL,''),(1012,'菜单查询',102,1,'','','','',1,0,'F','0','0','system:menu:query','#','admin','2025-04-25 08:01:01','',NULL,''),(1013,'菜单新增',102,2,'','','','',1,0,'F','0','0','system:menu:add','#','admin','2025-04-25 08:01:01','',NULL,''),(1014,'菜单修改',102,3,'','','','',1,0,'F','0','0','system:menu:edit','#','admin','2025-04-25 08:01:01','',NULL,''),(1015,'菜单删除',102,4,'','','','',1,0,'F','0','0','system:menu:remove','#','admin','2025-04-25 08:01:01','',NULL,''),(1016,'部门查询',103,1,'','','','',1,0,'F','0','0','system:dept:query','#','admin','2025-04-25 08:01:01','',NULL,''),(1017,'部门新增',103,2,'','','','',1,0,'F','0','0','system:dept:add','#','admin','2025-04-25 08:01:01','',NULL,''),(1018,'部门修改',103,3,'','','','',1,0,'F','0','0','system:dept:edit','#','admin','2025-04-25 08:01:01','',NULL,''),(1019,'部门删除',103,4,'','','','',1,0,'F','0','0','system:dept:remove','#','admin','2025-04-25 08:01:01','',NULL,''),(1020,'岗位查询',104,1,'','','','',1,0,'F','0','0','system:post:query','#','admin','2025-04-25 08:01:01','',NULL,''),(1021,'岗位新增',104,2,'','','','',1,0,'F','0','0','system:post:add','#','admin','2025-04-25 08:01:01','',NULL,''),(1022,'岗位修改',104,3,'','','','',1,0,'F','0','0','system:post:edit','#','admin','2025-04-25 08:01:01','',NULL,''),(1023,'岗位删除',104,4,'','','','',1,0,'F','0','0','system:post:remove','#','admin','2025-04-25 08:01:01','',NULL,''),(1024,'岗位导出',104,5,'','','','',1,0,'F','0','0','system:post:export','#','admin','2025-04-25 08:01:01','',NULL,''),(1025,'字典查询',105,1,'#','','','',1,0,'F','0','0','system:dict:query','#','admin','2025-04-25 08:01:01','',NULL,''),(1026,'字典新增',105,2,'#','','','',1,0,'F','0','0','system:dict:add','#','admin','2025-04-25 08:01:01','',NULL,''),(1027,'字典修改',105,3,'#','','','',1,0,'F','0','0','system:dict:edit','#','admin','2025-04-25 08:01:01','',NULL,''),(1028,'字典删除',105,4,'#','','','',1,0,'F','0','0','system:dict:remove','#','admin','2025-04-25 08:01:01','',NULL,''),(1029,'字典导出',105,5,'#','','','',1,0,'F','0','0','system:dict:export','#','admin','2025-04-25 08:01:01','',NULL,''),(1030,'参数查询',106,1,'#','','','',1,0,'F','0','0','system:config:query','#','admin','2025-04-25 08:01:01','',NULL,''),(1031,'参数新增',106,2,'#','','','',1,0,'F','0','0','system:config:add','#','admin','2025-04-25 08:01:01','',NULL,''),(1032,'参数修改',106,3,'#','','','',1,0,'F','0','0','system:config:edit','#','admin','2025-04-25 08:01:01','',NULL,''),(1033,'参数删除',106,4,'#','','','',1,0,'F','0','0','system:config:remove','#','admin','2025-04-25 08:01:01','',NULL,''),(1034,'参数导出',106,5,'#','','','',1,0,'F','0','0','system:config:export','#','admin','2025-04-25 08:01:01','',NULL,''),(1035,'公告查询',107,1,'#','','','',1,0,'F','0','0','system:notice:query','#','admin','2025-04-25 08:01:01','',NULL,''),(1036,'公告新增',107,2,'#','','','',1,0,'F','0','0','system:notice:add','#','admin','2025-04-25 08:01:01','',NULL,''),(1037,'公告修改',107,3,'#','','','',1,0,'F','0','0','system:notice:edit','#','admin','2025-04-25 08:01:01','',NULL,''),(1038,'公告删除',107,4,'#','','','',1,0,'F','0','0','system:notice:remove','#','admin','2025-04-25 08:01:01','',NULL,''),(1039,'操作查询',500,1,'#','','','',1,0,'F','0','0','monitor:operlog:query','#','admin','2025-04-25 08:01:01','',NULL,''),(1040,'操作删除',500,2,'#','','','',1,0,'F','0','0','monitor:operlog:remove','#','admin','2025-04-25 08:01:01','',NULL,''),(1041,'日志导出',500,3,'#','','','',1,0,'F','0','0','monitor:operlog:export','#','admin','2025-04-25 08:01:01','',NULL,''),(1042,'登录查询',501,1,'#','','','',1,0,'F','0','0','monitor:logininfor:query','#','admin','2025-04-25 08:01:01','',NULL,''),(1043,'登录删除',501,2,'#','','','',1,0,'F','0','0','monitor:logininfor:remove','#','admin','2025-04-25 08:01:01','',NULL,''),(1044,'日志导出',501,3,'#','','','',1,0,'F','0','0','monitor:logininfor:export','#','admin','2025-04-25 08:01:01','',NULL,''),(1045,'账户解锁',501,4,'#','','','',1,0,'F','0','0','monitor:logininfor:unlock','#','admin','2025-04-25 08:01:01','',NULL,''),(1046,'在线查询',109,1,'#','','','',1,0,'F','0','0','monitor:online:query','#','admin','2025-04-25 08:01:01','',NULL,''),(1047,'批量强退',109,2,'#','','','',1,0,'F','0','0','monitor:online:batchLogout','#','admin','2025-04-25 08:01:01','',NULL,''),(1048,'单条强退',109,3,'#','','','',1,0,'F','0','0','monitor:online:forceLogout','#','admin','2025-04-25 08:01:01','',NULL,''),(1049,'任务查询',110,1,'#','','','',1,0,'F','0','0','monitor:job:query','#','admin','2025-04-25 08:01:01','',NULL,''),(1050,'任务新增',110,2,'#','','','',1,0,'F','0','0','monitor:job:add','#','admin','2025-04-25 08:01:01','',NULL,''),(1051,'任务修改',110,3,'#','','','',1,0,'F','0','0','monitor:job:edit','#','admin','2025-04-25 08:01:01','',NULL,''),(1052,'任务删除',110,4,'#','','','',1,0,'F','0','0','monitor:job:remove','#','admin','2025-04-25 08:01:01','',NULL,''),(1053,'状态修改',110,5,'#','','','',1,0,'F','0','0','monitor:job:changeStatus','#','admin','2025-04-25 08:01:01','',NULL,''),(1054,'任务导出',110,6,'#','','','',1,0,'F','0','0','monitor:job:export','#','admin','2025-04-25 08:01:01','',NULL,''),(1055,'生成查询',116,1,'#','','','',1,0,'F','0','0','tool:gen:query','#','admin','2025-04-25 08:01:01','',NULL,''),(1056,'生成修改',116,2,'#','','','',1,0,'F','0','0','tool:gen:edit','#','admin','2025-04-25 08:01:01','',NULL,''),(1057,'生成删除',116,3,'#','','','',1,0,'F','0','0','tool:gen:remove','#','admin','2025-04-25 08:01:01','',NULL,''),(1058,'导入代码',116,4,'#','','','',1,0,'F','0','0','tool:gen:import','#','admin','2025-04-25 08:01:01','',NULL,''),(1059,'预览代码',116,5,'#','','','',1,0,'F','0','0','tool:gen:preview','#','admin','2025-04-25 08:01:01','',NULL,''),(1060,'生成代码',116,6,'#','','','',1,0,'F','0','0','tool:gen:code','#','admin','2025-04-25 08:01:01','',NULL,''),(2000,'库存系统',0,4,'retail',NULL,NULL,'',1,0,'M','0','0',NULL,'redis','admin','2025-04-25 08:32:04','',NULL,''),(2001,'门店管理',2000,1,'store','retail/store/index',NULL,'',1,0,'C','0','0','retail:store:list','dashboard','admin','2025-04-25 08:36:02','',NULL,''),(2002,'柜台管理',2000,2,'counter','retail/counter/index',NULL,'',1,0,'C','0','0','retail:counter:list','cascader','admin','2025-04-25 08:36:56','',NULL,''),(2003,'渠道管理',2000,3,'channels','retail/channel/index',NULL,'',1,0,'C','0','0','retail:channel:list','international','admin','2025-04-25 08:37:45','admin','2025-04-27 03:10:35',''),(2004,'商品管理',2000,4,'products','retail/products/index',NULL,'',1,0,'C','0','0','retail:products:list','star','admin','2025-04-25 08:39:06','',NULL,''),(2005,'商品入库',2000,5,'inorder','retail/inorder/index',NULL,'',1,0,'C','0','0','retail:inorder:list','list','admin','2025-04-25 08:41:08','',NULL,''),(2006,'商品库存',2000,5,'stock','retail/stock/index',NULL,'',1,0,'C','0','0','retail:stock:list','documentation','admin','2025-04-25 08:43:05','',NULL,''),(2007,'门店库存',2000,6,'storestock','retail/storestock/index',NULL,'',1,0,'C','0','0','retail:storestock:list','excel','admin','2025-04-25 08:44:28','',NULL,''),(2008,'订单审核',2000,7,'outapproval','retail/outapproval/index',NULL,'',1,0,'C','0','0','retail:outorder:list','checkbox','admin','2025-04-25 08:46:23','admin','2025-04-25 09:44:22',''),(2009,'订单管理',2000,8,'outorder','retail/outorder/index',NULL,'',1,0,'C','0','0','retail:outorder:list','nested','admin','2025-04-25 08:47:38','',NULL,''),(2010,'流程管理',2000,9,'outflow','retail/outflow/index',NULL,'',1,0,'C','0','0','retail:outflow:list','slider','admin','2025-04-25 08:48:52','admin','2025-04-25 08:49:02',''),(2011,'统计分析',0,6,'analysis',NULL,NULL,'',1,0,'M','0','0','','druid','admin','2025-04-25 08:49:47','admin','2025-04-27 03:59:07',''),(2012,'销售数据',2011,1,'sale','analysis/sale/index','','',1,0,'C','0','0','analysis:sale:data','chart','admin','2025-04-25 08:50:54','boss','2025-04-27 01:33:21',''),(2013,'渠道数据',2011,2,'channel','analysis/channel/index',NULL,'',1,0,'C','0','0','analysis:channel:data','monitor','admin','2025-04-25 08:52:05','',NULL,''),(2014,'门店新增',2001,1,'',NULL,NULL,'',1,0,'F','0','0','retail:store:add','#','admin','2025-04-25 09:06:44','',NULL,''),(2015,'门店修改',2001,2,'',NULL,NULL,'',1,0,'F','0','0','retail:store:edit','#','admin','2025-04-25 09:07:39','admin','2025-04-25 09:10:26',''),(2016,'门店删除',2001,3,'',NULL,NULL,'',1,0,'F','0','0','retail:store:remove','#','admin','2025-04-25 09:08:07','admin','2025-04-25 09:10:46',''),(2017,'门店查看',2001,0,'',NULL,NULL,'',1,0,'F','0','0','retail:store:query','#','admin','2025-04-25 09:14:33','',NULL,''),(2018,'柜台查看',2002,1,'',NULL,NULL,'',1,0,'F','0','0','retail:counter:query','#','admin','2025-04-25 09:16:29','',NULL,''),(2019,'柜台新增',2002,2,'',NULL,NULL,'',1,0,'F','0','0','retail:counter:add','#','admin','2025-04-25 09:16:59','',NULL,''),(2020,'柜台修改',2002,3,'',NULL,NULL,'',1,0,'F','0','0','retail:counter:edit','#','admin','2025-04-25 09:17:39','',NULL,''),(2021,'柜台删除',2002,4,'',NULL,NULL,'',1,0,'F','0','0','retail:counter:remove','#','admin','2025-04-25 09:18:10','',NULL,''),(2022,'渠道查看',2003,1,'',NULL,NULL,'',1,0,'F','0','0','retail:channel:query','#','admin','2025-04-25 09:25:19','',NULL,''),(2023,'渠道修改',2003,2,'',NULL,NULL,'',1,0,'F','0','0','retail:channel:edit','#','admin','2025-04-25 09:25:43','',NULL,''),(2024,'渠道删除',2003,3,'',NULL,NULL,'',1,0,'F','0','0','retail:channel:remove','#','admin','2025-04-25 09:27:32','',NULL,''),(2025,'商品查看',2004,1,'',NULL,NULL,'',1,0,'F','0','0','retail:products:query','#','admin','2025-04-25 09:28:07','',NULL,''),(2026,'商品修改',2004,2,'',NULL,NULL,'',1,0,'F','0','0','retail:products:edit','#','admin','2025-04-25 09:28:34','',NULL,''),(2027,'商品删除',2004,3,'',NULL,NULL,'',1,0,'F','0','0','retail:products:remove','#','admin','2025-04-25 09:28:59','',NULL,''),(2028,'渠道新增',2003,0,'',NULL,NULL,'',1,0,'F','0','0','retail:channel:add','#','admin','2025-04-25 09:30:01','',NULL,''),(2029,'商品新增',2004,0,'',NULL,NULL,'',1,0,'F','0','0','retail:products:add','#','admin','2025-04-25 09:30:23','',NULL,''),(2030,'入库查看',2005,1,'',NULL,NULL,'',1,0,'F','0','0','retail:inorder:query','#','admin','2025-04-25 09:30:48','',NULL,''),(2031,'入库新增',2005,2,'',NULL,NULL,'',1,0,'F','0','0','retail:inorder:add','#','admin','2025-04-25 09:31:09','',NULL,''),(2032,'入库修改',2005,3,'',NULL,NULL,'',1,0,'F','0','0','retail:inorder:edit','#','admin','2025-04-25 09:31:34','',NULL,''),(2033,'入库删除',2005,4,'',NULL,NULL,'',1,0,'F','0','0','retail:inorder:remove','#','admin','2025-04-25 09:31:55','',NULL,''),(2034,'库存查看',2006,1,'',NULL,NULL,'',1,0,'F','0','0','retail:stock:query','#','admin','2025-04-25 09:33:00','',NULL,''),(2035,'库存查看',2007,1,'',NULL,NULL,'',1,0,'F','0','0','retail:storestock:query','#','admin','2025-04-25 09:33:34','',NULL,''),(2036,'商品导出',2004,4,'',NULL,NULL,'',1,0,'F','0','0','retail:products:export','#','admin','2025-04-25 09:34:44','',NULL,''),(2037,'入库导出',2005,5,'',NULL,NULL,'',1,0,'F','0','0','retail:inorder:export','#','admin','2025-04-25 09:35:30','',NULL,''),(2038,'库存导出',2006,2,'',NULL,NULL,'',1,0,'F','0','0','retail:stock:export','#','admin','2025-04-25 09:38:05','',NULL,''),(2039,'库存导出',2007,2,'',NULL,NULL,'',1,0,'F','0','0','retail:storestock:export','#','admin','2025-04-25 09:38:29','',NULL,''),(2040,'商品售出',2007,3,'',NULL,NULL,'',1,0,'F','0','0','retail:storestock:sale','#','admin','2025-04-25 09:41:42','',NULL,''),(2041,'订单查看',2008,1,'',NULL,NULL,'',1,0,'F','0','0','retail:outorder:query','#','admin','2025-04-25 09:44:56','',NULL,''),(2042,'订单导出',2008,2,'',NULL,NULL,'',1,0,'F','0','0','retail:outorder:export','#','admin','2025-04-25 09:45:23','',NULL,''),(2043,'审核通过',2008,3,'',NULL,NULL,'',1,0,'F','0','0','retail:outorder:approve','#','admin','2025-04-25 09:45:50','admin','2025-04-25 09:46:02',''),(2044,'审核驳回',2008,4,'',NULL,NULL,'',1,0,'F','0','0','retail:outorder:reject','#','admin','2025-04-25 09:46:21','',NULL,''),(2045,'审核撤销',2008,5,'',NULL,NULL,'',1,0,'F','0','0','retail:outorder:cancel','#','admin','2025-04-25 09:46:41','',NULL,''),(2046,'订单导出',2009,1,'',NULL,NULL,'',1,0,'F','0','0','retail:outorder:export','#','admin','2025-04-25 09:47:50','',NULL,''),(2047,'订单查看',2009,2,'',NULL,NULL,'',1,0,'F','0','0','retail:outorder:query','#','admin','2025-04-25 09:50:36','',NULL,''),(2048,'订单退货',2009,3,'',NULL,NULL,'',1,0,'F','0','0','retail:outorder:return','#','admin','2025-04-25 09:51:11','',NULL,''),(2049,'流程查看',2010,1,'',NULL,NULL,'',1,0,'F','0','0','retail:outflow:query','#','admin','2025-04-25 10:03:35','',NULL,''),(2050,'流程新增',2010,0,'',NULL,NULL,'',1,0,'F','0','0','retail:outflow:add','#','admin','2025-04-25 10:03:50','',NULL,''),(2051,'流程修改',2010,2,'',NULL,NULL,'',1,0,'F','0','0','retail:outflow:edit','#','admin','2025-04-25 10:04:05','',NULL,''),(2052,'流程删除',2010,3,'',NULL,NULL,'',1,0,'F','0','0','retail:outflow:remove','#','admin','2025-04-25 10:04:29','',NULL,''),(2053,'会员系统',0,5,'member','',NULL,'',1,0,'M','0','0','','user','boss','2025-04-27 03:58:33','admin','2025-04-27 04:38:13',''),(2054,'会员信息',2053,1,'member','member/member/index',NULL,'',1,0,'C','0','0','member:member:list','people','admin','2025-04-27 04:39:04','',NULL,''),(2055,'会员等级',2053,2,'level','member/level/index',NULL,'',1,0,'C','0','0','member:level:list','tree','admin','2025-04-27 04:40:10','',NULL,''),(2056,'会员新增',2054,1,'',NULL,NULL,'',1,0,'F','0','0','member:member:add','#','admin','2025-04-27 07:22:22','',NULL,''),(2057,'会员查看',2054,0,'',NULL,NULL,'',1,0,'F','0','0','member:member:query','#','admin','2025-04-27 07:23:09','admin','2025-04-27 07:23:24',''),(2058,'会员修改',2054,2,'',NULL,NULL,'',1,0,'F','0','0','member:member:edit','#','admin','2025-04-27 07:23:48','',NULL,''),(2059,'会员删除',2054,3,'',NULL,NULL,'',1,0,'F','0','0','member:member:remove','#','admin','2025-04-27 07:24:12','',NULL,''),(2060,'等级查看',2055,1,'',NULL,NULL,'',1,0,'F','0','0','member:level:query','#','admin','2025-04-27 07:24:40','',NULL,''),(2061,'等级新增',2055,2,'',NULL,NULL,'',1,0,'F','0','0','member:level:add','#','admin','2025-04-27 07:25:29','',NULL,''),(2062,'等级修改',2055,3,'',NULL,NULL,'',1,0,'F','0','0','member:level:edit','#','admin','2025-04-27 07:26:12','',NULL,''),(2063,'等级删除',2055,4,'',NULL,NULL,'',1,0,'F','0','0','member:level:remove','#','admin','2025-04-27 07:26:36','',NULL,''),(2064,'商品预订',2007,4,'',NULL,NULL,'',1,0,'F','0','0','retail:storestock:reserve','#','admin','2025-04-28 13:43:21','',NULL,''),(2065,'预订交货',2009,4,'',NULL,NULL,'',1,0,'F','0','0','retail:outorder:deliver','#','admin','2025-04-28 13:44:37','admin','2025-04-30 10:22:52',''),(2066,'取消预订',2009,5,'',NULL,NULL,'',1,0,'F','0','0','retail:outorder:reservecancel','#','admin','2025-04-30 10:00:42','',NULL,'');
/*!40000 ALTER TABLE `sys_menu` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `sys_notice`
--

DROP TABLE IF EXISTS `sys_notice`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `sys_notice` (
  `notice_id` int NOT NULL AUTO_INCREMENT COMMENT '公告ID',
  `notice_title` varchar(50) NOT NULL COMMENT '公告标题',
  `notice_type` char(1) NOT NULL COMMENT '公告类型（1通知 2公告）',
  `notice_content` longblob COMMENT '公告内容',
  `status` char(1) DEFAULT '0' COMMENT '公告状态（0正常 1关闭）',
  `create_by` varchar(64) DEFAULT '' COMMENT '创建者',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(64) DEFAULT '' COMMENT '更新者',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  `remark` varchar(255) DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`notice_id`)
) ENGINE=InnoDB AUTO_INCREMENT=10 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='通知公告表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `sys_notice`
--

LOCK TABLES `sys_notice` WRITE;
/*!40000 ALTER TABLE `sys_notice` DISABLE KEYS */;
INSERT INTO `sys_notice` VALUES (1,'温馨提醒：2018-07-01 若依新版本发布啦','2',_binary '新版本内容','0','admin','2025-04-25 08:01:03','',NULL,'管理员'),(2,'维护通知：2018-07-01 若依系统凌晨维护','1',_binary '维护内容','0','admin','2025-04-25 08:01:03','',NULL,'管理员');
/*!40000 ALTER TABLE `sys_notice` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `sys_oper_log`
--

DROP TABLE IF EXISTS `sys_oper_log`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `sys_oper_log` (
  `oper_id` bigint NOT NULL AUTO_INCREMENT COMMENT '日志主键',
  `title` varchar(50) DEFAULT '' COMMENT '模块标题',
  `business_type` int DEFAULT '0' COMMENT '业务类型（0其它 1新增 2修改 3删除）',
  `method` varchar(200) DEFAULT '' COMMENT '方法名称',
  `request_method` varchar(10) DEFAULT '' COMMENT '请求方式',
  `operator_type` int DEFAULT '0' COMMENT '操作类别（0其它 1后台用户 2手机端用户）',
  `oper_name` varchar(50) DEFAULT '' COMMENT '操作人员',
  `dept_name` varchar(50) DEFAULT '' COMMENT '部门名称',
  `oper_url` varchar(255) DEFAULT '' COMMENT '请求URL',
  `oper_ip` varchar(128) DEFAULT '' COMMENT '主机地址',
  `oper_location` varchar(255) DEFAULT '' COMMENT '操作地点',
  `oper_param` varchar(2000) DEFAULT '' COMMENT '请求参数',
  `json_result` varchar(2000) DEFAULT '' COMMENT '返回参数',
  `status` int DEFAULT '0' COMMENT '操作状态（0正常 1异常）',
  `error_msg` varchar(2000) DEFAULT '' COMMENT '错误消息',
  `oper_time` datetime DEFAULT NULL COMMENT '操作时间',
  `cost_time` bigint DEFAULT '0' COMMENT '消耗时间',
  PRIMARY KEY (`oper_id`),
  KEY `idx_sys_oper_log_bt` (`business_type`),
  KEY `idx_sys_oper_log_s` (`status`),
  KEY `idx_sys_oper_log_ot` (`oper_time`)
) ENGINE=InnoDB AUTO_INCREMENT=402 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='操作日志记录';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `sys_oper_log`
--

LOCK TABLES `sys_oper_log` WRITE;
/*!40000 ALTER TABLE `sys_oper_log` DISABLE KEYS */;
INSERT INTO `sys_oper_log` VALUES (100,'菜单管理',3,'com.retail.project.system.controller.SysMenuController.remove()','DELETE',1,'admin','研发部门','/system/menu/4','127.0.0.1','内网IP','4','{\"msg\":\"菜单已分配,不允许删除\",\"code\":601}',0,NULL,'2025-04-25 08:30:59',40),(101,'角色管理',2,'com.retail.project.system.controller.SysRoleController.edit()','PUT',1,'admin','研发部门','/system/role','127.0.0.1','内网IP','{\"admin\":false,\"createTime\":\"2025-04-25 08:01:00\",\"dataScope\":\"2\",\"delFlag\":\"0\",\"deptCheckStrictly\":true,\"flag\":false,\"menuCheckStrictly\":true,\"menuIds\":[1,100,1000,1001,1002,1003,1004,1005,1006,101,1007,1008,1009,1010,1011,102,1012,1013,1014,1015,103,1016,1017,1018,1019,104,1020,1021,1022,1023,1024,105,1025,1026,1027,1028,1029,106,1030,1031,1032,1033,1034,107,1035,1036,1037,1038,108,500,1039,1040,1041,501,1042,1043,1044,1045,2,109,1046,1047,1048,110,1049,1050,1051,1052,1053,1054,111,112,113,114,3,115,116,1055,1056,1057,1058,1059,1060,117],\"params\":{},\"remark\":\"普通角色\",\"roleId\":2,\"roleKey\":\"common\",\"roleName\":\"普通角色\",\"roleSort\":2,\"status\":\"0\",\"updateBy\":\"admin\"}','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,'2025-04-25 08:31:06',54),(102,'菜单管理',3,'com.retail.project.system.controller.SysMenuController.remove()','DELETE',1,'admin','研发部门','/system/menu/4','127.0.0.1','内网IP','4','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,'2025-04-25 08:31:10',20),(103,'菜单管理',1,'com.retail.project.system.controller.SysMenuController.add()','POST',1,'admin','研发部门','/system/menu','127.0.0.1','内网IP','{\"children\":[],\"createBy\":\"admin\",\"icon\":\"redis\",\"isCache\":\"0\",\"isFrame\":\"1\",\"menuName\":\"库存系统\",\"menuType\":\"M\",\"orderNum\":4,\"params\":{},\"parentId\":0,\"path\":\"retail\",\"status\":\"0\",\"visible\":\"0\"}','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,'2025-04-25 08:32:04',22),(104,'菜单管理',1,'com.retail.project.system.controller.SysMenuController.add()','POST',1,'admin','研发部门','/system/menu','127.0.0.1','内网IP','{\"children\":[],\"component\":\"retail/store/index\",\"createBy\":\"admin\",\"icon\":\"dashboard\",\"isCache\":\"0\",\"isFrame\":\"1\",\"menuName\":\"门店管理\",\"menuType\":\"C\",\"orderNum\":1,\"params\":{},\"parentId\":2000,\"path\":\"store\",\"perms\":\"retail:store:list\",\"status\":\"0\",\"visible\":\"0\"}','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,'2025-04-25 08:36:02',31),(105,'菜单管理',1,'com.retail.project.system.controller.SysMenuController.add()','POST',1,'admin','研发部门','/system/menu','127.0.0.1','内网IP','{\"children\":[],\"component\":\"retail/counter/index\",\"createBy\":\"admin\",\"icon\":\"cascader\",\"isCache\":\"0\",\"isFrame\":\"1\",\"menuName\":\"柜台管理\",\"menuType\":\"C\",\"orderNum\":2,\"params\":{},\"parentId\":2000,\"path\":\"counter\",\"perms\":\"retail:counter:list\",\"status\":\"0\",\"visible\":\"0\"}','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,'2025-04-25 08:36:56',14),(106,'菜单管理',1,'com.retail.project.system.controller.SysMenuController.add()','POST',1,'admin','研发部门','/system/menu','127.0.0.1','内网IP','{\"children\":[],\"component\":\"retail/channel/index\",\"createBy\":\"admin\",\"isCache\":\"0\",\"isFrame\":\"1\",\"menuName\":\"渠道管理\",\"menuType\":\"C\",\"orderNum\":3,\"params\":{},\"parentId\":2000,\"path\":\"channel\",\"perms\":\"retail:channel:list\",\"status\":\"0\",\"visible\":\"0\"}','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,'2025-04-25 08:37:45',50),(107,'菜单管理',2,'com.retail.project.system.controller.SysMenuController.edit()','PUT',1,'admin','研发部门','/system/menu','127.0.0.1','内网IP','{\"children\":[],\"component\":\"retail/channel/index\",\"createTime\":\"2025-04-25 08:37:45\",\"icon\":\"international\",\"isCache\":\"0\",\"isFrame\":\"1\",\"menuId\":2003,\"menuName\":\"渠道管理\",\"menuType\":\"C\",\"orderNum\":3,\"params\":{},\"parentId\":2000,\"path\":\"channel\",\"perms\":\"retail:channel:list\",\"routeName\":\"\",\"status\":\"0\",\"updateBy\":\"admin\",\"visible\":\"0\"}','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,'2025-04-25 08:38:05',25),(108,'菜单管理',1,'com.retail.project.system.controller.SysMenuController.add()','POST',1,'admin','研发部门','/system/menu','127.0.0.1','内网IP','{\"children\":[],\"component\":\"retail/products/index\",\"createBy\":\"admin\",\"icon\":\"star\",\"isCache\":\"0\",\"isFrame\":\"1\",\"menuName\":\"商品管理\",\"menuType\":\"C\",\"orderNum\":4,\"params\":{},\"parentId\":2000,\"path\":\"products\",\"perms\":\"retail:products:list\",\"status\":\"0\",\"visible\":\"0\"}','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,'2025-04-25 08:39:06',30),(109,'菜单管理',1,'com.retail.project.system.controller.SysMenuController.add()','POST',1,'admin','研发部门','/system/menu','127.0.0.1','内网IP','{\"children\":[],\"component\":\"retail/inorder/index\",\"createBy\":\"admin\",\"icon\":\"list\",\"isCache\":\"0\",\"isFrame\":\"1\",\"menuName\":\"商品入库\",\"menuType\":\"C\",\"orderNum\":5,\"params\":{},\"parentId\":2000,\"path\":\"inorder\",\"perms\":\"retail:inorder:list\",\"status\":\"0\",\"visible\":\"0\"}','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,'2025-04-25 08:41:08',32),(110,'菜单管理',1,'com.retail.project.system.controller.SysMenuController.add()','POST',1,'admin','研发部门','/system/menu','127.0.0.1','内网IP','{\"children\":[],\"component\":\"retail/stock/index\",\"createBy\":\"admin\",\"icon\":\"documentation\",\"isCache\":\"0\",\"isFrame\":\"1\",\"menuName\":\"商品库存\",\"menuType\":\"C\",\"orderNum\":5,\"params\":{},\"parentId\":2000,\"path\":\"stock\",\"perms\":\"retail:stock:list\",\"status\":\"0\",\"visible\":\"0\"}','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,'2025-04-25 08:43:06',36),(111,'菜单管理',1,'com.retail.project.system.controller.SysMenuController.add()','POST',1,'admin','研发部门','/system/menu','127.0.0.1','内网IP','{\"children\":[],\"component\":\"retail/storestock/index\",\"createBy\":\"admin\",\"icon\":\"excel\",\"isCache\":\"0\",\"isFrame\":\"1\",\"menuName\":\"门店库存\",\"menuType\":\"C\",\"orderNum\":6,\"params\":{},\"parentId\":2000,\"path\":\"storestock\",\"perms\":\"retail:storestock:list\",\"status\":\"0\",\"visible\":\"0\"}','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,'2025-04-25 08:44:28',35),(112,'菜单管理',1,'com.retail.project.system.controller.SysMenuController.add()','POST',1,'admin','研发部门','/system/menu','127.0.0.1','内网IP','{\"children\":[],\"component\":\"retail/outapproval/index\",\"createBy\":\"admin\",\"icon\":\"checkbox\",\"isCache\":\"0\",\"isFrame\":\"1\",\"menuName\":\"订单审核\",\"menuType\":\"C\",\"orderNum\":7,\"params\":{},\"parentId\":2000,\"path\":\"outapproval\",\"perms\":\"retail:outapproval:list\",\"status\":\"0\",\"visible\":\"0\"}','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,'2025-04-25 08:46:23',31),(113,'菜单管理',1,'com.retail.project.system.controller.SysMenuController.add()','POST',1,'admin','研发部门','/system/menu','127.0.0.1','内网IP','{\"children\":[],\"component\":\"retail/outorder/index\",\"createBy\":\"admin\",\"icon\":\"nested\",\"isCache\":\"0\",\"isFrame\":\"1\",\"menuName\":\"订单管理\",\"menuType\":\"C\",\"orderNum\":8,\"params\":{},\"parentId\":2000,\"path\":\"outorder\",\"perms\":\"retail:outorder:list\",\"status\":\"0\",\"visible\":\"0\"}','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,'2025-04-25 08:47:38',19),(114,'菜单管理',1,'com.retail.project.system.controller.SysMenuController.add()','POST',1,'admin','研发部门','/system/menu','127.0.0.1','内网IP','{\"children\":[],\"component\":\"retail/outflow/index\",\"createBy\":\"admin\",\"icon\":\"slider\",\"isCache\":\"0\",\"isFrame\":\"1\",\"menuName\":\"流程管理\",\"menuType\":\"C\",\"orderNum\":9,\"params\":{},\"parentId\":0,\"path\":\"outflow\",\"perms\":\"retail:outflow:list\",\"status\":\"0\",\"visible\":\"0\"}','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,'2025-04-25 08:48:52',30),(115,'菜单管理',2,'com.retail.project.system.controller.SysMenuController.edit()','PUT',1,'admin','研发部门','/system/menu','127.0.0.1','内网IP','{\"children\":[],\"component\":\"retail/outflow/index\",\"createTime\":\"2025-04-25 08:48:52\",\"icon\":\"slider\",\"isCache\":\"0\",\"isFrame\":\"1\",\"menuId\":2010,\"menuName\":\"流程管理\",\"menuType\":\"C\",\"orderNum\":9,\"params\":{},\"parentId\":2000,\"path\":\"outflow\",\"perms\":\"retail:outflow:list\",\"routeName\":\"\",\"status\":\"0\",\"updateBy\":\"admin\",\"visible\":\"0\"}','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,'2025-04-25 08:49:02',18),(116,'菜单管理',1,'com.retail.project.system.controller.SysMenuController.add()','POST',1,'admin','研发部门','/system/menu','127.0.0.1','内网IP','{\"children\":[],\"createBy\":\"admin\",\"icon\":\"druid\",\"isCache\":\"0\",\"isFrame\":\"1\",\"menuName\":\"统计分析\",\"menuType\":\"M\",\"orderNum\":5,\"params\":{},\"parentId\":0,\"path\":\"analysis\",\"status\":\"0\",\"visible\":\"0\"}','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,'2025-04-25 08:49:47',28),(117,'菜单管理',1,'com.retail.project.system.controller.SysMenuController.add()','POST',1,'admin','研发部门','/system/menu','127.0.0.1','内网IP','{\"children\":[],\"component\":\"analysis/sale/index\",\"createBy\":\"admin\",\"icon\":\"chart\",\"isCache\":\"0\",\"isFrame\":\"1\",\"menuName\":\"销售数据\",\"menuType\":\"C\",\"orderNum\":1,\"params\":{},\"parentId\":2011,\"path\":\"sale\",\"perms\":\"analysis/sale/data\",\"status\":\"0\",\"visible\":\"0\"}','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,'2025-04-25 08:50:54',50),(118,'菜单管理',1,'com.retail.project.system.controller.SysMenuController.add()','POST',1,'admin','研发部门','/system/menu','127.0.0.1','内网IP','{\"children\":[],\"component\":\"analysis/channel/index\",\"createBy\":\"admin\",\"icon\":\"monitor\",\"isCache\":\"0\",\"isFrame\":\"1\",\"menuName\":\"渠道数据\",\"menuType\":\"C\",\"orderNum\":2,\"params\":{},\"parentId\":2011,\"path\":\"channel\",\"perms\":\"analysis:channel:data\",\"status\":\"0\",\"visible\":\"0\"}','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,'2025-04-25 08:52:05',32),(119,'用户管理',3,'com.retail.project.system.controller.SysUserController.remove()','DELETE',1,'admin','研发部门','/system/user/2','127.0.0.1','内网IP','[2]','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,'2025-04-25 08:52:36',48),(120,'角色管理',3,'com.retail.project.system.controller.SysRoleController.remove()','DELETE',1,'admin','研发部门','/system/role/2','127.0.0.1','内网IP','[2]','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,'2025-04-25 08:52:42',34),(121,'部门管理',3,'com.retail.project.system.controller.SysDeptController.remove()','DELETE',1,'admin','研发部门','/system/dept/102','127.0.0.1','内网IP','102','{\"msg\":\"存在下级部门,不允许删除\",\"code\":601}',0,NULL,'2025-04-25 08:52:49',5),(122,'部门管理',3,'com.retail.project.system.controller.SysDeptController.remove()','DELETE',1,'admin','研发部门','/system/dept/109','127.0.0.1','内网IP','109','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,'2025-04-25 08:52:51',16),(123,'部门管理',3,'com.retail.project.system.controller.SysDeptController.remove()','DELETE',1,'admin','研发部门','/system/dept/108','127.0.0.1','内网IP','108','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,'2025-04-25 08:52:52',21),(124,'部门管理',3,'com.retail.project.system.controller.SysDeptController.remove()','DELETE',1,'admin','研发部门','/system/dept/102','127.0.0.1','内网IP','102','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,'2025-04-25 08:52:54',13),(125,'部门管理',3,'com.retail.project.system.controller.SysDeptController.remove()','DELETE',1,'admin','研发部门','/system/dept/107','127.0.0.1','内网IP','107','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,'2025-04-25 08:52:56',18),(126,'部门管理',3,'com.retail.project.system.controller.SysDeptController.remove()','DELETE',1,'admin','研发部门','/system/dept/106','127.0.0.1','内网IP','106','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,'2025-04-25 08:52:58',20),(127,'部门管理',3,'com.retail.project.system.controller.SysDeptController.remove()','DELETE',1,'admin','研发部门','/system/dept/105','127.0.0.1','内网IP','105','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,'2025-04-25 08:53:00',16),(128,'部门管理',3,'com.retail.project.system.controller.SysDeptController.remove()','DELETE',1,'admin','研发部门','/system/dept/104','127.0.0.1','内网IP','104','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,'2025-04-25 08:53:02',16),(129,'部门管理',3,'com.retail.project.system.controller.SysDeptController.remove()','DELETE',1,'admin','研发部门','/system/dept/103','127.0.0.1','内网IP','103','{\"msg\":\"部门存在用户,不允许删除\",\"code\":601}',0,NULL,'2025-04-25 08:53:03',11),(130,'部门管理',2,'com.retail.project.system.controller.SysDeptController.edit()','PUT',1,'admin','研发部门','/system/dept','127.0.0.1','内网IP','{\"ancestors\":\"0\",\"children\":[],\"deptId\":100,\"deptName\":\"奢胜\",\"email\":\"<EMAIL>\",\"leader\":\"boss\",\"orderNum\":0,\"params\":{},\"parentId\":0,\"phone\":\"15888888888\",\"status\":\"0\",\"updateBy\":\"admin\"}','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,'2025-04-25 08:53:25',15),(131,'部门管理',2,'com.retail.project.system.controller.SysDeptController.edit()','PUT',1,'admin','研发部门','/system/dept','127.0.0.1','内网IP','{\"ancestors\":\"0,100\",\"children\":[],\"deptId\":101,\"deptName\":\"总部\",\"email\":\"<EMAIL>\",\"leader\":\"boss\",\"orderNum\":1,\"params\":{},\"parentId\":100,\"parentName\":\"奢胜\",\"phone\":\"15888888888\",\"status\":\"0\",\"updateBy\":\"admin\"}','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,'2025-04-25 08:53:45',35),(132,'部门管理',1,'com.retail.project.system.controller.SysDeptController.add()','POST',1,'admin','研发部门','/system/dept','127.0.0.1','内网IP','{\"ancestors\":\"0,100\",\"children\":[],\"createBy\":\"admin\",\"deptName\":\"门店\",\"orderNum\":2,\"params\":{},\"parentId\":100,\"status\":\"0\"}','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,'2025-04-25 08:57:02',23),(133,'部门管理',1,'com.retail.project.system.controller.SysDeptController.add()','POST',1,'admin','研发部门','/system/dept','127.0.0.1','内网IP','{\"ancestors\":\"0,100\",\"children\":[],\"createBy\":\"admin\",\"deptName\":\"供应商\",\"orderNum\":3,\"params\":{},\"parentId\":100,\"status\":\"0\"}','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,'2025-04-25 08:57:18',17),(134,'角色管理',1,'com.retail.project.system.controller.SysRoleController.add()','POST',1,'admin','研发部门','/system/role','127.0.0.1','内网IP','{\"admin\":false,\"createBy\":\"admin\",\"deptCheckStrictly\":true,\"deptIds\":[],\"flag\":false,\"menuCheckStrictly\":true,\"menuIds\":[1,100,1000,1001,1002,1003,1004,1005,1006,101,1007,1008,1009,1010,1011,102,1012,1013,1014,1015,103,1016,1017,1018,1019,104,1020,1021,1022,1023,1024,105,1025,1026,1027,1028,1029,106,1030,1031,1032,1033,1034,107,1035,1036,1037,1038,108,500,1039,1040,1041,501,1042,1043,1044,1045,2000,2001,2002,2003,2004,2005,2006,2007,2008,2009,2010,2011,2012,2013],\"params\":{},\"roleId\":100,\"roleKey\":\"manager\",\"roleName\":\"管理员\",\"roleSort\":1,\"status\":\"0\"}','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,'2025-04-25 08:57:46',36),(135,'角色管理',1,'com.retail.project.system.controller.SysRoleController.add()','POST',1,'admin','研发部门','/system/role','127.0.0.1','内网IP','{\"admin\":false,\"createBy\":\"admin\",\"deptCheckStrictly\":true,\"deptIds\":[],\"flag\":false,\"menuCheckStrictly\":true,\"menuIds\":[2000,2001,2002,2003,2004,2005,2006,2007,2008,2009],\"params\":{},\"roleId\":101,\"roleKey\":\"finance\",\"roleName\":\"财务\",\"roleSort\":3,\"status\":\"0\"}','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,'2025-04-25 08:58:13',28),(136,'角色管理',1,'com.retail.project.system.controller.SysRoleController.add()','POST',1,'admin','研发部门','/system/role','127.0.0.1','内网IP','{\"admin\":false,\"createBy\":\"admin\",\"deptCheckStrictly\":true,\"deptIds\":[],\"flag\":false,\"menuCheckStrictly\":true,\"menuIds\":[2000,2007,2008,2009],\"params\":{},\"roleId\":102,\"roleKey\":\"store\",\"roleName\":\"门店\",\"roleSort\":4,\"status\":\"0\"}','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,'2025-04-25 08:58:52',39),(137,'角色管理',1,'com.retail.project.system.controller.SysRoleController.add()','POST',1,'admin','研发部门','/system/role','127.0.0.1','内网IP','{\"admin\":false,\"createBy\":\"admin\",\"deptCheckStrictly\":true,\"deptIds\":[],\"flag\":false,\"menuCheckStrictly\":true,\"menuIds\":[2011,2013],\"params\":{},\"roleId\":103,\"roleKey\":\"channel\",\"roleName\":\"供应商\",\"roleSort\":5,\"status\":\"0\"}','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,'2025-04-25 08:59:17',31),(138,'用户管理',1,'com.retail.project.system.controller.SysUserController.add()','POST',1,'admin','研发部门','/system/user','127.0.0.1','内网IP','{\"admin\":false,\"createBy\":\"admin\",\"deptId\":101,\"nickName\":\"老板\",\"params\":{},\"phonenumber\":\"\",\"postIds\":[],\"roleIds\":[100],\"status\":\"0\",\"userId\":100,\"userName\":\"boss\"}','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,'2025-04-25 08:59:57',98),(139,'用户管理',4,'com.retail.project.system.controller.SysUserController.insertAuthRole()','PUT',1,'admin','研发部门','/system/user/authRole','127.0.0.1','内网IP','{\"roleIds\":\"100\",\"userId\":\"100\"}','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,'2025-04-25 09:00:37',21),(140,'用户管理',1,'com.retail.project.system.controller.SysUserController.add()','POST',1,'admin','研发部门','/system/user','127.0.0.1','内网IP','{\"admin\":false,\"createBy\":\"admin\",\"deptId\":101,\"nickName\":\"财务经理\",\"params\":{},\"postIds\":[],\"roleIds\":[101],\"status\":\"0\",\"userId\":101,\"userName\":\"finance\"}','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,'2025-04-25 09:01:13',106),(141,'用户管理',1,'com.retail.project.system.controller.SysUserController.add()','POST',1,'admin','研发部门','/system/user','127.0.0.1','内网IP','{\"admin\":false,\"createBy\":\"admin\",\"deptId\":200,\"nickName\":\"门店1\",\"params\":{},\"postIds\":[],\"roleIds\":[102],\"status\":\"0\",\"userId\":102,\"userName\":\"store1\"}','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,'2025-04-25 09:01:40',107),(142,'用户管理',1,'com.retail.project.system.controller.SysUserController.add()','POST',1,'admin','研发部门','/system/user','127.0.0.1','内网IP','{\"admin\":false,\"createBy\":\"admin\",\"deptId\":200,\"nickName\":\"门店2\",\"params\":{},\"postIds\":[],\"roleIds\":[102],\"status\":\"0\",\"userId\":103,\"userName\":\"store2\"}','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,'2025-04-25 09:02:11',103),(143,'用户管理',1,'com.retail.project.system.controller.SysUserController.add()','POST',1,'admin','研发部门','/system/user','127.0.0.1','内网IP','{\"admin\":false,\"createBy\":\"admin\",\"deptId\":201,\"nickName\":\"供应商1\",\"params\":{},\"postIds\":[],\"roleIds\":[103],\"status\":\"0\",\"userId\":104,\"userName\":\"channel1\"}','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,'2025-04-25 09:02:44',99),(144,'用户管理',1,'com.retail.project.system.controller.SysUserController.add()','POST',1,'admin','研发部门','/system/user','127.0.0.1','内网IP','{\"admin\":false,\"createBy\":\"admin\",\"deptId\":201,\"nickName\":\"供应商2\",\"params\":{},\"postIds\":[],\"roleIds\":[103],\"status\":\"0\",\"userId\":105,\"userName\":\"channel2\"}','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,'2025-04-25 09:03:27',107),(145,'用户管理',4,'com.retail.project.system.controller.SysUserController.insertAuthRole()','PUT',1,'admin','研发部门','/system/user/authRole','127.0.0.1','内网IP','{\"roleIds\":\"101\",\"userId\":\"101\"}','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,'2025-04-25 09:03:34',17),(146,'用户管理',4,'com.retail.project.system.controller.SysUserController.insertAuthRole()','PUT',1,'admin','研发部门','/system/user/authRole','127.0.0.1','内网IP','{\"roleIds\":\"102\",\"userId\":\"102\"}','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,'2025-04-25 09:03:40',15),(147,'菜单管理',1,'com.retail.project.system.controller.SysMenuController.add()','POST',1,'admin','研发部门','/system/menu','127.0.0.1','内网IP','{\"children\":[],\"createBy\":\"admin\",\"isCache\":\"0\",\"isFrame\":\"1\",\"menuName\":\"门店新增\",\"menuType\":\"F\",\"orderNum\":1,\"params\":{},\"parentId\":2001,\"perms\":\"retail:store:add\",\"status\":\"0\",\"visible\":\"0\"}','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,'2025-04-25 09:06:44',18),(148,'菜单管理',1,'com.retail.project.system.controller.SysMenuController.add()','POST',1,'admin','研发部门','/system/menu','127.0.0.1','内网IP','{\"children\":[],\"createBy\":\"admin\",\"isCache\":\"0\",\"isFrame\":\"1\",\"menuName\":\"门店修改\",\"menuType\":\"F\",\"orderNum\":2,\"params\":{},\"parentId\":2001,\"perms\":\"retail:store:update\",\"status\":\"0\",\"visible\":\"0\"}','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,'2025-04-25 09:07:39',10),(149,'菜单管理',1,'com.retail.project.system.controller.SysMenuController.add()','POST',1,'admin','研发部门','/system/menu','127.0.0.1','内网IP','{\"children\":[],\"createBy\":\"admin\",\"isCache\":\"0\",\"isFrame\":\"1\",\"menuName\":\"门店删除\",\"menuType\":\"F\",\"orderNum\":3,\"params\":{},\"parentId\":2001,\"perms\":\"retail:store:delete\",\"status\":\"0\",\"visible\":\"0\"}','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,'2025-04-25 09:08:07',14),(150,'角色管理',2,'com.retail.project.system.controller.SysRoleController.edit()','PUT',1,'admin','研发部门','/system/role','127.0.0.1','内网IP','{\"admin\":false,\"createTime\":\"2025-04-25 08:57:46\",\"dataScope\":\"1\",\"delFlag\":\"0\",\"deptCheckStrictly\":true,\"flag\":false,\"menuCheckStrictly\":true,\"menuIds\":[1,100,1000,1001,1002,1003,1004,1005,1006,101,1007,1008,1009,1010,1011,102,1012,1013,1014,1015,103,1016,1017,1018,1019,104,1020,1021,1022,1023,1024,105,1025,1026,1027,1028,1029,106,1030,1031,1032,1033,1034,107,1035,1036,1037,1038,108,500,1039,1040,1041,501,1042,1043,1044,1045,2000,2001,2014,2015,2016,2002,2003,2004,2005,2006,2007,2008,2009,2010,2011,2012,2013],\"params\":{},\"roleId\":100,\"roleKey\":\"manager\",\"roleName\":\"管理员\",\"roleSort\":1,\"status\":\"0\",\"updateBy\":\"admin\"}','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,'2025-04-25 09:08:46',25),(151,'角色管理',2,'com.retail.project.system.controller.SysRoleController.edit()','PUT',1,'admin','研发部门','/system/role','127.0.0.1','内网IP','{\"admin\":false,\"createTime\":\"2025-04-25 08:58:13\",\"dataScope\":\"1\",\"delFlag\":\"0\",\"deptCheckStrictly\":true,\"flag\":false,\"menuCheckStrictly\":true,\"menuIds\":[2000,2001,2014,2015,2016,2002,2003,2004,2005,2006,2007,2008,2009],\"params\":{},\"roleId\":101,\"roleKey\":\"finance\",\"roleName\":\"财务\",\"roleSort\":3,\"status\":\"0\",\"updateBy\":\"admin\"}','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,'2025-04-25 09:08:56',27),(152,'门店管理',1,'com.retail.project.retail.controller.RetailStoreController.add()','POST',1,'finance','总部','/retail/store','127.0.0.1','内网IP','{\"createBy\":\"finance\",\"createTime\":\"2025-04-25 17:09:56\",\"id\":1,\"params\":{},\"storeAddress\":\"北京市故宫\",\"storeContact\":\"13344445555\",\"storeName\":\"北京门店\",\"userId\":\"102\",\"userName\":\"门店1\"}','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,'2025-04-25 09:09:56',29),(153,'菜单管理',2,'com.retail.project.system.controller.SysMenuController.edit()','PUT',1,'admin','研发部门','/system/menu','127.0.0.1','内网IP','{\"children\":[],\"createTime\":\"2025-04-25 09:07:39\",\"icon\":\"#\",\"isCache\":\"0\",\"isFrame\":\"1\",\"menuId\":2015,\"menuName\":\"门店修改\",\"menuType\":\"F\",\"orderNum\":2,\"params\":{},\"parentId\":2001,\"path\":\"\",\"perms\":\"retail:store:edit\",\"routeName\":\"\",\"status\":\"0\",\"updateBy\":\"admin\",\"visible\":\"0\"}','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,'2025-04-25 09:10:26',15),(154,'菜单管理',2,'com.retail.project.system.controller.SysMenuController.edit()','PUT',1,'admin','研发部门','/system/menu','127.0.0.1','内网IP','{\"children\":[],\"createTime\":\"2025-04-25 09:08:07\",\"icon\":\"#\",\"isCache\":\"0\",\"isFrame\":\"1\",\"menuId\":2016,\"menuName\":\"门店删除\",\"menuType\":\"F\",\"orderNum\":3,\"params\":{},\"parentId\":2001,\"path\":\"\",\"perms\":\"retail:store:remove\",\"routeName\":\"\",\"status\":\"0\",\"updateBy\":\"admin\",\"visible\":\"0\"}','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,'2025-04-25 09:10:46',14),(155,'门店管理',1,'com.retail.project.retail.controller.RetailStoreController.add()','POST',1,'finance','总部','/retail/store','127.0.0.1','内网IP','{\"createBy\":\"finance\",\"createTime\":\"2025-04-25 17:12:51\",\"id\":2,\"params\":{},\"storeAddress\":\"成都武侯区\",\"storeContact\":\"15566667777\",\"storeName\":\"成都门店\",\"userId\":\"103\",\"userName\":\"门店2\"}','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,'2025-04-25 09:12:52',46),(156,'角色管理',2,'com.retail.project.system.controller.SysRoleController.edit()','PUT',1,'admin','研发部门','/system/role','127.0.0.1','内网IP','{\"admin\":false,\"createTime\":\"2025-04-25 08:58:13\",\"dataScope\":\"1\",\"delFlag\":\"0\",\"deptCheckStrictly\":true,\"flag\":false,\"menuCheckStrictly\":true,\"menuIds\":[2000,2001,2014,2015,2016,2002,2003,2004,2005,2006,2007,2008,2009],\"params\":{},\"roleId\":101,\"roleKey\":\"finance\",\"roleName\":\"财务\",\"roleSort\":3,\"status\":\"0\",\"updateBy\":\"admin\"}','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,'2025-04-25 09:13:41',37),(157,'角色管理',2,'com.retail.project.system.controller.SysRoleController.edit()','PUT',1,'admin','研发部门','/system/role','127.0.0.1','内网IP','{\"admin\":false,\"createTime\":\"2025-04-25 08:57:46\",\"dataScope\":\"1\",\"delFlag\":\"0\",\"deptCheckStrictly\":true,\"flag\":false,\"menuCheckStrictly\":true,\"menuIds\":[1,100,1000,1001,1002,1003,1004,1005,1006,101,1007,1008,1009,1010,1011,102,1012,1013,1014,1015,103,1016,1017,1018,1019,104,1020,1021,1022,1023,1024,105,1025,1026,1027,1028,1029,106,1030,1031,1032,1033,1034,107,1035,1036,1037,1038,108,500,1039,1040,1041,501,1042,1043,1044,1045,2000,2001,2014,2015,2016,2002,2003,2004,2005,2006,2007,2008,2009,2010,2011,2012,2013],\"params\":{},\"roleId\":100,\"roleKey\":\"manager\",\"roleName\":\"管理员\",\"roleSort\":1,\"status\":\"0\",\"updateBy\":\"admin\"}','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,'2025-04-25 09:13:49',41),(158,'菜单管理',1,'com.retail.project.system.controller.SysMenuController.add()','POST',1,'admin','研发部门','/system/menu','127.0.0.1','内网IP','{\"children\":[],\"createBy\":\"admin\",\"isCache\":\"0\",\"isFrame\":\"1\",\"menuName\":\"门店查看\",\"menuType\":\"F\",\"orderNum\":0,\"params\":{},\"parentId\":2001,\"perms\":\"retail:store:query\",\"status\":\"0\",\"visible\":\"0\"}','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,'2025-04-25 09:14:33',13),(159,'角色管理',2,'com.retail.project.system.controller.SysRoleController.edit()','PUT',1,'admin','研发部门','/system/role','127.0.0.1','内网IP','{\"admin\":false,\"createTime\":\"2025-04-25 08:57:46\",\"dataScope\":\"1\",\"delFlag\":\"0\",\"deptCheckStrictly\":true,\"flag\":false,\"menuCheckStrictly\":true,\"menuIds\":[1,100,1000,1001,1002,1003,1004,1005,1006,101,1007,1008,1009,1010,1011,102,1012,1013,1014,1015,103,1016,1017,1018,1019,104,1020,1021,1022,1023,1024,105,1025,1026,1027,1028,1029,106,1030,1031,1032,1033,1034,107,1035,1036,1037,1038,108,500,1039,1040,1041,501,1042,1043,1044,1045,2000,2001,2017,2014,2015,2016,2002,2003,2004,2005,2006,2007,2008,2009,2010,2011,2012,2013],\"params\":{},\"roleId\":100,\"roleKey\":\"manager\",\"roleName\":\"管理员\",\"roleSort\":1,\"status\":\"0\",\"updateBy\":\"admin\"}','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,'2025-04-25 09:14:43',36),(160,'角色管理',2,'com.retail.project.system.controller.SysRoleController.edit()','PUT',1,'admin','研发部门','/system/role','127.0.0.1','内网IP','{\"admin\":false,\"createTime\":\"2025-04-25 08:58:13\",\"dataScope\":\"1\",\"delFlag\":\"0\",\"deptCheckStrictly\":true,\"flag\":false,\"menuCheckStrictly\":true,\"menuIds\":[2000,2001,2017,2014,2015,2016,2002,2003,2004,2005,2006,2007,2008,2009],\"params\":{},\"roleId\":101,\"roleKey\":\"finance\",\"roleName\":\"财务\",\"roleSort\":3,\"status\":\"0\",\"updateBy\":\"admin\"}','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,'2025-04-25 09:14:51',19),(161,'角色管理',2,'com.retail.project.system.controller.SysRoleController.edit()','PUT',1,'admin','研发部门','/system/role','127.0.0.1','内网IP','{\"admin\":false,\"createTime\":\"2025-04-25 08:57:46\",\"dataScope\":\"1\",\"delFlag\":\"0\",\"deptCheckStrictly\":true,\"flag\":false,\"menuCheckStrictly\":true,\"menuIds\":[1,100,1000,1001,1002,1003,1004,1005,1006,101,1007,1008,1009,1010,1011,102,1012,1013,1014,1015,103,1016,1017,1018,1019,104,1020,1021,1022,1023,1024,105,1025,1026,1027,1028,1029,106,1030,1031,1032,1033,1034,107,1035,1036,1037,1038,108,500,1039,1040,1041,501,1042,1043,1044,1045,2000,2001,2017,2014,2015,2016,2002,2003,2004,2005,2006,2007,2008,2009,2010,2011,2012,2013],\"params\":{},\"roleId\":100,\"roleKey\":\"manager\",\"roleName\":\"管理员\",\"roleSort\":1,\"status\":\"0\",\"updateBy\":\"admin\"}','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,'2025-04-25 09:15:11',24),(162,'角色管理',2,'com.retail.project.system.controller.SysRoleController.edit()','PUT',1,'admin','研发部门','/system/role','127.0.0.1','内网IP','{\"admin\":false,\"createTime\":\"2025-04-25 08:58:13\",\"dataScope\":\"1\",\"delFlag\":\"0\",\"deptCheckStrictly\":true,\"flag\":false,\"menuCheckStrictly\":true,\"menuIds\":[2000,2001,2017,2014,2015,2016,2002,2003,2004,2005,2006,2007,2008,2009],\"params\":{},\"roleId\":101,\"roleKey\":\"finance\",\"roleName\":\"财务\",\"roleSort\":3,\"status\":\"0\",\"updateBy\":\"admin\"}','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,'2025-04-25 09:15:17',26),(163,'门店管理',2,'com.retail.project.retail.controller.RetailStoreController.edit()','PUT',1,'finance','总部','/retail/store','127.0.0.1','内网IP','{\"createBy\":\"finance\",\"createTime\":\"2025-04-25 09:09:56\",\"id\":1,\"params\":{},\"storeAddress\":\"北京市故宫1\",\"storeContact\":\"13344445555\",\"storeName\":\"北京门店\",\"updateBy\":\"finance\",\"updateTime\":\"2025-04-25 17:15:41\",\"userId\":\"102\",\"userName\":\"门店1\"}','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,'2025-04-25 09:15:41',40),(164,'门店管理',2,'com.retail.project.retail.controller.RetailStoreController.edit()','PUT',1,'finance','总部','/retail/store','127.0.0.1','内网IP','{\"createBy\":\"finance\",\"createTime\":\"2025-04-25 09:12:51\",\"id\":2,\"params\":{},\"storeAddress\":\"成都武侯区2\",\"storeContact\":\"15566667777\",\"storeName\":\"成都门店\",\"updateBy\":\"finance\",\"updateTime\":\"2025-04-25 17:15:44\",\"userId\":\"103\",\"userName\":\"门店2\"}','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,'2025-04-25 09:15:44',9),(165,'菜单管理',1,'com.retail.project.system.controller.SysMenuController.add()','POST',1,'admin','研发部门','/system/menu','127.0.0.1','内网IP','{\"children\":[],\"createBy\":\"admin\",\"isCache\":\"0\",\"isFrame\":\"1\",\"menuName\":\"柜台查看\",\"menuType\":\"F\",\"orderNum\":1,\"params\":{},\"parentId\":2002,\"perms\":\"retail:counter:query\",\"status\":\"0\",\"visible\":\"0\"}','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,'2025-04-25 09:16:29',16),(166,'菜单管理',1,'com.retail.project.system.controller.SysMenuController.add()','POST',1,'admin','研发部门','/system/menu','127.0.0.1','内网IP','{\"children\":[],\"createBy\":\"admin\",\"isCache\":\"0\",\"isFrame\":\"1\",\"menuName\":\"柜台新增\",\"menuType\":\"F\",\"orderNum\":2,\"params\":{},\"parentId\":2002,\"perms\":\"retail:counter:add\",\"status\":\"0\",\"visible\":\"0\"}','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,'2025-04-25 09:16:59',16),(167,'菜单管理',1,'com.retail.project.system.controller.SysMenuController.add()','POST',1,'admin','研发部门','/system/menu','127.0.0.1','内网IP','{\"children\":[],\"createBy\":\"admin\",\"isCache\":\"0\",\"isFrame\":\"1\",\"menuName\":\"柜台修改\",\"menuType\":\"F\",\"orderNum\":3,\"params\":{},\"parentId\":2002,\"perms\":\"retail:counter:edit\",\"status\":\"0\",\"visible\":\"0\"}','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,'2025-04-25 09:17:39',11),(168,'菜单管理',1,'com.retail.project.system.controller.SysMenuController.add()','POST',1,'admin','研发部门','/system/menu','127.0.0.1','内网IP','{\"children\":[],\"createBy\":\"admin\",\"isCache\":\"0\",\"isFrame\":\"1\",\"menuName\":\"柜台删除\",\"menuType\":\"F\",\"orderNum\":4,\"params\":{},\"parentId\":2002,\"perms\":\"retail:counter:remove\",\"status\":\"0\",\"visible\":\"0\"}','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,'2025-04-25 09:18:10',12),(169,'菜单管理',1,'com.retail.project.system.controller.SysMenuController.add()','POST',1,'admin','研发部门','/system/menu','127.0.0.1','内网IP','{\"children\":[],\"createBy\":\"admin\",\"isCache\":\"0\",\"isFrame\":\"1\",\"menuName\":\"渠道查看\",\"menuType\":\"F\",\"orderNum\":1,\"params\":{},\"parentId\":2003,\"perms\":\"retail:channel:query\",\"status\":\"0\",\"visible\":\"0\"}','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,'2025-04-25 09:25:19',20),(170,'菜单管理',1,'com.retail.project.system.controller.SysMenuController.add()','POST',1,'admin','研发部门','/system/menu','127.0.0.1','内网IP','{\"children\":[],\"createBy\":\"admin\",\"isCache\":\"0\",\"isFrame\":\"1\",\"menuName\":\"渠道修改\",\"menuType\":\"F\",\"orderNum\":2,\"params\":{},\"parentId\":2003,\"perms\":\"retail:channel:edit\",\"status\":\"0\",\"visible\":\"0\"}','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,'2025-04-25 09:25:43',20),(171,'菜单管理',1,'com.retail.project.system.controller.SysMenuController.add()','POST',1,'admin','研发部门','/system/menu','127.0.0.1','内网IP','{\"children\":[],\"createBy\":\"admin\",\"isCache\":\"0\",\"isFrame\":\"1\",\"menuName\":\"渠道删除\",\"menuType\":\"F\",\"orderNum\":3,\"params\":{},\"parentId\":2003,\"perms\":\"retail:channel:remove\",\"status\":\"0\",\"visible\":\"0\"}','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,'2025-04-25 09:27:32',21),(172,'菜单管理',1,'com.retail.project.system.controller.SysMenuController.add()','POST',1,'admin','研发部门','/system/menu','127.0.0.1','内网IP','{\"children\":[],\"createBy\":\"admin\",\"isCache\":\"0\",\"isFrame\":\"1\",\"menuName\":\"商品查看\",\"menuType\":\"F\",\"orderNum\":1,\"params\":{},\"parentId\":2004,\"perms\":\"retail:products:query\",\"status\":\"0\",\"visible\":\"0\"}','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,'2025-04-25 09:28:07',15),(173,'菜单管理',1,'com.retail.project.system.controller.SysMenuController.add()','POST',1,'admin','研发部门','/system/menu','127.0.0.1','内网IP','{\"children\":[],\"createBy\":\"admin\",\"isCache\":\"0\",\"isFrame\":\"1\",\"menuName\":\"商品修改\",\"menuType\":\"F\",\"orderNum\":2,\"params\":{},\"parentId\":2004,\"perms\":\"retail:products:edit\",\"status\":\"0\",\"visible\":\"0\"}','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,'2025-04-25 09:28:34',13),(174,'菜单管理',1,'com.retail.project.system.controller.SysMenuController.add()','POST',1,'admin','研发部门','/system/menu','127.0.0.1','内网IP','{\"children\":[],\"createBy\":\"admin\",\"isCache\":\"0\",\"isFrame\":\"1\",\"menuName\":\"商品删除\",\"menuType\":\"F\",\"orderNum\":3,\"params\":{},\"parentId\":2004,\"perms\":\"retail:products:remove\",\"status\":\"0\",\"visible\":\"0\"}','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,'2025-04-25 09:28:59',35),(175,'菜单管理',1,'com.retail.project.system.controller.SysMenuController.add()','POST',1,'admin','研发部门','/system/menu','127.0.0.1','内网IP','{\"children\":[],\"createBy\":\"admin\",\"isCache\":\"0\",\"isFrame\":\"1\",\"menuName\":\"渠道新增\",\"menuType\":\"F\",\"orderNum\":0,\"params\":{},\"parentId\":2003,\"perms\":\"retail:channel:add\",\"status\":\"0\",\"visible\":\"0\"}','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,'2025-04-25 09:30:01',31),(176,'菜单管理',1,'com.retail.project.system.controller.SysMenuController.add()','POST',1,'admin','研发部门','/system/menu','127.0.0.1','内网IP','{\"children\":[],\"createBy\":\"admin\",\"isCache\":\"0\",\"isFrame\":\"1\",\"menuName\":\"商品新增\",\"menuType\":\"F\",\"orderNum\":0,\"params\":{},\"parentId\":2004,\"perms\":\"retail:products:add\",\"status\":\"0\",\"visible\":\"0\"}','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,'2025-04-25 09:30:23',9),(177,'菜单管理',1,'com.retail.project.system.controller.SysMenuController.add()','POST',1,'admin','研发部门','/system/menu','127.0.0.1','内网IP','{\"children\":[],\"createBy\":\"admin\",\"isCache\":\"0\",\"isFrame\":\"1\",\"menuName\":\"入库查看\",\"menuType\":\"F\",\"orderNum\":1,\"params\":{},\"parentId\":2005,\"perms\":\"retail:inorder:query\",\"status\":\"0\",\"visible\":\"0\"}','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,'2025-04-25 09:30:48',17),(178,'菜单管理',1,'com.retail.project.system.controller.SysMenuController.add()','POST',1,'admin','研发部门','/system/menu','127.0.0.1','内网IP','{\"children\":[],\"createBy\":\"admin\",\"isCache\":\"0\",\"isFrame\":\"1\",\"menuName\":\"入库新增\",\"menuType\":\"F\",\"orderNum\":2,\"params\":{},\"parentId\":2005,\"perms\":\"retail:inorder:add\",\"status\":\"0\",\"visible\":\"0\"}','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,'2025-04-25 09:31:09',12),(179,'菜单管理',1,'com.retail.project.system.controller.SysMenuController.add()','POST',1,'admin','研发部门','/system/menu','127.0.0.1','内网IP','{\"children\":[],\"createBy\":\"admin\",\"isCache\":\"0\",\"isFrame\":\"1\",\"menuName\":\"入库修改\",\"menuType\":\"F\",\"orderNum\":3,\"params\":{},\"parentId\":2005,\"perms\":\"retail:inorder:edit\",\"status\":\"0\",\"visible\":\"0\"}','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,'2025-04-25 09:31:34',13),(180,'菜单管理',1,'com.retail.project.system.controller.SysMenuController.add()','POST',1,'admin','研发部门','/system/menu','127.0.0.1','内网IP','{\"children\":[],\"createBy\":\"admin\",\"isCache\":\"0\",\"isFrame\":\"1\",\"menuName\":\"入库删除\",\"menuType\":\"F\",\"orderNum\":4,\"params\":{},\"parentId\":2005,\"perms\":\"retail:inorder:remove\",\"status\":\"0\",\"visible\":\"0\"}','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,'2025-04-25 09:31:55',11),(181,'菜单管理',1,'com.retail.project.system.controller.SysMenuController.add()','POST',1,'admin','研发部门','/system/menu','127.0.0.1','内网IP','{\"children\":[],\"createBy\":\"admin\",\"isCache\":\"0\",\"isFrame\":\"1\",\"menuName\":\"库存查看\",\"menuType\":\"F\",\"orderNum\":1,\"params\":{},\"parentId\":2006,\"perms\":\"retail:stock:query\",\"status\":\"0\",\"visible\":\"0\"}','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,'2025-04-25 09:33:00',16),(182,'菜单管理',1,'com.retail.project.system.controller.SysMenuController.add()','POST',1,'admin','研发部门','/system/menu','127.0.0.1','内网IP','{\"children\":[],\"createBy\":\"admin\",\"isCache\":\"0\",\"isFrame\":\"1\",\"menuName\":\"库存查看\",\"menuType\":\"F\",\"orderNum\":1,\"params\":{},\"parentId\":2007,\"perms\":\"retail:storestock:query\",\"status\":\"0\",\"visible\":\"0\"}','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,'2025-04-25 09:33:34',16),(183,'菜单管理',1,'com.retail.project.system.controller.SysMenuController.add()','POST',1,'admin','研发部门','/system/menu','127.0.0.1','内网IP','{\"children\":[],\"createBy\":\"admin\",\"isCache\":\"0\",\"isFrame\":\"1\",\"menuName\":\"商品导出\",\"menuType\":\"F\",\"orderNum\":4,\"params\":{},\"parentId\":2004,\"perms\":\"retail:products:export\",\"status\":\"0\",\"visible\":\"0\"}','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,'2025-04-25 09:34:44',30),(184,'菜单管理',1,'com.retail.project.system.controller.SysMenuController.add()','POST',1,'admin','研发部门','/system/menu','127.0.0.1','内网IP','{\"children\":[],\"createBy\":\"admin\",\"isCache\":\"0\",\"isFrame\":\"1\",\"menuName\":\"入库导出\",\"menuType\":\"F\",\"orderNum\":5,\"params\":{},\"parentId\":2005,\"perms\":\"retail:inorder:export\",\"status\":\"0\",\"visible\":\"0\"}','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,'2025-04-25 09:35:30',32),(185,'菜单管理',1,'com.retail.project.system.controller.SysMenuController.add()','POST',1,'admin','研发部门','/system/menu','127.0.0.1','内网IP','{\"children\":[],\"createBy\":\"admin\",\"isCache\":\"0\",\"isFrame\":\"1\",\"menuName\":\"库存导出\",\"menuType\":\"F\",\"orderNum\":2,\"params\":{},\"parentId\":2006,\"perms\":\"retail:stock:export\",\"status\":\"0\",\"visible\":\"0\"}','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,'2025-04-25 09:38:05',24),(186,'菜单管理',1,'com.retail.project.system.controller.SysMenuController.add()','POST',1,'admin','研发部门','/system/menu','127.0.0.1','内网IP','{\"children\":[],\"createBy\":\"admin\",\"isCache\":\"0\",\"isFrame\":\"1\",\"menuName\":\"库存导出\",\"menuType\":\"F\",\"orderNum\":2,\"params\":{},\"parentId\":2007,\"perms\":\"retail:storestock:export\",\"status\":\"0\",\"visible\":\"0\"}','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,'2025-04-25 09:38:29',11),(187,'菜单管理',1,'com.retail.project.system.controller.SysMenuController.add()','POST',1,'admin','研发部门','/system/menu','127.0.0.1','内网IP','{\"children\":[],\"createBy\":\"admin\",\"isCache\":\"0\",\"isFrame\":\"1\",\"menuName\":\"商品售出\",\"menuType\":\"F\",\"orderNum\":3,\"params\":{},\"parentId\":2007,\"perms\":\"retail:storestock:sale\",\"status\":\"0\",\"visible\":\"0\"}','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,'2025-04-25 09:41:42',20),(188,'菜单管理',2,'com.retail.project.system.controller.SysMenuController.edit()','PUT',1,'admin','研发部门','/system/menu','127.0.0.1','内网IP','{\"children\":[],\"component\":\"retail/outapproval/index\",\"createTime\":\"2025-04-25 08:46:23\",\"icon\":\"checkbox\",\"isCache\":\"0\",\"isFrame\":\"1\",\"menuId\":2008,\"menuName\":\"订单审核\",\"menuType\":\"C\",\"orderNum\":7,\"params\":{},\"parentId\":2000,\"path\":\"outapproval\",\"perms\":\"retail:outorder:list\",\"routeName\":\"\",\"status\":\"0\",\"updateBy\":\"admin\",\"visible\":\"0\"}','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,'2025-04-25 09:44:22',16),(189,'菜单管理',1,'com.retail.project.system.controller.SysMenuController.add()','POST',1,'admin','研发部门','/system/menu','127.0.0.1','内网IP','{\"children\":[],\"createBy\":\"admin\",\"isCache\":\"0\",\"isFrame\":\"1\",\"menuName\":\"订单查看\",\"menuType\":\"F\",\"orderNum\":1,\"params\":{},\"parentId\":2008,\"perms\":\"retail:outorder:query\",\"status\":\"0\",\"visible\":\"0\"}','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,'2025-04-25 09:44:56',27),(190,'菜单管理',1,'com.retail.project.system.controller.SysMenuController.add()','POST',1,'admin','研发部门','/system/menu','127.0.0.1','内网IP','{\"children\":[],\"createBy\":\"admin\",\"isCache\":\"0\",\"isFrame\":\"1\",\"menuName\":\"订单导出\",\"menuType\":\"F\",\"orderNum\":2,\"params\":{},\"parentId\":2008,\"perms\":\"retail:outorder:export\",\"status\":\"0\",\"visible\":\"0\"}','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,'2025-04-25 09:45:23',16),(191,'菜单管理',1,'com.retail.project.system.controller.SysMenuController.add()','POST',1,'admin','研发部门','/system/menu','127.0.0.1','内网IP','{\"children\":[],\"createBy\":\"admin\",\"isCache\":\"0\",\"isFrame\":\"1\",\"menuName\":\"订单审核\",\"menuType\":\"F\",\"orderNum\":3,\"params\":{},\"parentId\":2008,\"perms\":\"retail:outorder:approve\",\"status\":\"0\",\"visible\":\"0\"}','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,'2025-04-25 09:45:50',22),(192,'菜单管理',2,'com.retail.project.system.controller.SysMenuController.edit()','PUT',1,'admin','研发部门','/system/menu','127.0.0.1','内网IP','{\"children\":[],\"createTime\":\"2025-04-25 09:45:50\",\"icon\":\"#\",\"isCache\":\"0\",\"isFrame\":\"1\",\"menuId\":2043,\"menuName\":\"审核通过\",\"menuType\":\"F\",\"orderNum\":3,\"params\":{},\"parentId\":2008,\"path\":\"\",\"perms\":\"retail:outorder:approve\",\"routeName\":\"\",\"status\":\"0\",\"updateBy\":\"admin\",\"visible\":\"0\"}','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,'2025-04-25 09:46:02',14),(193,'菜单管理',1,'com.retail.project.system.controller.SysMenuController.add()','POST',1,'admin','研发部门','/system/menu','127.0.0.1','内网IP','{\"children\":[],\"createBy\":\"admin\",\"isCache\":\"0\",\"isFrame\":\"1\",\"menuName\":\"审核驳回\",\"menuType\":\"F\",\"orderNum\":4,\"params\":{},\"parentId\":2008,\"perms\":\"retail:outorder:reject\",\"status\":\"0\",\"visible\":\"0\"}','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,'2025-04-25 09:46:21',24),(194,'菜单管理',1,'com.retail.project.system.controller.SysMenuController.add()','POST',1,'admin','研发部门','/system/menu','127.0.0.1','内网IP','{\"children\":[],\"createBy\":\"admin\",\"isCache\":\"0\",\"isFrame\":\"1\",\"menuName\":\"审核撤销\",\"menuType\":\"F\",\"orderNum\":5,\"params\":{},\"parentId\":2008,\"perms\":\"retail:outorder:cancel\",\"status\":\"0\",\"visible\":\"0\"}','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,'2025-04-25 09:46:41',16),(195,'菜单管理',1,'com.retail.project.system.controller.SysMenuController.add()','POST',1,'admin','研发部门','/system/menu','127.0.0.1','内网IP','{\"children\":[],\"createBy\":\"admin\",\"isCache\":\"0\",\"isFrame\":\"1\",\"menuName\":\"订单导出\",\"menuType\":\"F\",\"orderNum\":1,\"params\":{},\"parentId\":2009,\"perms\":\"retail:outorder:export\",\"status\":\"0\",\"visible\":\"0\"}','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,'2025-04-25 09:47:50',23),(196,'菜单管理',1,'com.retail.project.system.controller.SysMenuController.add()','POST',1,'admin','研发部门','/system/menu','127.0.0.1','内网IP','{\"children\":[],\"createBy\":\"admin\",\"isCache\":\"0\",\"isFrame\":\"1\",\"menuName\":\"订单查看\",\"menuType\":\"F\",\"orderNum\":2,\"params\":{},\"parentId\":2009,\"perms\":\"retail:outorder:query\",\"status\":\"0\",\"visible\":\"0\"}','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,'2025-04-25 09:50:36',25),(197,'菜单管理',1,'com.retail.project.system.controller.SysMenuController.add()','POST',1,'admin','研发部门','/system/menu','127.0.0.1','内网IP','{\"children\":[],\"createBy\":\"admin\",\"isCache\":\"0\",\"isFrame\":\"1\",\"menuName\":\"订单退货\",\"menuType\":\"F\",\"orderNum\":3,\"params\":{},\"parentId\":2009,\"perms\":\"retail:outorder:return\",\"status\":\"0\",\"visible\":\"0\"}','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,'2025-04-25 09:51:11',29),(198,'菜单管理',1,'com.retail.project.system.controller.SysMenuController.add()','POST',1,'admin','研发部门','/system/menu','127.0.0.1','内网IP','{\"children\":[],\"createBy\":\"admin\",\"isCache\":\"0\",\"isFrame\":\"1\",\"menuName\":\"流程查看\",\"menuType\":\"F\",\"orderNum\":1,\"params\":{},\"parentId\":2010,\"perms\":\"retail:outflow:query\",\"status\":\"0\",\"visible\":\"0\"}','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,'2025-04-25 10:03:35',46),(199,'菜单管理',1,'com.retail.project.system.controller.SysMenuController.add()','POST',1,'admin','研发部门','/system/menu','127.0.0.1','内网IP','{\"children\":[],\"createBy\":\"admin\",\"isCache\":\"0\",\"isFrame\":\"1\",\"menuName\":\"流程新增\",\"menuType\":\"F\",\"orderNum\":0,\"params\":{},\"parentId\":2010,\"perms\":\"retail:outflow:add\",\"status\":\"0\",\"visible\":\"0\"}','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,'2025-04-25 10:03:50',14),(200,'菜单管理',1,'com.retail.project.system.controller.SysMenuController.add()','POST',1,'admin','研发部门','/system/menu','127.0.0.1','内网IP','{\"children\":[],\"createBy\":\"admin\",\"isCache\":\"0\",\"isFrame\":\"1\",\"menuName\":\"流程修改\",\"menuType\":\"F\",\"orderNum\":2,\"params\":{},\"parentId\":2010,\"perms\":\"retail:outflow:edit\",\"status\":\"0\",\"visible\":\"0\"}','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,'2025-04-25 10:04:05',42),(201,'菜单管理',1,'com.retail.project.system.controller.SysMenuController.add()','POST',1,'admin','研发部门','/system/menu','127.0.0.1','内网IP','{\"children\":[],\"createBy\":\"admin\",\"isCache\":\"0\",\"isFrame\":\"1\",\"menuName\":\"流程删除\",\"menuType\":\"F\",\"orderNum\":3,\"params\":{},\"parentId\":2010,\"perms\":\"retail:outflow:remove\",\"status\":\"0\",\"visible\":\"0\"}','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,'2025-04-25 10:04:29',13),(202,'角色管理',2,'com.retail.project.system.controller.SysRoleController.edit()','PUT',1,'admin','研发部门','/system/role','127.0.0.1','内网IP','{\"admin\":false,\"createTime\":\"2025-04-25 08:57:46\",\"dataScope\":\"1\",\"delFlag\":\"0\",\"deptCheckStrictly\":true,\"flag\":false,\"menuCheckStrictly\":true,\"menuIds\":[1,100,1000,1001,1002,1003,1004,1005,1006,101,1007,1008,1009,1010,1011,102,1012,1013,1014,1015,103,1016,1017,1018,1019,104,1020,1021,1022,1023,1024,105,1025,1026,1027,1028,1029,106,1030,1031,1032,1033,1034,107,1035,1036,1037,1038,108,500,1039,1040,1041,501,1042,1043,1044,1045,2000,2001,2017,2014,2015,2016,2002,2018,2019,2020,2021,2003,2028,2022,2023,2024,2004,2029,2025,2026,2027,2036,2005,2030,2031,2032,2033,2037,2006,2034,2038,2007,2035,2039,2040,2008,2041,2042,2043,2044,2045,2009,2046,2047,2048,2010,2050,2049,2051,2052,2011,2012,2013],\"params\":{},\"roleId\":100,\"roleKey\":\"manager\",\"roleName\":\"管理员\",\"roleSort\":1,\"status\":\"0\",\"updateBy\":\"admin\"}','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,'2025-04-25 10:05:06',38),(203,'角色管理',2,'com.retail.project.system.controller.SysRoleController.edit()','PUT',1,'admin','研发部门','/system/role','127.0.0.1','内网IP','{\"admin\":false,\"createTime\":\"2025-04-25 08:58:13\",\"dataScope\":\"1\",\"delFlag\":\"0\",\"deptCheckStrictly\":true,\"flag\":false,\"menuCheckStrictly\":true,\"menuIds\":[2000,2001,2017,2014,2015,2016,2002,2018,2019,2020,2021,2003,2028,2022,2023,2024,2004,2029,2025,2026,2027,2036,2005,2030,2031,2032,2033,2037,2006,2034,2038,2007,2035,2039,2040,2008,2041,2042,2043,2044,2045,2009,2046,2047,2048],\"params\":{},\"roleId\":101,\"roleKey\":\"finance\",\"roleName\":\"财务\",\"roleSort\":3,\"status\":\"0\",\"updateBy\":\"admin\"}','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,'2025-04-25 10:05:16',29),(204,'角色管理',2,'com.retail.project.system.controller.SysRoleController.edit()','PUT',1,'admin','研发部门','/system/role','127.0.0.1','内网IP','{\"admin\":false,\"createTime\":\"2025-04-25 08:58:51\",\"dataScope\":\"1\",\"delFlag\":\"0\",\"deptCheckStrictly\":true,\"flag\":false,\"menuCheckStrictly\":true,\"menuIds\":[2000,2008,2007,2035,2039,2040,2041,2042,2045,2009,2046,2047,2048],\"params\":{},\"roleId\":102,\"roleKey\":\"store\",\"roleName\":\"门店\",\"roleSort\":4,\"status\":\"0\",\"updateBy\":\"admin\"}','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,'2025-04-25 10:05:50',36),(205,'角色管理',2,'com.retail.project.system.controller.SysRoleController.edit()','PUT',1,'admin','研发部门','/system/role','127.0.0.1','内网IP','{\"admin\":false,\"createTime\":\"2025-04-25 08:59:17\",\"dataScope\":\"1\",\"delFlag\":\"0\",\"deptCheckStrictly\":true,\"flag\":false,\"menuCheckStrictly\":true,\"menuIds\":[2011,2013],\"params\":{},\"roleId\":103,\"roleKey\":\"channel\",\"roleName\":\"供应商\",\"roleSort\":5,\"status\":\"0\",\"updateBy\":\"admin\"}','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,'2025-04-25 10:05:56',22),(206,'柜台管理',1,'com.retail.project.retail.controller.RetailCounterController.add()','POST',1,'finance','总部','/retail/counter','127.0.0.1','内网IP','{\"counterName\":\"AA\",\"createBy\":\"finance\",\"createTime\":\"2025-04-25 18:06:12\",\"id\":1,\"params\":{},\"storeId\":1}','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,'2025-04-25 10:06:12',25),(207,'柜台管理',1,'com.retail.project.retail.controller.RetailCounterController.add()','POST',1,'finance','总部','/retail/counter','127.0.0.1','内网IP','{\"counterName\":\"BB\",\"createBy\":\"finance\",\"createTime\":\"2025-04-25 18:06:20\",\"id\":2,\"params\":{},\"storeId\":2}','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,'2025-04-25 10:06:20',7),(208,'渠道管理',1,'com.retail.project.retail.controller.RetailChannelController.add()','POST',1,'finance','总部','/retail/channel','127.0.0.1','内网IP','{\"channelName\":\"海外采购\",\"createBy\":\"finance\",\"createTime\":\"2025-04-25 18:06:43\",\"id\":1,\"params\":{},\"userId\":\"\",\"userName\":\"\"}','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,'2025-04-25 10:06:43',37),(209,'渠道管理',1,'com.retail.project.retail.controller.RetailChannelController.add()','POST',1,'finance','总部','/retail/channel','127.0.0.1','内网IP','{\"channelName\":\"供应商1\",\"createBy\":\"finance\",\"createTime\":\"2025-04-25 18:07:03\",\"id\":2,\"params\":{},\"userId\":\"104\",\"userName\":\"供应商1\"}','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,'2025-04-25 10:07:03',9),(210,'渠道管理',1,'com.retail.project.retail.controller.RetailChannelController.add()','POST',1,'finance','总部','/retail/channel','127.0.0.1','内网IP','{\"channelName\":\"供应商2\",\"createBy\":\"finance\",\"createTime\":\"2025-04-25 18:07:11\",\"id\":3,\"params\":{},\"userId\":\"105\",\"userName\":\"供应商2\"}','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,'2025-04-25 10:07:11',12),(211,'商品',1,'com.retail.project.retail.controller.RetailProductsController.add()','POST',1,'finance','总部','/retail/products','127.0.0.1','内网IP','{\"brand\":\"COACH\",\"category\":\"背提包\",\"color\":\"炭黑色/黑色\",\"createBy\":\"finance\",\"createTime\":\"2025-04-25 18:09:44\",\"id\":1,\"material\":\"人造革配牛皮革配织物\",\"origin\":\"见内标\",\"params\":{},\"productGrade\":\"合格品\",\"productImage\":\"/profile/upload/2025/04/25/bag1_20250425180819A001.jpg\",\"productName\":\"COACH 背提包-2525001\",\"productNumber\":\"2736QBAF4\",\"referencePrice\":8300,\"retailPrice\":4980,\"safetyCategory\":\"C类\",\"specification\":\"35x15x44cm\",\"standard\":\"QB/T 1333-2018\"}','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,'2025-04-25 10:09:44',47),(212,'出库审核流程',1,'com.retail.project.retail.controller.InventoryOutFlowController.add()','POST',1,'admin','总部','/retail/outflow','127.0.0.1','内网IP','{\"approverRoles\":\"admin,manager,finance\",\"createBy\":\"admin\",\"createTime\":\"2025-04-27 09:12:18\",\"description\":\"销售审核\",\"id\":1,\"name\":\"默认审核\",\"params\":{},\"status\":1}','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,'2025-04-27 01:12:18',35),(213,'入库单',1,'com.retail.project.retail.controller.InventoryInOrderController.saveWithDetails()','POST',1,'admin','总部','/retail/inorder/save','127.0.0.1','内网IP','{\"channelId\":1,\"createBy\":\"admin\",\"createTime\":\"2025-04-27 09:12:45\",\"detailList\":[{\"batchNumber\":\"ed6bd0e6a3\",\"counterId\":1,\"createBy\":\"admin\",\"id\":1,\"orderId\":1,\"params\":{},\"productId\":1,\"productImage\":\"/profile/upload/2025/04/25/bag1_20250425180819A001.jpg\",\"productName\":\"COACH 背提包-2525001\",\"purchasePrice\":2000,\"quantity\":10,\"storeId\":1}],\"id\":1,\"isFutures\":false,\"orderNumber\":\"IN3970627084\",\"params\":{}}','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,'2025-04-27 01:12:45',74),(214,'商品',1,'com.retail.project.retail.controller.RetailProductsController.add()','POST',1,'boss','总部','/retail/products','127.0.0.1','内网IP','{\"brand\":\"COACH\",\"category\":\"背提包\",\"color\":\"卡其色/黑色\",\"createBy\":\"boss\",\"createTime\":\"2025-04-27 09:22:14\",\"id\":2,\"material\":\"人造革配剖层牛皮革\",\"origin\":\"见内标\",\"params\":{},\"productGrade\":\"合格品\",\"productImage\":\"/profile/upload/2025/04/27/bag2_20250427092057A001.jpg\",\"productName\":\"COACH 背提包-2524996\",\"productNumber\":\"78277IMCBI\",\"referencePrice\":5500,\"retailPrice\":3300,\"safetyCategory\":\"C类\",\"specification\":\"19.5x10x13.5cm\",\"standard\":\"QB/T 1333-2018\"}','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,'2025-04-27 01:22:14',47),(215,'入库单',2,'com.retail.project.retail.controller.InventoryInOrderController.edit()','PUT',1,'boss','总部','/retail/inorder','127.0.0.1','内网IP','{\"channelId\":1,\"channelName\":\"海外采购\",\"createBy\":\"admin\",\"createTime\":\"2025-04-27 01:12:45\",\"detailList\":[{\"batchNumber\":\"ed6bd0e6a3\",\"counterId\":1,\"counterName\":\"AA\",\"createBy\":\"admin\",\"createTime\":\"2025-04-27 01:12:45\",\"id\":1,\"orderId\":1,\"params\":{},\"productId\":1,\"productImage\":\"/profile/upload/2025/04/25/bag1_20250425180819A001.jpg\",\"productName\":\"COACH 背提包-2525001\",\"productNumber\":\"2736QBAF4\",\"purchasePrice\":2000,\"quantity\":10,\"storeId\":1,\"storeName\":\"北京门店\",\"updateBy\":\"boss\"},{\"batchNumber\":\"158b5e5ff8\",\"counterId\":1,\"createBy\":\"boss\",\"id\":2,\"orderId\":1,\"params\":{},\"productId\":2,\"productImage\":\"/profile/upload/2025/04/27/bag2_20250427092057A001.jpg\",\"productName\":\"COACH 背提包-2524996\",\"purchasePrice\":1300,\"quantity\":10,\"storeId\":1,\"updateBy\":\"boss\"}],\"id\":1,\"isFutures\":false,\"params\":{},\"updateBy\":\"boss\",\"updateTime\":\"2025-04-27 09:22:44\"}','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,'2025-04-27 01:22:44',62),(216,'入库单',1,'com.retail.project.retail.controller.InventoryInOrderController.saveWithDetails()','POST',1,'boss','总部','/retail/inorder/save','127.0.0.1','内网IP','{\"channelId\":1,\"createBy\":\"boss\",\"createTime\":\"2025-04-27 09:23:28\",\"detailList\":[{\"batchNumber\":\"8f861f2bfa\",\"counterId\":2,\"createBy\":\"boss\",\"id\":3,\"orderId\":2,\"params\":{},\"productId\":1,\"productImage\":\"/profile/upload/2025/04/25/bag1_20250425180819A001.jpg\",\"productName\":\"COACH 背提包-2525001\",\"purchasePrice\":2300,\"quantity\":10,\"storeId\":2},{\"batchNumber\":\"8f861f2bfa\",\"counterId\":2,\"createBy\":\"boss\",\"id\":4,\"orderId\":2,\"params\":{},\"productId\":2,\"productImage\":\"/profile/upload/2025/04/27/bag2_20250427092057A001.jpg\",\"productName\":\"COACH 背提包-2524996\",\"purchasePrice\":1400,\"quantity\":10,\"storeId\":2}],\"id\":2,\"isFutures\":false,\"orderNumber\":\"IN3551334901\",\"params\":{}}','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,'2025-04-27 01:23:28',36),(217,'商品售出',1,'com.retail.project.retail.controller.OutOrderController.sale()','POST',1,'store1','门店','/retail/outorder/sale','127.0.0.1','内网IP','{\"customerGender\":0,\"customerName\":\"zz\",\"customerPhone\":\"13344445555\",\"payType\":1,\"quantity\":1,\"remark\":\"111\",\"salePrice\":4980,\"stockId\":1}','{\"msg\":\"商品售出成功\",\"code\":200}',0,NULL,'2025-04-27 01:24:40',75),(218,'字典类型',1,'com.retail.project.system.controller.SysDictTypeController.add()','POST',1,'boss','总部','/system/dict/type','127.0.0.1','内网IP','{\"createBy\":\"boss\",\"dictName\":\"支付方式\",\"dictType\":\"retail_pay_type\",\"params\":{},\"remark\":\"客户支付方式\",\"status\":\"0\"}','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,'2025-04-27 01:26:49',15),(219,'字典数据',1,'com.retail.project.system.controller.SysDictDataController.add()','POST',1,'boss','总部','/system/dict/data','127.0.0.1','内网IP','{\"createBy\":\"boss\",\"default\":false,\"dictLabel\":\"支付宝\",\"dictSort\":1,\"dictType\":\"retail_pay_type\",\"dictValue\":\"1\",\"listClass\":\"default\",\"params\":{},\"status\":\"0\"}','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,'2025-04-27 01:27:14',18),(220,'字典数据',1,'com.retail.project.system.controller.SysDictDataController.add()','POST',1,'boss','总部','/system/dict/data','127.0.0.1','内网IP','{\"createBy\":\"boss\",\"default\":false,\"dictLabel\":\"微信\",\"dictSort\":2,\"dictType\":\"retail_pay_type\",\"dictValue\":\"2\",\"listClass\":\"default\",\"params\":{},\"status\":\"0\"}','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,'2025-04-27 01:27:23',21),(221,'字典数据',2,'com.retail.project.system.controller.SysDictDataController.edit()','PUT',1,'boss','总部','/system/dict/data','127.0.0.1','内网IP','{\"createBy\":\"boss\",\"createTime\":\"2025-04-27 01:27:23\",\"default\":false,\"dictCode\":101,\"dictLabel\":\"微信\",\"dictSort\":2,\"dictType\":\"retail_pay_type\",\"dictValue\":\"2\",\"isDefault\":\"N\",\"listClass\":\"primary\",\"params\":{},\"status\":\"0\",\"updateBy\":\"boss\"}','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,'2025-04-27 01:27:33',19),(222,'字典数据',2,'com.retail.project.system.controller.SysDictDataController.edit()','PUT',1,'boss','总部','/system/dict/data','127.0.0.1','内网IP','{\"createBy\":\"boss\",\"createTime\":\"2025-04-27 01:27:23\",\"default\":false,\"dictCode\":101,\"dictLabel\":\"微信\",\"dictSort\":2,\"dictType\":\"retail_pay_type\",\"dictValue\":\"2\",\"isDefault\":\"N\",\"listClass\":\"success\",\"params\":{},\"status\":\"0\",\"updateBy\":\"boss\"}','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,'2025-04-27 01:27:39',18),(223,'字典数据',2,'com.retail.project.system.controller.SysDictDataController.edit()','PUT',1,'boss','总部','/system/dict/data','127.0.0.1','内网IP','{\"createBy\":\"boss\",\"createTime\":\"2025-04-27 01:27:14\",\"default\":false,\"dictCode\":100,\"dictLabel\":\"支付宝\",\"dictSort\":1,\"dictType\":\"retail_pay_type\",\"dictValue\":\"1\",\"isDefault\":\"N\",\"listClass\":\"primary\",\"params\":{},\"status\":\"0\",\"updateBy\":\"boss\"}','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,'2025-04-27 01:27:48',20),(224,'字典数据',1,'com.retail.project.system.controller.SysDictDataController.add()','POST',1,'boss','总部','/system/dict/data','127.0.0.1','内网IP','{\"createBy\":\"boss\",\"default\":false,\"dictLabel\":\"银行卡\",\"dictSort\":3,\"dictType\":\"retail_pay_type\",\"dictValue\":\"3\",\"listClass\":\"info\",\"params\":{},\"status\":\"0\"}','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,'2025-04-27 01:28:05',17),(225,'审核出库单',2,'com.retail.project.retail.controller.InventoryOutOrderController.approve()','POST',1,'finance','总部','/retail/outorder/approve','127.0.0.1','内网IP','{\"id\":1,\"remark\":\"同意\"}','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,'2025-04-27 01:29:11',38),(226,'商品售出',1,'com.retail.project.retail.controller.OutOrderController.sale()','POST',1,'store1','门店','/retail/outorder/sale','127.0.0.1','内网IP','{\"customerGender\":0,\"customerName\":\"ll\",\"customerPhone\":\"15566667777\",\"payType\":2,\"quantity\":1,\"remark\":\"222\",\"salePrice\":3300,\"stockId\":2}','{\"msg\":\"商品售出成功\",\"code\":200}',0,NULL,'2025-04-27 01:29:45',58),(227,'驳回出库单',2,'com.retail.project.retail.controller.InventoryOutOrderController.reject()','POST',1,'finance','总部','/retail/outorder/reject','127.0.0.1','内网IP','{\"id\":2,\"rejectReason\":\"no\"}','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,'2025-04-27 01:30:07',51),(228,'商品售出',1,'com.retail.project.retail.controller.OutOrderController.sale()','POST',1,'store1','门店','/retail/outorder/sale','127.0.0.1','内网IP','{\"customerGender\":0,\"customerName\":\"zz\",\"customerPhone\":\"13344445555\",\"payType\":2,\"quantity\":1,\"remark\":\"222\",\"salePrice\":3300,\"stockId\":2}','{\"msg\":\"商品售出成功\",\"code\":200}',0,NULL,'2025-04-27 01:30:38',50),(229,'审核出库单',2,'com.retail.project.retail.controller.InventoryOutOrderController.approve()','POST',1,'finance','总部','/retail/outorder/approve','127.0.0.1','内网IP','{\"id\":3,\"remark\":\"11\"}','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,'2025-04-27 01:30:47',30),(230,'申请退货',2,'com.retail.project.retail.controller.InventoryOutOrderController.applyReturn()','POST',1,'store1','门店','/retail/outorder/apply-return','127.0.0.1','内网IP','{\"id\":3,\"returnReason\":\"无理由\"}','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,'2025-04-27 01:31:13',42),(231,'审核退货申请',2,'com.retail.project.retail.controller.InventoryOutOrderController.approveReturn()','POST',1,'finance','总部','/retail/outorder/approve-return','127.0.0.1','内网IP','{\"id\":3,\"remark\":\"同意\"}','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,'2025-04-27 01:31:37',47),(232,'角色管理',2,'com.retail.project.system.controller.SysRoleController.edit()','PUT',1,'boss','总部','/system/role','127.0.0.1','内网IP','{\"admin\":false,\"createTime\":\"2025-04-25 08:57:46\",\"dataScope\":\"1\",\"delFlag\":\"0\",\"deptCheckStrictly\":true,\"flag\":false,\"menuCheckStrictly\":true,\"menuIds\":[1,100,1000,1001,1002,1003,1004,1005,1006,101,1007,1008,1009,1010,1011,102,1012,1013,1014,1015,103,1016,1017,1018,1019,104,1020,1021,1022,1023,1024,105,1025,1026,1027,1028,1029,106,1030,1031,1032,1033,1034,107,1035,1036,1037,1038,108,500,1039,1040,1041,501,1042,1043,1044,1045,2000,2001,2017,2014,2015,2016,2002,2018,2019,2020,2021,2003,2028,2022,2023,2024,2004,2029,2025,2026,2027,2036,2005,2030,2031,2032,2033,2037,2006,2034,2038,2007,2035,2039,2040,2008,2041,2042,2043,2044,2045,2009,2046,2047,2048,2010,2050,2049,2051,2052,2011,2012,2013],\"params\":{},\"roleId\":100,\"roleKey\":\"manager\",\"roleName\":\"管理员\",\"roleSort\":1,\"status\":\"0\",\"updateBy\":\"boss\"}','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,'2025-04-27 01:32:50',53),(233,'菜单管理',2,'com.retail.project.system.controller.SysMenuController.edit()','PUT',1,'boss','总部','/system/menu','127.0.0.1','内网IP','{\"children\":[],\"component\":\"analysis/sale/index\",\"createTime\":\"2025-04-25 08:50:54\",\"icon\":\"chart\",\"isCache\":\"0\",\"isFrame\":\"1\",\"menuId\":2012,\"menuName\":\"销售数据\",\"menuType\":\"C\",\"orderNum\":1,\"params\":{},\"parentId\":2011,\"path\":\"sale\",\"perms\":\"analysis:sale:data\",\"query\":\"\",\"routeName\":\"\",\"status\":\"0\",\"updateBy\":\"boss\",\"visible\":\"0\"}','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,'2025-04-27 01:33:21',30),(234,'入库单',1,'com.retail.project.retail.controller.InventoryInOrderController.saveWithDetails()','POST',1,'admin','总部','/retail/inorder/save','127.0.0.1','内网IP','{\"channelId\":2,\"createBy\":\"admin\",\"createTime\":\"2025-04-27 10:24:39\",\"detailList\":[{\"batchNumber\":\"d42d95db08\",\"counterId\":1,\"createBy\":\"admin\",\"id\":5,\"orderId\":3,\"params\":{},\"productId\":1,\"productImage\":\"/profile/upload/2025/04/25/bag1_20250425180819A001.jpg\",\"productName\":\"COACH 背提包-2525001\",\"purchasePrice\":3000,\"quantity\":5,\"storeId\":1},{\"batchNumber\":\"d42d95db08\",\"counterId\":1,\"createBy\":\"admin\",\"id\":6,\"orderId\":3,\"params\":{},\"productId\":2,\"productImage\":\"/profile/upload/2025/04/27/bag2_20250427092057A001.jpg\",\"productName\":\"COACH 背提包-2524996\",\"purchasePrice\":2800,\"quantity\":5,\"storeId\":1}],\"id\":3,\"isFutures\":false,\"orderNumber\":\"IN6982479464\",\"params\":{}}','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,'2025-04-27 02:24:39',56),(235,'角色管理',2,'com.retail.project.system.controller.SysRoleController.edit()','PUT',1,'boss','总部','/system/role','127.0.0.1','内网IP','{\"admin\":false,\"createTime\":\"2025-04-25 08:57:46\",\"dataScope\":\"1\",\"delFlag\":\"0\",\"deptCheckStrictly\":true,\"flag\":false,\"menuCheckStrictly\":true,\"menuIds\":[2000,1,100,1000,1001,1002,1003,1004,1005,1006,101,1007,1008,1009,1010,1011,102,1012,1013,1014,1015,103,1016,1017,1018,1019,104,1020,1021,1022,1023,1024,105,1025,1026,1027,1028,1029,106,1030,1031,1032,1033,1034,107,1035,1036,1037,1038,108,500,1039,1040,1041,501,1042,1043,1044,1045,2001,2017,2014,2015,2016,2002,2018,2019,2020,2021,2004,2029,2025,2026,2027,2036,2005,2030,2031,2032,2033,2037,2006,2034,2038,2007,2035,2039,2040,2008,2041,2042,2043,2044,2045,2009,2046,2047,2048,2010,2050,2049,2051,2052,2011,2012,2013],\"params\":{},\"roleId\":100,\"roleKey\":\"manager\",\"roleName\":\"管理员\",\"roleSort\":1,\"status\":\"0\",\"updateBy\":\"boss\"}','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,'2025-04-27 03:04:17',61),(236,'角色管理',2,'com.retail.project.system.controller.SysRoleController.edit()','PUT',1,'admin','总部','/system/role','127.0.0.1','内网IP','{\"admin\":false,\"createTime\":\"2025-04-25 08:57:46\",\"dataScope\":\"1\",\"delFlag\":\"0\",\"deptCheckStrictly\":true,\"flag\":false,\"menuCheckStrictly\":true,\"menuIds\":[1,100,1000,1001,1002,1003,1004,1005,1006,101,1007,1008,1009,1010,1011,102,1012,1013,1014,1015,103,1016,1017,1018,1019,104,1020,1021,1022,1023,1024,105,1025,1026,1027,1028,1029,106,1030,1031,1032,1033,1034,107,1035,1036,1037,1038,108,500,1039,1040,1041,501,1042,1043,1044,1045,2000,2001,2017,2014,2015,2016,2002,2018,2019,2020,2021,2003,2028,2022,2023,2024,2004,2029,2025,2026,2027,2036,2005,2030,2031,2032,2033,2037,2006,2034,2038,2007,2035,2039,2040,2008,2041,2042,2043,2044,2045,2009,2046,2047,2048,2010,2050,2049,2051,2052,2011,2012,2013],\"params\":{},\"roleId\":100,\"roleKey\":\"manager\",\"roleName\":\"管理员\",\"roleSort\":1,\"status\":\"0\",\"updateBy\":\"admin\"}','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,'2025-04-27 03:05:26',48),(237,'菜单管理',2,'com.retail.project.system.controller.SysMenuController.edit()','PUT',1,'admin','总部','/system/menu','127.0.0.1','内网IP','{\"children\":[],\"component\":\"retail/channel/index\",\"createTime\":\"2025-04-25 08:37:45\",\"icon\":\"international\",\"isCache\":\"0\",\"isFrame\":\"1\",\"menuId\":2003,\"menuName\":\"渠道管理\",\"menuType\":\"C\",\"orderNum\":3,\"params\":{},\"parentId\":2000,\"path\":\"channels\",\"perms\":\"retail:channel:list\",\"routeName\":\"\",\"status\":\"0\",\"updateBy\":\"admin\",\"visible\":\"0\"}','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,'2025-04-27 03:10:35',20),(238,'角色管理',2,'com.retail.project.system.controller.SysRoleController.edit()','PUT',1,'admin','总部','/system/role','127.0.0.1','内网IP','{\"admin\":false,\"createTime\":\"2025-04-25 08:57:46\",\"dataScope\":\"1\",\"delFlag\":\"0\",\"deptCheckStrictly\":true,\"flag\":false,\"menuCheckStrictly\":true,\"menuIds\":[1,100,1000,1001,1002,1003,1004,1005,1006,101,1007,1008,1009,1010,1011,102,1012,1013,1014,1015,103,1016,1017,1018,1019,104,1020,1021,1022,1023,1024,105,1025,1026,1027,1028,1029,106,1030,1031,1032,1033,1034,107,1035,1036,1037,1038,108,500,1039,1040,1041,501,1042,1043,1044,1045,2000,2001,2017,2014,2015,2016,2002,2018,2019,2020,2021,2003,2028,2022,2023,2024,2004,2029,2025,2026,2027,2036,2005,2030,2031,2032,2033,2037,2006,2034,2038,2007,2035,2039,2040,2008,2041,2042,2043,2044,2045,2009,2046,2047,2048,2010,2050,2049,2051,2052,2011,2012,2013],\"params\":{},\"roleId\":100,\"roleKey\":\"manager\",\"roleName\":\"管理员\",\"roleSort\":1,\"status\":\"0\",\"updateBy\":\"admin\"}','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,'2025-04-27 03:10:45',40),(239,'菜单管理',1,'com.retail.project.system.controller.SysMenuController.add()','POST',1,'boss','总部','/system/menu','127.0.0.1','内网IP','{\"children\":[],\"component\":\"member/index\",\"createBy\":\"boss\",\"icon\":\"user\",\"isCache\":\"0\",\"isFrame\":\"1\",\"menuName\":\"会员系统\",\"menuType\":\"C\",\"orderNum\":5,\"params\":{},\"parentId\":0,\"path\":\"member\",\"perms\":\"member:member:list\",\"status\":\"0\",\"visible\":\"0\"}','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,'2025-04-27 03:58:33',31),(240,'菜单管理',2,'com.retail.project.system.controller.SysMenuController.edit()','PUT',1,'admin','总部','/system/menu','127.0.0.1','内网IP','{\"children\":[],\"createTime\":\"2025-04-25 08:49:47\",\"icon\":\"druid\",\"isCache\":\"0\",\"isFrame\":\"1\",\"menuId\":2011,\"menuName\":\"统计分析\",\"menuType\":\"M\",\"orderNum\":6,\"params\":{},\"parentId\":0,\"path\":\"analysis\",\"perms\":\"\",\"routeName\":\"\",\"status\":\"0\",\"updateBy\":\"admin\",\"visible\":\"0\"}','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,'2025-04-27 03:59:07',28),(241,'菜单管理',2,'com.retail.project.system.controller.SysMenuController.edit()','PUT',1,'admin','总部','/system/menu','127.0.0.1','内网IP','{\"children\":[],\"component\":\"member/index\",\"createTime\":\"2025-04-27 03:58:33\",\"icon\":\"user\",\"isCache\":\"0\",\"isFrame\":\"1\",\"menuId\":2053,\"menuName\":\"会员系统\",\"menuType\":\"M\",\"orderNum\":5,\"params\":{},\"parentId\":0,\"path\":\"member\",\"perms\":\"member:member:list\",\"routeName\":\"\",\"status\":\"0\",\"updateBy\":\"admin\",\"visible\":\"0\"}','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,'2025-04-27 04:37:46',31),(242,'菜单管理',2,'com.retail.project.system.controller.SysMenuController.edit()','PUT',1,'admin','总部','/system/menu','127.0.0.1','内网IP','{\"children\":[],\"component\":\"member/index\",\"createTime\":\"2025-04-27 03:58:33\",\"icon\":\"user\",\"isCache\":\"0\",\"isFrame\":\"1\",\"menuId\":2053,\"menuName\":\"会员系统\",\"menuType\":\"C\",\"orderNum\":5,\"params\":{},\"parentId\":0,\"path\":\"member\",\"perms\":\"\",\"routeName\":\"\",\"status\":\"0\",\"updateBy\":\"admin\",\"visible\":\"0\"}','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,'2025-04-27 04:37:57',18),(243,'菜单管理',2,'com.retail.project.system.controller.SysMenuController.edit()','PUT',1,'admin','总部','/system/menu','127.0.0.1','内网IP','{\"children\":[],\"component\":\"member/index\",\"createTime\":\"2025-04-27 03:58:33\",\"icon\":\"user\",\"isCache\":\"0\",\"isFrame\":\"1\",\"menuId\":2053,\"menuName\":\"会员系统\",\"menuType\":\"M\",\"orderNum\":5,\"params\":{},\"parentId\":0,\"path\":\"member\",\"perms\":\"\",\"routeName\":\"\",\"status\":\"0\",\"updateBy\":\"admin\",\"visible\":\"0\"}','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,'2025-04-27 04:38:02',20),(244,'菜单管理',2,'com.retail.project.system.controller.SysMenuController.edit()','PUT',1,'admin','总部','/system/menu','127.0.0.1','内网IP','{\"children\":[],\"component\":\"\",\"createTime\":\"2025-04-27 03:58:33\",\"icon\":\"user\",\"isCache\":\"0\",\"isFrame\":\"1\",\"menuId\":2053,\"menuName\":\"会员系统\",\"menuType\":\"C\",\"orderNum\":5,\"params\":{},\"parentId\":0,\"path\":\"member\",\"perms\":\"\",\"routeName\":\"\",\"status\":\"0\",\"updateBy\":\"admin\",\"visible\":\"0\"}','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,'2025-04-27 04:38:10',18),(245,'菜单管理',2,'com.retail.project.system.controller.SysMenuController.edit()','PUT',1,'admin','总部','/system/menu','127.0.0.1','内网IP','{\"children\":[],\"component\":\"\",\"createTime\":\"2025-04-27 03:58:33\",\"icon\":\"user\",\"isCache\":\"0\",\"isFrame\":\"1\",\"menuId\":2053,\"menuName\":\"会员系统\",\"menuType\":\"M\",\"orderNum\":5,\"params\":{},\"parentId\":0,\"path\":\"member\",\"perms\":\"\",\"routeName\":\"\",\"status\":\"0\",\"updateBy\":\"admin\",\"visible\":\"0\"}','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,'2025-04-27 04:38:13',19),(246,'菜单管理',1,'com.retail.project.system.controller.SysMenuController.add()','POST',1,'admin','总部','/system/menu','127.0.0.1','内网IP','{\"children\":[],\"component\":\"member/member/index\",\"createBy\":\"admin\",\"icon\":\"people\",\"isCache\":\"0\",\"isFrame\":\"1\",\"menuName\":\"会员信息\",\"menuType\":\"C\",\"orderNum\":1,\"params\":{},\"parentId\":2053,\"path\":\"member\",\"perms\":\"member:member:list\",\"status\":\"0\",\"visible\":\"0\"}','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,'2025-04-27 04:39:04',29),(247,'菜单管理',1,'com.retail.project.system.controller.SysMenuController.add()','POST',1,'admin','总部','/system/menu','127.0.0.1','内网IP','{\"children\":[],\"component\":\"member/level/index\",\"createBy\":\"admin\",\"icon\":\"tree\",\"isCache\":\"0\",\"isFrame\":\"1\",\"menuName\":\"会员等级\",\"menuType\":\"C\",\"orderNum\":2,\"params\":{},\"parentId\":2053,\"path\":\"level\",\"perms\":\"member:level:list\",\"status\":\"0\",\"visible\":\"0\"}','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,'2025-04-27 04:40:10',26),(248,'会员等级',2,'com.retail.project.member.controller.MemberLevelController.edit()','PUT',1,'admin','总部','/member/level','127.0.0.1','内网IP','{\"createBy\":\"admin\",\"createTime\":\"2025-04-27 03:55:44\",\"discountRate\":1,\"levelId\":2,\"levelName\":\"银卡会员\",\"maxAmount\":4999.99,\"minAmount\":1000,\"params\":{},\"remark\":\"消费1000-4999元\",\"status\":\"0\",\"tagType\":\"info\",\"updateBy\":\"\"}','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,'2025-04-27 04:40:48',26),(249,'会员等级',2,'com.retail.project.member.controller.MemberLevelController.edit()','PUT',1,'admin','总部','/member/level','127.0.0.1','内网IP','{\"createBy\":\"admin\",\"createTime\":\"2025-04-27 03:55:44\",\"discountRate\":1,\"levelId\":3,\"levelName\":\"金卡会员\",\"maxAmount\":9999.99,\"minAmount\":5000,\"params\":{},\"remark\":\"消费5000-9999元\",\"status\":\"0\",\"tagType\":\"warning\",\"updateBy\":\"\"}','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,'2025-04-27 04:40:52',16),(250,'会员等级',2,'com.retail.project.member.controller.MemberLevelController.edit()','PUT',1,'admin','总部','/member/level','127.0.0.1','内网IP','{\"createBy\":\"admin\",\"createTime\":\"2025-04-27 03:55:44\",\"discountRate\":1,\"levelId\":4,\"levelName\":\"VIP会员\",\"maxAmount\":49999.99,\"minAmount\":10000,\"params\":{},\"remark\":\"消费10000-49999元\",\"status\":\"0\",\"tagType\":\"success\",\"updateBy\":\"\"}','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,'2025-04-27 04:40:56',11),(251,'会员等级',2,'com.retail.project.member.controller.MemberLevelController.edit()','PUT',1,'admin','总部','/member/level','127.0.0.1','内网IP','{\"createBy\":\"admin\",\"createTime\":\"2025-04-27 03:55:44\",\"discountRate\":1,\"levelId\":5,\"levelName\":\"钻石会员\",\"maxAmount\":999999.99,\"minAmount\":50000,\"params\":{},\"remark\":\"消费50000元以上\",\"status\":\"0\",\"tagType\":\"danger\",\"updateBy\":\"\"}','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,'2025-04-27 04:41:02',14),(252,'角色管理',2,'com.retail.project.system.controller.SysRoleController.edit()','PUT',1,'admin','总部','/system/role','127.0.0.1','内网IP','{\"admin\":false,\"createTime\":\"2025-04-25 08:57:46\",\"dataScope\":\"1\",\"delFlag\":\"0\",\"deptCheckStrictly\":true,\"flag\":false,\"menuCheckStrictly\":true,\"menuIds\":[1,100,1000,1001,1002,1003,1004,1005,1006,101,1007,1008,1009,1010,1011,102,1012,1013,1014,1015,103,1016,1017,1018,1019,104,1020,1021,1022,1023,1024,105,1025,1026,1027,1028,1029,106,1030,1031,1032,1033,1034,107,1035,1036,1037,1038,108,500,1039,1040,1041,501,1042,1043,1044,1045,2000,2001,2017,2014,2015,2016,2002,2018,2019,2020,2021,2003,2028,2022,2023,2024,2004,2029,2025,2026,2027,2036,2005,2030,2031,2032,2033,2037,2006,2034,2038,2007,2035,2039,2040,2008,2041,2042,2043,2044,2045,2009,2046,2047,2048,2010,2050,2049,2051,2052,2053,2054,2055,2011,2012,2013],\"params\":{},\"roleId\":100,\"roleKey\":\"manager\",\"roleName\":\"管理员\",\"roleSort\":1,\"status\":\"0\",\"updateBy\":\"admin\"}','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,'2025-04-27 07:21:11',56),(253,'角色管理',2,'com.retail.project.system.controller.SysRoleController.edit()','PUT',1,'admin','总部','/system/role','127.0.0.1','内网IP','{\"admin\":false,\"createTime\":\"2025-04-25 08:58:13\",\"dataScope\":\"1\",\"delFlag\":\"0\",\"deptCheckStrictly\":true,\"flag\":false,\"menuCheckStrictly\":true,\"menuIds\":[2000,2001,2017,2014,2015,2016,2002,2018,2019,2020,2021,2003,2028,2022,2023,2024,2004,2029,2025,2026,2027,2036,2005,2030,2031,2032,2033,2037,2006,2034,2038,2007,2035,2039,2040,2008,2041,2042,2043,2044,2045,2009,2046,2047,2048,2053,2054,2055],\"params\":{},\"roleId\":101,\"roleKey\":\"finance\",\"roleName\":\"财务\",\"roleSort\":3,\"status\":\"0\",\"updateBy\":\"admin\"}','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,'2025-04-27 07:21:18',43),(254,'菜单管理',1,'com.retail.project.system.controller.SysMenuController.add()','POST',1,'admin','总部','/system/menu','127.0.0.1','内网IP','{\"children\":[],\"createBy\":\"admin\",\"isCache\":\"0\",\"isFrame\":\"1\",\"menuName\":\"会员新增\",\"menuType\":\"F\",\"orderNum\":1,\"params\":{},\"parentId\":2054,\"perms\":\"member:member:add\",\"status\":\"0\",\"visible\":\"0\"}','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,'2025-04-27 07:22:22',34),(255,'菜单管理',1,'com.retail.project.system.controller.SysMenuController.add()','POST',1,'admin','总部','/system/menu','127.0.0.1','内网IP','{\"children\":[],\"createBy\":\"admin\",\"isCache\":\"0\",\"isFrame\":\"1\",\"menuName\":\"会员查询\",\"menuType\":\"F\",\"orderNum\":0,\"params\":{},\"parentId\":2054,\"perms\":\"member:member:query\",\"status\":\"0\",\"visible\":\"0\"}','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,'2025-04-27 07:23:09',23),(256,'菜单管理',2,'com.retail.project.system.controller.SysMenuController.edit()','PUT',1,'admin','总部','/system/menu','127.0.0.1','内网IP','{\"children\":[],\"createTime\":\"2025-04-27 07:23:09\",\"icon\":\"#\",\"isCache\":\"0\",\"isFrame\":\"1\",\"menuId\":2057,\"menuName\":\"会员查看\",\"menuType\":\"F\",\"orderNum\":0,\"params\":{},\"parentId\":2054,\"path\":\"\",\"perms\":\"member:member:query\",\"routeName\":\"\",\"status\":\"0\",\"updateBy\":\"admin\",\"visible\":\"0\"}','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,'2025-04-27 07:23:24',21),(257,'菜单管理',1,'com.retail.project.system.controller.SysMenuController.add()','POST',1,'admin','总部','/system/menu','127.0.0.1','内网IP','{\"children\":[],\"createBy\":\"admin\",\"isCache\":\"0\",\"isFrame\":\"1\",\"menuName\":\"会员修改\",\"menuType\":\"F\",\"orderNum\":2,\"params\":{},\"parentId\":2054,\"perms\":\"member:member:edit\",\"status\":\"0\",\"visible\":\"0\"}','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,'2025-04-27 07:23:48',18),(258,'菜单管理',1,'com.retail.project.system.controller.SysMenuController.add()','POST',1,'admin','总部','/system/menu','127.0.0.1','内网IP','{\"children\":[],\"createBy\":\"admin\",\"isCache\":\"0\",\"isFrame\":\"1\",\"menuName\":\"会员删除\",\"menuType\":\"F\",\"orderNum\":3,\"params\":{},\"parentId\":2054,\"perms\":\"member:member:remove\",\"status\":\"0\",\"visible\":\"0\"}','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,'2025-04-27 07:24:12',40),(259,'菜单管理',1,'com.retail.project.system.controller.SysMenuController.add()','POST',1,'admin','总部','/system/menu','127.0.0.1','内网IP','{\"children\":[],\"createBy\":\"admin\",\"isCache\":\"0\",\"isFrame\":\"1\",\"menuName\":\"等级查看\",\"menuType\":\"F\",\"orderNum\":1,\"params\":{},\"parentId\":2055,\"perms\":\"member:level:query\",\"status\":\"0\",\"visible\":\"0\"}','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,'2025-04-27 07:24:40',25),(260,'菜单管理',1,'com.retail.project.system.controller.SysMenuController.add()','POST',1,'admin','总部','/system/menu','127.0.0.1','内网IP','{\"children\":[],\"createBy\":\"admin\",\"isCache\":\"0\",\"isFrame\":\"1\",\"menuName\":\"等级新增\",\"menuType\":\"F\",\"orderNum\":2,\"params\":{},\"parentId\":2055,\"perms\":\"member:level:add\",\"status\":\"0\",\"visible\":\"0\"}','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,'2025-04-27 07:25:29',31),(261,'菜单管理',1,'com.retail.project.system.controller.SysMenuController.add()','POST',1,'admin','总部','/system/menu','127.0.0.1','内网IP','{\"children\":[],\"createBy\":\"admin\",\"isCache\":\"0\",\"isFrame\":\"1\",\"menuName\":\"等级修改\",\"menuType\":\"F\",\"orderNum\":3,\"params\":{},\"parentId\":2055,\"perms\":\"member:level:edit\",\"status\":\"0\",\"visible\":\"0\"}','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,'2025-04-27 07:26:12',18),(262,'菜单管理',1,'com.retail.project.system.controller.SysMenuController.add()','POST',1,'admin','总部','/system/menu','127.0.0.1','内网IP','{\"children\":[],\"createBy\":\"admin\",\"isCache\":\"0\",\"isFrame\":\"1\",\"menuName\":\"等级删除\",\"menuType\":\"F\",\"orderNum\":4,\"params\":{},\"parentId\":2055,\"perms\":\"member:level:remove\",\"status\":\"0\",\"visible\":\"0\"}','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,'2025-04-27 07:26:36',19),(263,'角色管理',2,'com.retail.project.system.controller.SysRoleController.edit()','PUT',1,'admin','总部','/system/role','127.0.0.1','内网IP','{\"admin\":false,\"createTime\":\"2025-04-25 08:57:46\",\"dataScope\":\"1\",\"delFlag\":\"0\",\"deptCheckStrictly\":true,\"flag\":false,\"menuCheckStrictly\":true,\"menuIds\":[1,100,1000,1001,1002,1003,1004,1005,1006,101,1007,1008,1009,1010,1011,102,1012,1013,1014,1015,103,1016,1017,1018,1019,104,1020,1021,1022,1023,1024,105,1025,1026,1027,1028,1029,106,1030,1031,1032,1033,1034,107,1035,1036,1037,1038,108,500,1039,1040,1041,501,1042,1043,1044,1045,2000,2001,2017,2014,2015,2016,2002,2018,2019,2020,2021,2003,2028,2022,2023,2024,2004,2029,2025,2026,2027,2036,2005,2030,2031,2032,2033,2037,2006,2034,2038,2007,2035,2039,2040,2008,2041,2042,2043,2044,2045,2009,2046,2047,2048,2010,2050,2049,2051,2052,2053,2054,2057,2056,2058,2059,2055,2060,2061,2062,2063,2011,2012,2013],\"params\":{},\"roleId\":100,\"roleKey\":\"manager\",\"roleName\":\"管理员\",\"roleSort\":1,\"status\":\"0\",\"updateBy\":\"admin\"}','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,'2025-04-27 07:26:43',55),(264,'角色管理',2,'com.retail.project.system.controller.SysRoleController.edit()','PUT',1,'admin','总部','/system/role','127.0.0.1','内网IP','{\"admin\":false,\"createTime\":\"2025-04-25 08:58:13\",\"dataScope\":\"1\",\"delFlag\":\"0\",\"deptCheckStrictly\":true,\"flag\":false,\"menuCheckStrictly\":true,\"menuIds\":[2000,2001,2017,2014,2015,2016,2002,2018,2019,2020,2021,2003,2028,2022,2023,2024,2004,2029,2025,2026,2027,2036,2005,2030,2031,2032,2033,2037,2006,2034,2038,2007,2035,2039,2040,2008,2041,2042,2043,2044,2045,2009,2046,2047,2048,2053,2054,2057,2056,2058,2059,2055,2060,2061,2062,2063],\"params\":{},\"roleId\":101,\"roleKey\":\"finance\",\"roleName\":\"财务\",\"roleSort\":3,\"status\":\"0\",\"updateBy\":\"admin\"}','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,'2025-04-27 07:26:47',40),(265,'角色管理',2,'com.retail.project.system.controller.SysRoleController.edit()','PUT',1,'admin','总部','/system/role','127.0.0.1','内网IP','{\"admin\":false,\"createTime\":\"2025-04-25 08:58:51\",\"dataScope\":\"1\",\"delFlag\":\"0\",\"deptCheckStrictly\":true,\"flag\":false,\"menuCheckStrictly\":true,\"menuIds\":[2000,2008,2053,2054,2055,2007,2035,2039,2040,2041,2042,2045,2009,2046,2047,2048,2057,2056,2058,2060],\"params\":{},\"roleId\":102,\"roleKey\":\"store\",\"roleName\":\"门店\",\"roleSort\":4,\"status\":\"0\",\"updateBy\":\"admin\"}','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,'2025-04-27 07:27:02',36),(266,'入库单',1,'com.retail.project.retail.controller.InventoryInOrderController.saveWithDetails()','POST',1,'finance','总部','/retail/inorder/save','127.0.0.1','内网IP','{\"channelId\":1,\"createBy\":\"finance\",\"createTime\":\"2025-04-27 15:40:39\",\"detailList\":[{\"batchNumber\":\"4c856ff795\",\"counterId\":1,\"createBy\":\"finance\",\"id\":7,\"orderId\":4,\"params\":{},\"productId\":1,\"productImage\":\"/profile/upload/2025/04/25/bag1_20250425180819A001.jpg\",\"productName\":\"COACH 背提包-2525001\",\"purchasePrice\":2000,\"quantity\":10,\"storeId\":1},{\"batchNumber\":\"4c856ff795\",\"counterId\":1,\"createBy\":\"finance\",\"id\":8,\"orderId\":4,\"params\":{},\"productId\":2,\"productImage\":\"/profile/upload/2025/04/27/bag2_20250427092057A001.jpg\",\"productName\":\"COACH 背提包-2524996\",\"purchasePrice\":2300,\"quantity\":10,\"storeId\":1},{\"batchNumber\":\"4c856ff795\",\"counterId\":2,\"createBy\":\"finance\",\"id\":9,\"orderId\":4,\"params\":{},\"productId\":1,\"productImage\":\"/profile/upload/2025/04/25/bag1_20250425180819A001.jpg\",\"productName\":\"COACH 背提包-2525001\",\"purchasePrice\":2100,\"quantity\":5,\"storeId\":2},{\"batchNumber\":\"4c856ff795\",\"counterId\":2,\"createBy\":\"finance\",\"id\":10,\"orderId\":4,\"params\":{},\"productId\":2,\"productImage\":\"/profile/upload/2025/04/27/bag2_20250427092057A001.jpg\",\"productName\":\"COACH 背提包-2524996\",\"purchasePrice\":2200,\"quantity\":5,\"storeId\":2}],\"id\":4,\"isFutures\":false,\"orderNumber\":\"IN1580667627\",\"params\":{}}','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,'2025-04-27 07:40:39',80),(267,'会员',1,'com.retail.project.member.controller.MemberController.add()','POST',1,'finance','总部','/member','127.0.0.1','内网IP','{\"birthday\":\"1996-01-01 00:00:00\",\"gender\":\"1\",\"memberId\":2504275355,\"memberName\":\"张先生\",\"params\":{},\"phoneNumber\":\"13344445555\",\"remark\":\"\"}','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,'2025-04-27 07:58:45',82),(268,'商品售出',1,'com.retail.project.retail.controller.OutOrderController.sale()','POST',1,'finance','总部','/retail/outorder/sale','127.0.0.1','内网IP','{\"payType\":1,\"quantity\":1,\"remark\":\"\",\"salePrice\":4980,\"stockId\":7}','{\"msg\":\"客户姓名不能为空\",\"code\":500}',0,NULL,'2025-04-27 08:06:07',5),(269,'字典数据',2,'com.retail.project.system.controller.SysDictDataController.edit()','PUT',1,'admin','总部','/system/dict/data','127.0.0.1','内网IP','{\"createBy\":\"admin\",\"createTime\":\"2025-04-25 08:01:03\",\"cssClass\":\"\",\"default\":true,\"dictCode\":1,\"dictLabel\":\"男\",\"dictSort\":1,\"dictType\":\"sys_user_sex\",\"dictValue\":\"1\",\"isDefault\":\"Y\",\"listClass\":\"\",\"params\":{},\"remark\":\"性别男\",\"status\":\"0\",\"updateBy\":\"admin\"}','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,'2025-04-27 08:20:18',91),(270,'字典数据',2,'com.retail.project.system.controller.SysDictDataController.edit()','PUT',1,'admin','总部','/system/dict/data','127.0.0.1','内网IP','{\"createBy\":\"admin\",\"createTime\":\"2025-04-25 08:01:03\",\"cssClass\":\"\",\"default\":false,\"dictCode\":2,\"dictLabel\":\"女\",\"dictSort\":2,\"dictType\":\"sys_user_sex\",\"dictValue\":\"0\",\"isDefault\":\"N\",\"listClass\":\"\",\"params\":{},\"remark\":\"性别女\",\"status\":\"0\",\"updateBy\":\"admin\"}','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,'2025-04-27 08:20:22',24),(271,'字典类型',3,'com.retail.project.system.controller.SysDictDataController.remove()','DELETE',1,'admin','总部','/system/dict/data/3','127.0.0.1','内网IP','[3]','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,'2025-04-27 08:20:31',31),(272,'字典数据',2,'com.retail.project.system.controller.SysDictDataController.edit()','PUT',1,'admin','总部','/system/dict/data','127.0.0.1','内网IP','{\"createBy\":\"admin\",\"createTime\":\"2025-04-25 08:01:03\",\"cssClass\":\"\",\"default\":false,\"dictCode\":2,\"dictLabel\":\"女\",\"dictSort\":0,\"dictType\":\"sys_user_sex\",\"dictValue\":\"0\",\"isDefault\":\"N\",\"listClass\":\"\",\"params\":{},\"remark\":\"性别女\",\"status\":\"0\",\"updateBy\":\"admin\"}','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,'2025-04-27 08:20:42',16),(273,'会员',1,'com.retail.project.member.controller.MemberController.add()','POST',1,'store1','门店','/member','127.0.0.1','内网IP','{\"birthday\":\"1996-01-01 00:00:00\",\"createBy\":\"store1\",\"gender\":1,\"memberId\":2504277155,\"memberName\":\"张先生\",\"params\":{},\"phoneNumber\":\"13344445555\",\"remark\":\"\"}','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,'2025-04-27 16:37:12',36),(274,'商品售出',1,'com.retail.project.retail.controller.OutOrderController.sale()','POST',1,'store1','门店','/retail/outorder/sale','127.0.0.1','内网IP','{\"memberId\":2504277155,\"payType\":1,\"quantity\":1,\"remark\":\"\",\"salePrice\":4980,\"stockId\":7}','{\"msg\":\"客户姓名不能为空\",\"code\":500}',0,NULL,'2025-04-27 16:37:18',7),(275,'商品售出',1,'com.retail.project.retail.controller.OutOrderController.sale()','POST',1,'store1','门店','/retail/outorder/sale','127.0.0.1','内网IP','{\"memberId\":2504277155,\"payType\":1,\"quantity\":1,\"remark\":\"\",\"salePrice\":4980,\"stockId\":7}','{\"msg\":\"客户姓名不能为空\",\"code\":500}',0,NULL,'2025-04-27 16:37:37',6),(276,'商品售出',1,'com.retail.project.retail.controller.OutOrderController.sale()','POST',1,'store1','门店','/retail/outorder/sale','127.0.0.1','内网IP','{\"memberId\":2504277155,\"payType\":1,\"quantity\":1,\"remark\":\"\",\"salePrice\":4980,\"stockId\":7}','{\"msg\":\"商品售出成功\",\"code\":200}',0,NULL,'2025-04-27 16:55:22',103),(277,'审核出库单',2,'com.retail.project.retail.controller.InventoryOutOrderController.approve()','POST',1,'finance','总部','/retail/outorder/approve','127.0.0.1','内网IP','{\"id\":4,\"remark\":\"同意\"}','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,'2025-04-27 17:35:30',111),(278,'申请退货',2,'com.retail.project.retail.controller.InventoryOutOrderController.applyReturn()','POST',1,'store1','门店','/retail/outorder/apply-return','127.0.0.1','内网IP','{\"id\":4,\"returnReason\":\"测试\"}','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,'2025-04-27 17:53:25',70),(279,'审核退货申请',2,'com.retail.project.retail.controller.InventoryOutOrderController.approveReturn()','POST',1,'finance','总部','/retail/outorder/approve-return','127.0.0.1','内网IP','{\"id\":4,\"remark\":\"1\"}','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,'2025-04-27 17:53:47',76),(280,'会员',1,'com.retail.project.member.controller.MemberController.add()','POST',1,'store1','门店','/member','127.0.0.1','内网IP','{\"birthday\":\"1996-01-01 00:00:00\",\"createBy\":\"store1\",\"gender\":0,\"memberId\":2504277195,\"memberName\":\"张女士\",\"params\":{},\"phoneNumber\":\"13355556666\",\"remark\":\"\"}','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,'2025-04-27 17:56:17',26),(281,'商品售出',1,'com.retail.project.retail.controller.OutOrderController.sale()','POST',1,'store1','门店','/retail/outorder/sale','127.0.0.1','内网IP','{\"memberId\":2504277195,\"payType\":1,\"quantity\":1,\"remark\":\"\",\"salePrice\":4880,\"stockId\":7}','{\"msg\":\"商品售出成功\",\"code\":200}',0,NULL,'2025-04-28 09:13:27',67),(282,'审核出库单',2,'com.retail.project.retail.controller.InventoryOutOrderController.approve()','POST',1,'finance','总部','/retail/outorder/approve','127.0.0.1','内网IP','{\"id\":5,\"remark\":\"\"}','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,'2025-04-28 09:13:46',66),(283,'批量商品售出',1,'com.retail.project.retail.controller.OutOrderController.batchSale()','POST',1,'store1','门店','/retail/outorder/batch-sale','127.0.0.1','内网IP','{\"memberId\":2504277195,\"payType\":1,\"remark\":\"\",\"stocks\":[{\"productId\":1,\"quantity\":1,\"salePrice\":4980,\"stockId\":7},{\"productId\":2,\"quantity\":1,\"salePrice\":3300,\"stockId\":8}]}','{\"msg\":\"批量售出成功\",\"code\":200}',0,NULL,'2025-04-28 09:57:27',102),(284,'撤销出库单',2,'com.retail.project.retail.controller.InventoryOutOrderController.cancel()','POST',1,'store1','门店','/retail/outorder/cancel','127.0.0.1','内网IP','{\"id\":6,\"cancelReason\":\"计算错误\"}','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,'2025-04-28 10:02:24',79),(285,'批量商品售出',1,'com.retail.project.retail.controller.OutOrderController.batchSale()','POST',1,'store1','门店','/retail/outorder/batch-sale','127.0.0.1','内网IP','{\"memberId\":2504277195,\"payType\":3,\"remark\":\"\",\"stocks\":[{\"productId\":1,\"quantity\":1,\"salePrice\":4980,\"stockId\":7},{\"productId\":2,\"quantity\":1,\"salePrice\":3300,\"stockId\":8}],\"totalSalePrice\":8080}','{\"msg\":\"批量售出成功\",\"code\":200}',0,NULL,'2025-04-28 10:03:20',78),(286,'审核出库单',2,'com.retail.project.retail.controller.InventoryOutOrderController.approve()','POST',1,'finance','总部','/retail/outorder/approve','127.0.0.1','内网IP','{\"id\":7,\"remark\":\"1\"}','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,'2025-04-28 10:04:13',50),(287,'批量商品售出',1,'com.retail.project.retail.controller.OutOrderController.batchSale()','POST',1,'store1','门店','/retail/outorder/batch-sale','127.0.0.1','内网IP','{\"memberId\":2504277155,\"payType\":2,\"remark\":\"\",\"stocks\":[{\"productId\":2,\"quantity\":9,\"salePrice\":3300,\"stockId\":8}],\"totalSalePrice\":29700}','{\"msg\":\"批量售出成功\",\"code\":200}',0,NULL,'2025-04-28 10:36:17',70),(288,'撤销出库单',2,'com.retail.project.retail.controller.InventoryOutOrderController.cancel()','POST',1,'store1','门店','/retail/outorder/cancel','127.0.0.1','内网IP','{\"id\":8,\"cancelReason\":\"1\"}','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,'2025-04-28 10:36:27',33),(289,'商品售出',1,'com.retail.project.retail.controller.OutOrderController.sale()','POST',1,'boss','总部','/retail/outorder/sale','127.0.0.1','内网IP','{\"payType\":1,\"quantity\":1,\"remark\":\"\",\"salePrice\":4980,\"stockId\":7}','{\"msg\":\"商品售出成功\",\"code\":200}',0,NULL,'2025-04-28 10:45:16',73),(290,'驳回出库单',2,'com.retail.project.retail.controller.InventoryOutOrderController.reject()','POST',1,'boss','总部','/retail/outorder/reject','127.0.0.1','内网IP','{\"id\":9,\"rejectReason\":\"1\"}','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,'2025-04-28 10:45:35',76),(291,'商品售出',1,'com.retail.project.retail.controller.OutOrderController.sale()','POST',1,'store1','门店','/retail/outorder/sale','127.0.0.1','内网IP','{\"payType\":1,\"quantity\":1,\"remark\":\"\",\"salePrice\":4980,\"stockId\":7}','{\"msg\":\"会员ID不能为空\",\"code\":500}',0,NULL,'2025-04-28 10:50:32',13),(292,'商品售出',1,'com.retail.project.retail.controller.OutOrderController.sale()','POST',1,'store1','门店','/retail/outorder/sale','127.0.0.1','内网IP','{\"memberId\":2504277155,\"payType\":1,\"quantity\":1,\"remark\":\"\",\"salePrice\":4980,\"stockId\":7}','{\"msg\":\"商品售出成功\",\"code\":200}',0,NULL,'2025-04-28 10:50:49',66),(293,'商品售出',1,'com.retail.project.retail.controller.OutOrderController.sale()','POST',1,'store1','门店','/retail/outorder/sale','127.0.0.1','内网IP','{\"memberId\":2504277155,\"payType\":1,\"quantity\":1,\"remark\":\"\",\"salePrice\":4980,\"stockId\":7}','{\"msg\":\"商品售出成功\",\"code\":200}',0,NULL,'2025-04-28 10:54:24',54),(294,'批量商品售出',1,'com.retail.project.retail.controller.OutOrderController.batchSale()','POST',1,'store1','门店','/retail/outorder/batch-sale','127.0.0.1','内网IP','{\"payType\":1,\"remark\":\"\",\"stocks\":[{\"productId\":2,\"quantity\":1,\"salePrice\":3300,\"stockId\":8}],\"totalSalePrice\":3300}','{\"msg\":\"会员ID不能为空\",\"code\":500}',0,NULL,'2025-04-28 11:03:41',13),(295,'批量商品售出',1,'com.retail.project.retail.controller.OutOrderController.batchSale()','POST',1,'store1','门店','/retail/outorder/batch-sale','127.0.0.1','内网IP','{\"memberId\":2504277155,\"payType\":2,\"remark\":\"\",\"stocks\":[{\"productId\":1,\"quantity\":6,\"salePrice\":4980,\"stockId\":7}],\"totalSalePrice\":28880}','{\"msg\":\"批量售出成功\",\"code\":200}',0,NULL,'2025-04-28 11:04:28',62),(296,'审核出库单',2,'com.retail.project.retail.controller.InventoryOutOrderController.approve()','POST',1,'boss','总部','/retail/outorder/approve','127.0.0.1','内网IP','{\"id\":10,\"remark\":\"\"}','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,'2025-04-28 11:05:09',55),(297,'审核出库单',2,'com.retail.project.retail.controller.InventoryOutOrderController.approve()','POST',1,'boss','总部','/retail/outorder/approve','127.0.0.1','内网IP','{\"id\":11,\"remark\":\"\"}','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,'2025-04-28 11:05:11',54),(298,'审核出库单',2,'com.retail.project.retail.controller.InventoryOutOrderController.approve()','POST',1,'boss','总部','/retail/outorder/approve','127.0.0.1','内网IP','{\"id\":12,\"remark\":\"\"}','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,'2025-04-28 11:05:13',45),(299,'菜单管理',1,'com.retail.project.system.controller.SysMenuController.add()','POST',1,'admin','总部','/system/menu','127.0.0.1','内网IP','{\"children\":[],\"createBy\":\"admin\",\"isCache\":\"0\",\"isFrame\":\"1\",\"menuName\":\"商品预订\",\"menuType\":\"F\",\"orderNum\":4,\"params\":{},\"parentId\":2007,\"perms\":\"retail:storestock:reserve\",\"status\":\"0\",\"visible\":\"0\"}','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,'2025-04-28 13:43:21',37),(300,'菜单管理',1,'com.retail.project.system.controller.SysMenuController.add()','POST',1,'admin','总部','/system/menu','127.0.0.1','内网IP','{\"children\":[],\"createBy\":\"admin\",\"isCache\":\"0\",\"isFrame\":\"1\",\"menuName\":\"订单交货\",\"menuType\":\"F\",\"orderNum\":4,\"params\":{},\"parentId\":2009,\"perms\":\"retail:outorder:deliver\",\"status\":\"0\",\"visible\":\"0\"}','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,'2025-04-28 13:44:37',26),(301,'角色管理',2,'com.retail.project.system.controller.SysRoleController.edit()','PUT',1,'admin','总部','/system/role','127.0.0.1','内网IP','{\"admin\":false,\"createTime\":\"2025-04-25 08:57:46\",\"dataScope\":\"1\",\"delFlag\":\"0\",\"deptCheckStrictly\":true,\"flag\":false,\"menuCheckStrictly\":true,\"menuIds\":[1,100,1000,1001,1002,1003,1004,1005,1006,101,1007,1008,1009,1010,1011,102,1012,1013,1014,1015,103,1016,1017,1018,1019,104,1020,1021,1022,1023,1024,105,1025,1026,1027,1028,1029,106,1030,1031,1032,1033,1034,107,1035,1036,1037,1038,108,500,1039,1040,1041,501,1042,1043,1044,1045,2000,2001,2017,2014,2015,2016,2002,2018,2019,2020,2021,2003,2028,2022,2023,2024,2004,2029,2025,2026,2027,2036,2005,2030,2031,2032,2033,2037,2006,2034,2038,2007,2035,2039,2040,2064,2008,2041,2042,2043,2044,2045,2009,2046,2047,2048,2065,2010,2050,2049,2051,2052,2053,2054,2057,2056,2058,2059,2055,2060,2061,2062,2063,2011,2012,2013],\"params\":{},\"roleId\":100,\"roleKey\":\"manager\",\"roleName\":\"管理员\",\"roleSort\":1,\"status\":\"0\",\"updateBy\":\"admin\"}','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,'2025-04-28 13:44:54',77),(302,'角色管理',2,'com.retail.project.system.controller.SysRoleController.edit()','PUT',1,'admin','总部','/system/role','127.0.0.1','内网IP','{\"admin\":false,\"createTime\":\"2025-04-25 08:58:13\",\"dataScope\":\"1\",\"delFlag\":\"0\",\"deptCheckStrictly\":true,\"flag\":false,\"menuCheckStrictly\":true,\"menuIds\":[2000,2001,2017,2014,2015,2016,2002,2018,2019,2020,2021,2003,2028,2022,2023,2024,2004,2029,2025,2026,2027,2036,2005,2030,2031,2032,2033,2037,2006,2034,2038,2007,2035,2039,2040,2064,2008,2041,2042,2043,2044,2045,2009,2046,2047,2048,2065,2053,2054,2057,2056,2058,2059,2055,2060,2061,2062,2063],\"params\":{},\"roleId\":101,\"roleKey\":\"finance\",\"roleName\":\"财务\",\"roleSort\":3,\"status\":\"0\",\"updateBy\":\"admin\"}','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,'2025-04-28 13:45:03',39),(303,'角色管理',2,'com.retail.project.system.controller.SysRoleController.edit()','PUT',1,'admin','总部','/system/role','127.0.0.1','内网IP','{\"admin\":false,\"createTime\":\"2025-04-25 08:58:51\",\"dataScope\":\"1\",\"delFlag\":\"0\",\"deptCheckStrictly\":true,\"flag\":false,\"menuCheckStrictly\":true,\"menuIds\":[2000,2008,2053,2054,2055,2007,2035,2039,2040,2064,2041,2042,2045,2009,2046,2047,2048,2065,2057,2056,2058,2060],\"params\":{},\"roleId\":102,\"roleKey\":\"store\",\"roleName\":\"门店\",\"roleSort\":4,\"status\":\"0\",\"updateBy\":\"admin\"}','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,'2025-04-28 13:45:26',31),(304,'创建预订单',1,'com.retail.project.retail.controller.ReserveOrderController.createReserveOrder()','POST',1,'store1','门店','/retail/reserve/create','127.0.0.1','内网IP','{\"items\":[{\"productId\":1,\"productName\":\"COACH 背提包-2525001\",\"productNumber\":\"2736QBAF4\",\"salePrice\":4980,\"quantity\":1}],\"payType\":1,\"depositType\":0,\"depositAmount\":2000,\"remark\":\"\",\"storeId\":1,\"totalSalePrice\":4980,\"status\":0,\"createBy\":\"store1\"}','{\"msg\":\"\\n### Error updating database.  Cause: java.sql.SQLException: Field \'batch_number\' doesn\'t have a default value\\n### The error may exist in file [/Users/<USER>/Documents/work/RuoYi-Vue-fast/target/classes/mybatis/retail/InventoryOutDetailMapper.xml]\\n### The error may involve com.retail.project.retail.mapper.InventoryOutDetailMapper.insertInventoryOutDetail-Inline\\n### The error occurred while setting parameters\\n### SQL: insert into inventory_out_detail          ( order_id,             product_id,                                       quantity,             sale_price,                          product_name,             create_by,                          create_time )           values ( ?,             ?,                                       ?,             ?,                          ?,             ?,                          sysdate() )\\n### Cause: java.sql.SQLException: Field \'batch_number\' doesn\'t have a default value\\n; Field \'batch_number\' doesn\'t have a default value; nested exception is java.sql.SQLException: Field \'batch_number\' doesn\'t have a default value\",\"code\":500}',0,NULL,'2025-04-28 15:15:33',117),(305,'商品预订',1,'com.retail.project.retail.controller.ReserveOrderController.reserve()','POST',1,'store1','门店','/retail/reserve/reserve','127.0.0.1','内网IP','{\"depositAmount\":2000,\"depositType\":0,\"payType\":1,\"quantity\":1,\"remark\":\"\",\"storeId\":1}','{\"msg\":\"商品ID不能为空\",\"code\":500}',0,NULL,'2025-04-28 15:36:41',16),(306,'商品预订',1,'com.retail.project.retail.controller.ReserveOrderController.reserve()','POST',1,'store1','门店','/retail/reserve/reserve','127.0.0.1','内网IP','{\"depositAmount\":2000,\"depositType\":0,\"payType\":1,\"quantity\":1,\"remark\":\"\",\"storeId\":1}','{\"msg\":\"商品ID不能为空\",\"code\":500}',0,NULL,'2025-04-28 15:36:45',1),(307,'商品预订',1,'com.retail.project.retail.controller.ReserveOrderController.reserve()','POST',1,'store1','门店','/retail/reserve/reserve','127.0.0.1','内网IP','{\"depositAmount\":2000,\"depositType\":0,\"payType\":1,\"quantity\":1,\"remark\":\"\",\"storeId\":1}','{\"msg\":\"商品ID不能为空\",\"code\":500}',0,NULL,'2025-04-28 15:36:51',1),(308,'商品预订',1,'com.retail.project.retail.controller.ReserveOrderController.reserve()','POST',1,'store1','门店','/retail/reserve/reserve','127.0.0.1','内网IP','{\"depositAmount\":2000,\"depositType\":0,\"payType\":1,\"quantity\":1,\"remark\":\"\",\"storeId\":1}','{\"msg\":\"商品ID不能为空\",\"code\":500}',0,NULL,'2025-04-28 16:04:15',7),(309,'商品预订',1,'com.retail.project.retail.controller.ReserveOrderController.reserve()','POST',1,'store1','门店','/retail/reserve/reserve','127.0.0.1','内网IP','{\"depositAmount\":2000,\"depositType\":0,\"memberId\":2504277155,\"payType\":1,\"quantity\":1,\"remark\":\"\",\"storeId\":1}','{\"msg\":\"商品ID不能为空\",\"code\":500}',0,NULL,'2025-04-28 16:04:25',0),(310,'商品预订',1,'com.retail.project.retail.controller.ReserveOrderController.reserve()','POST',1,'store1','门店','/retail/reserve/reserve','127.0.0.1','内网IP','{\"depositAmount\":2000,\"depositType\":0,\"payType\":1,\"quantity\":1,\"remark\":\"\",\"storeId\":1}','{\"msg\":\"商品ID不能为空\",\"code\":500}',0,NULL,'2025-04-28 16:05:49',1),(311,'商品预订',1,'com.retail.project.retail.controller.ReserveOrderController.reserve()','POST',1,'store1','门店','/retail/reserve/reserve','127.0.0.1','内网IP','{\"depositAmount\":1494,\"depositType\":0,\"payType\":1,\"productId\":1,\"quantity\":1,\"remark\":\"\",\"salePrice\":4980,\"storeId\":1}','{\"msg\":\"会员ID不能为空\",\"code\":500}',0,NULL,'2025-04-28 16:18:58',2),(312,'商品预订',1,'com.retail.project.retail.controller.ReserveOrderController.reserve()','POST',1,'store1','门店','/retail/reserve/reserve','127.0.0.1','内网IP','{\"depositAmount\":2000,\"depositType\":0,\"payType\":1,\"productId\":1,\"quantity\":1,\"remark\":\"\",\"salePrice\":4980,\"storeId\":1}','{\"msg\":\"会员ID不能为空\",\"code\":500}',0,NULL,'2025-04-28 16:19:06',0),(313,'商品预订',1,'com.retail.project.retail.controller.ReserveOrderController.reserve()','POST',1,'store1','门店','/retail/reserve/reserve','127.0.0.1','内网IP','{\"depositAmount\":2000,\"depositType\":0,\"memberId\":2504277155,\"payType\":1,\"productId\":1,\"quantity\":1,\"remark\":\"预订11\",\"salePrice\":4980,\"storeId\":1}','{\"msg\":\"商品预订失败：\\n### Error updating database.  Cause: java.sql.SQLException: Field \'batch_number\' doesn\'t have a default value\\n### The error may exist in file [/Users/<USER>/Documents/work/RuoYi-Vue-fast/target/classes/mybatis/retail/InventoryOutDetailMapper.xml]\\n### The error may involve com.retail.project.retail.mapper.InventoryOutDetailMapper.insertInventoryOutDetail-Inline\\n### The error occurred while setting parameters\\n### SQL: insert into inventory_out_detail          ( order_id,             product_id,                                       quantity,             sale_price,                                       create_by,                          create_time )           values ( ?,             ?,                                       ?,             ?,                                       ?,                          sysdate() )\\n### Cause: java.sql.SQLException: Field \'batch_number\' doesn\'t have a default value\\n; Field \'batch_number\' doesn\'t have a default value; nested exception is java.sql.SQLException: Field \'batch_number\' doesn\'t have a default value\",\"code\":500}',0,NULL,'2025-04-28 16:19:20',158),(314,'商品预订',1,'com.retail.project.retail.controller.ReserveOrderController.reserve()','POST',1,'store1','门店','/retail/reserve/reserve','127.0.0.1','内网IP','{\"batchNumber\":\"R1745830326701\",\"depositAmount\":2000,\"depositType\":0,\"memberId\":2504277155,\"payType\":1,\"productId\":1,\"quantity\":1,\"remark\":\"预订11\",\"salePrice\":4980,\"storeId\":1}','{\"msg\":\"商品预订成功\",\"code\":200}',0,NULL,'2025-04-28 16:52:06',74),(315,'审核出库单',2,'com.retail.project.retail.controller.InventoryOutOrderController.approve()','POST',1,'finance','总部','/retail/outorder/approve','127.0.0.1','内网IP','{\"id\":15,\"remark\":\"11\"}','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,'2025-04-28 16:53:45',75),(316,'批量商品预订',1,'com.retail.project.retail.controller.ReserveOrderController.batchReserve()','POST',1,'store1','门店','/retail/reserve/batch-reserve','127.0.0.1','内网IP','{\"depositAmount\":9960,\"depositType\":1,\"items\":[{\"batchNumber\":\"R1745830551912-0\",\"productId\":1,\"productName\":\"COACH 背提包-2525001\",\"productNumber\":\"2736QBAF4\",\"quantity\":2,\"salePrice\":4980}],\"memberId\":2504277195,\"payType\":2,\"remark\":\"全款预订11\",\"storeId\":1,\"totalSalePrice\":9960}','{\"msg\":\"批量预订成功\",\"code\":200}',0,NULL,'2025-04-28 16:55:52',30),(317,'审核出库单',2,'com.retail.project.retail.controller.InventoryOutOrderController.approve()','POST',1,'finance','总部','/retail/outorder/approve','127.0.0.1','内网IP','{\"id\":16,\"remark\":\"22\"}','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,'2025-04-28 16:56:21',56),(318,'入库单',1,'com.retail.project.retail.controller.InventoryInOrderController.saveWithDetails()','POST',1,'finance','总部','/retail/inorder/save','127.0.0.1','内网IP','{\"channelId\":1,\"createBy\":\"finance\",\"createTime\":\"2025-04-29 12:20:32\",\"detailList\":[{\"batchNumber\":\"c02dda4a18\",\"counterId\":1,\"createBy\":\"finance\",\"id\":11,\"orderId\":5,\"params\":{},\"productId\":1,\"productImage\":\"/profile/upload/2025/04/25/bag1_20250425180819A001.jpg\",\"productName\":\"COACH 背提包-2525001\",\"purchasePrice\":2800,\"quantity\":10,\"storeId\":1},{\"batchNumber\":\"c02dda4a18\",\"counterId\":2,\"createBy\":\"finance\",\"id\":12,\"orderId\":5,\"params\":{},\"productId\":2,\"productImage\":\"/profile/upload/2025/04/27/bag2_20250427092057A001.jpg\",\"productName\":\"COACH 背提包-2524996\",\"purchasePrice\":2900,\"quantity\":5,\"storeId\":2}],\"id\":5,\"isFutures\":false,\"orderNumber\":\"IN9331212428\",\"params\":{}}','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,'2025-04-29 12:20:32',75),(319,'入库单',2,'com.retail.project.retail.controller.InventoryInOrderController.edit()','PUT',1,'finance','总部','/retail/inorder','127.0.0.1','内网IP','{\"channelId\":1,\"channelName\":\"海外采购\",\"createBy\":\"finance\",\"createTime\":\"2025-04-29 12:20:32\",\"detailList\":[{\"batchNumber\":\"c02dda4a18\",\"counterId\":1,\"counterName\":\"AA\",\"createBy\":\"finance\",\"createTime\":\"2025-04-29 12:20:32\",\"id\":11,\"orderId\":5,\"params\":{},\"productId\":1,\"productImage\":\"/profile/upload/2025/04/25/bag1_20250425180819A001.jpg\",\"productName\":\"COACH 背提包-2525001\",\"productNumber\":\"2736QBAF4\",\"purchasePrice\":2800,\"quantity\":10,\"storeId\":1,\"storeName\":\"北京门店\",\"updateBy\":\"finance\"},{\"batchNumber\":\"c02dda4a18\",\"counterId\":2,\"counterName\":\"BB\",\"createBy\":\"finance\",\"createTime\":\"2025-04-29 12:20:32\",\"id\":12,\"orderId\":5,\"params\":{},\"productId\":2,\"productImage\":\"/profile/upload/2025/04/27/bag2_20250427092057A001.jpg\",\"productName\":\"COACH 背提包-2524996\",\"productNumber\":\"78277IMCBI\",\"purchasePrice\":2900,\"quantity\":5,\"storeId\":2,\"storeName\":\"成都门店\",\"updateBy\":\"finance\"},{\"batchNumber\":\"75baad55fd\",\"counterId\":2,\"createBy\":\"finance\",\"id\":13,\"orderId\":5,\"params\":{},\"productId\":1,\"productImage\":\"/profile/upload/2025/04/25/bag1_20250425180819A001.jpg\",\"productName\":\"COACH 背提包-2525001\",\"purchasePrice\":2700,\"quantity\":5,\"storeId\":2,\"updateBy\":\"finance\"},{\"batchNumber\":\"40d6bea597\",\"counterId\":1,\"createBy\":\"finance\",\"id\":14,\"orderId\":5,\"params\":{},\"productId\":2,\"productImage\":\"/profile/upload/2025/04/27/bag2_20250427092057A001.jpg\",\"productName\":\"COACH 背提包-2524996\",\"purchasePrice\":2800,\"quantity\":10,\"storeId\":1,\"updateBy\":\"finance\"}],\"id\":5,\"isFutures\":false,\"params\":{},\"updateBy\":\"finance\",\"updateTime\":\"2025-04-29 12:21:22\"}','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,'2025-04-29 12:21:22',80),(320,'入库单',1,'com.retail.project.retail.controller.InventoryInOrderController.saveWithDetails()','POST',1,'finance','总部','/retail/inorder/save','127.0.0.1','内网IP','{\"channelId\":2,\"createBy\":\"finance\",\"createTime\":\"2025-04-29 12:22:59\",\"detailList\":[{\"batchNumber\":\"57f8b210a3\",\"counterId\":1,\"createBy\":\"finance\",\"id\":15,\"orderId\":6,\"params\":{},\"productId\":1,\"productImage\":\"/profile/upload/2025/04/25/bag1_20250425180819A001.jpg\",\"productName\":\"COACH 背提包-2525001\",\"purchasePrice\":2600,\"quantity\":5,\"storeId\":1},{\"batchNumber\":\"57f8b210a3\",\"counterId\":1,\"createBy\":\"finance\",\"id\":16,\"orderId\":6,\"params\":{},\"productId\":2,\"productImage\":\"/profile/upload/2025/04/27/bag2_20250427092057A001.jpg\",\"productName\":\"COACH 背提包-2524996\",\"purchasePrice\":2600,\"quantity\":5,\"storeId\":1}],\"futuresLogistics\":\"1122334455\",\"id\":6,\"isFutures\":true,\"orderNumber\":\"IN1600505038\",\"params\":{}}','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,'2025-04-29 12:22:59',74),(321,'商品售出',1,'com.retail.project.retail.controller.OutOrderController.sale()','POST',1,'store1','门店','/retail/outorder/sale','127.0.0.1','内网IP','{\"memberId\":2504277155,\"payType\":1,\"quantity\":1,\"remark\":\"11\",\"salePrice\":4980,\"stockId\":11}','{\"msg\":\"商品售出成功\",\"code\":200}',0,NULL,'2025-04-29 12:24:44',60),(322,'商品售出',1,'com.retail.project.retail.controller.OutOrderController.sale()','POST',1,'store1','门店','/retail/outorder/sale','127.0.0.1','内网IP','{\"memberId\":2504277155,\"payType\":1,\"quantity\":1,\"remark\":\"11\",\"salePrice\":4880,\"stockId\":11}','{\"msg\":\"商品售出成功\",\"code\":200}',0,NULL,'2025-04-29 13:43:09',79),(323,'驳回出库单',2,'com.retail.project.retail.controller.InventoryOutOrderController.reject()','POST',1,'finance','总部','/retail/outorder/reject','127.0.0.1','内网IP','{\"id\":18,\"rejectReason\":\"错误\"}','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,'2025-04-29 14:03:49',80),(324,'商品售出',1,'com.retail.project.retail.controller.OutOrderController.sale()','POST',1,'store1','门店','/retail/outorder/sale','127.0.0.1','内网IP','{\"payType\":2,\"quantity\":1,\"remark\":\"22\",\"salePrice\":4980,\"stockId\":11}','{\"msg\":\"会员ID不能为空\",\"code\":500}',0,NULL,'2025-04-29 14:04:55',9),(325,'商品售出',1,'com.retail.project.retail.controller.OutOrderController.sale()','POST',1,'store1','门店','/retail/outorder/sale','127.0.0.1','内网IP','{\"memberId\":2504277155,\"payType\":2,\"quantity\":1,\"remark\":\"22\",\"salePrice\":4980,\"stockId\":11}','{\"msg\":\"商品售出成功\",\"code\":200}',0,NULL,'2025-04-29 14:05:04',74),(326,'审核出库单',2,'com.retail.project.retail.controller.InventoryOutOrderController.approve()','POST',1,'finance','总部','/retail/outorder/approve','127.0.0.1','内网IP','{\"id\":19,\"remark\":\"22\"}','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,'2025-04-29 14:13:20',58),(327,'批量商品售出',1,'com.retail.project.retail.controller.OutOrderController.batchSale()','POST',1,'store1','门店','/retail/outorder/batch-sale','127.0.0.1','内网IP','{\"memberId\":2504277195,\"payType\":3,\"remark\":\"\",\"stocks\":[{\"productId\":1,\"quantity\":1,\"salePrice\":4980,\"stockId\":11},{\"productId\":2,\"quantity\":1,\"salePrice\":3300,\"stockId\":14}],\"totalSalePrice\":8000}','{\"msg\":\"批量售出成功\",\"code\":200}',0,NULL,'2025-04-29 14:22:14',55),(328,'撤销出库单',2,'com.retail.project.retail.controller.InventoryOutOrderController.cancel()','POST',1,'store1','门店','/retail/outorder/cancel','127.0.0.1','内网IP','{\"id\":20,\"cancelReason\":\"33\"}','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,'2025-04-29 14:23:02',37),(329,'申请退货',2,'com.retail.project.retail.controller.InventoryOutOrderController.applyReturn()','POST',1,'store1','门店','/retail/outorder/apply-return','127.0.0.1','内网IP','{\"id\":19,\"returnReason\":\"退货22\"}','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,'2025-04-29 14:52:59',68),(330,'撤销退货申请',2,'com.retail.project.retail.controller.InventoryOutOrderController.cancelReturn()','POST',1,'store1','门店','/retail/outorder/cancel-return','127.0.0.1','内网IP','{\"id\":19,\"cancelReason\":\"111\"}','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,'2025-04-29 14:53:25',36),(331,'申请退货',2,'com.retail.project.retail.controller.InventoryOutOrderController.applyReturn()','POST',1,'store1','门店','/retail/outorder/apply-return','127.0.0.1','内网IP','{\"id\":19,\"returnReason\":\"退货\"}','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,'2025-04-29 14:54:21',37),(332,'审核退货申请',2,'com.retail.project.retail.controller.InventoryOutOrderController.approveReturn()','POST',1,'finance','总部','/retail/outorder/approve-return','127.0.0.1','内网IP','{\"id\":19,\"remark\":\"22\"}','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,'2025-04-29 15:01:19',78),(333,'商品售出',1,'com.retail.project.retail.controller.OutOrderController.sale()','POST',1,'store1','门店','/retail/outorder/sale','127.0.0.1','内网IP','{\"memberId\":2504277155,\"payType\":2,\"quantity\":1,\"remark\":\"\",\"salePrice\":4680,\"stockId\":11}','{\"msg\":\"商品售出成功\",\"code\":200}',0,NULL,'2025-04-29 15:40:39',76),(334,'审核出库单',2,'com.retail.project.retail.controller.InventoryOutOrderController.approve()','POST',1,'finance','总部','/retail/outorder/approve','127.0.0.1','内网IP','{\"id\":21,\"remark\":\"55\"}','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,'2025-04-29 15:41:10',83),(335,'申请退货',2,'com.retail.project.retail.controller.InventoryOutOrderController.applyReturn()','POST',1,'store1','门店','/retail/outorder/apply-return','127.0.0.1','内网IP','{\"id\":21,\"returnReason\":\"55\"}','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,'2025-04-29 15:41:22',59),(336,'撤销退货申请',2,'com.retail.project.retail.controller.InventoryOutOrderController.cancelReturn()','POST',1,'store1','门店','/retail/outorder/cancel-return','127.0.0.1','内网IP','{\"id\":21,\"cancelReason\":\"55\"}','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,'2025-04-29 15:41:57',49),(337,'申请退货',2,'com.retail.project.retail.controller.InventoryOutOrderController.applyReturn()','POST',1,'store1','门店','/retail/outorder/apply-return','127.0.0.1','内网IP','{\"id\":21,\"returnReason\":\"55\"}','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,'2025-04-29 15:44:44',88),(338,'撤销退货申请',2,'com.retail.project.retail.controller.InventoryOutOrderController.cancelReturn()','POST',1,'store1','门店','/retail/outorder/cancel-return','127.0.0.1','内网IP','{\"id\":21,\"cancelReason\":\"55\"}','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,'2025-04-29 15:58:48',75),(339,'申请退货',2,'com.retail.project.retail.controller.InventoryOutOrderController.applyReturn()','POST',1,'store1','门店','/retail/outorder/apply-return','127.0.0.1','内网IP','{\"id\":21,\"returnReason\":\"55\"}','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,'2025-04-29 16:00:14',55),(340,'驳回退货申请',2,'com.retail.project.retail.controller.InventoryOutOrderController.rejectReturn()','POST',1,'finance','总部','/retail/outorder/reject-return','127.0.0.1','内网IP','{\"id\":21,\"rejectReason\":\"55\"}','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,'2025-04-29 16:06:29',68),(341,'批量商品售出',1,'com.retail.project.retail.controller.OutOrderController.batchSale()','POST',1,'store1','门店','/retail/outorder/batch-sale','127.0.0.1','内网IP','{\"memberId\":2504277155,\"payType\":1,\"remark\":\"66\",\"stocks\":[{\"productId\":1,\"quantity\":8,\"salePrice\":4980,\"stockId\":11}],\"totalSalePrice\":30999}','{\"msg\":\"批量售出成功\",\"code\":200}',0,NULL,'2025-04-29 16:07:57',195),(342,'审核出库单',2,'com.retail.project.retail.controller.InventoryOutOrderController.approve()','POST',1,'finance','总部','/retail/outorder/approve','127.0.0.1','内网IP','{\"id\":22,\"remark\":\"66\"}','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,'2025-04-29 16:08:47',77),(343,'商品预订',1,'com.retail.project.retail.controller.ReserveOrderController.reserve()','POST',1,'store1','门店','/retail/reserve/reserve','127.0.0.1','内网IP','{\"batchNumber\":\"R1745915863285\",\"depositAmount\":2999,\"depositType\":0,\"memberId\":2504277155,\"payType\":1,\"productId\":1,\"quantity\":1,\"remark\":\"\",\"salePrice\":4780,\"storeId\":1}','{\"msg\":\"商品预订成功\",\"code\":200}',0,NULL,'2025-04-29 16:37:43',72),(344,'撤销出库单',2,'com.retail.project.retail.controller.InventoryOutOrderController.cancel()','POST',1,'store1','门店','/retail/outorder/cancel','127.0.0.1','内网IP','{\"id\":23,\"cancelReason\":\"77\"}','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,'2025-04-29 16:38:55',45),(345,'商品预订',1,'com.retail.project.retail.controller.ReserveOrderController.reserve()','POST',1,'store1','门店','/retail/reserve/reserve','127.0.0.1','内网IP','{\"batchNumber\":\"R1745915986520\",\"depositAmount\":2000,\"depositType\":0,\"memberId\":2504277155,\"payType\":1,\"productId\":1,\"quantity\":1,\"remark\":\"88\",\"salePrice\":4880,\"storeId\":1}','{\"msg\":\"商品预订成功\",\"code\":200}',0,NULL,'2025-04-29 16:39:46',40),(346,'审核出库单',2,'com.retail.project.retail.controller.InventoryOutOrderController.approve()','POST',1,'finance','总部','/retail/outorder/approve','127.0.0.1','内网IP','{\"id\":24,\"remark\":\"88\"}','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,'2025-04-29 16:40:05',66),(347,'商品预订',1,'com.retail.project.retail.controller.ReserveOrderController.reserve()','POST',1,'store1','门店','/retail/reserve/reserve','127.0.0.1','内网IP','{\"batchNumber\":\"R1745919150216\",\"depositAmount\":2000,\"depositType\":0,\"memberId\":2504277155,\"payType\":1,\"productId\":1,\"quantity\":1,\"remark\":\"\",\"salePrice\":4980,\"storeId\":1}','{\"msg\":\"商品预订成功\",\"code\":200}',0,NULL,'2025-04-29 17:32:30',59),(348,'审核出库单',2,'com.retail.project.retail.controller.InventoryOutOrderController.approve()','POST',1,'finance','总部','/retail/outorder/approve','127.0.0.1','内网IP','{\"id\":25,\"remark\":\"99\"}','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,'2025-04-29 17:32:50',52),(349,'商品预订',1,'com.retail.project.retail.controller.ReserveOrderController.reserve()','POST',1,'store1','门店','/retail/reserve/reserve','127.0.0.1','内网IP','{\"batchNumber\":\"R1745978157737\",\"depositAmount\":4980,\"depositType\":1,\"memberId\":2504277155,\"payType\":1,\"productId\":1,\"quantity\":1,\"remark\":\"00\",\"salePrice\":4980,\"storeId\":1}','{\"msg\":\"商品预订成功\",\"code\":200}',0,NULL,'2025-04-30 09:55:57',97),(350,'审核出库单',2,'com.retail.project.retail.controller.InventoryOutOrderController.approve()','POST',1,'finance','总部','/retail/outorder/approve','127.0.0.1','内网IP','{\"id\":26,\"remark\":\"00\"}','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,'2025-04-30 09:57:23',75),(351,'菜单管理',1,'com.retail.project.system.controller.SysMenuController.add()','POST',1,'admin','总部','/system/menu','127.0.0.1','内网IP','{\"children\":[],\"createBy\":\"admin\",\"isCache\":\"0\",\"isFrame\":\"1\",\"menuName\":\"取消预订\",\"menuType\":\"F\",\"orderNum\":5,\"params\":{},\"parentId\":2009,\"perms\":\"retail:outorder:reservecancel\",\"status\":\"0\",\"visible\":\"0\"}','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,'2025-04-30 10:00:42',28),(352,'菜单管理',2,'com.retail.project.system.controller.SysMenuController.edit()','PUT',1,'admin','总部','/system/menu','127.0.0.1','内网IP','{\"children\":[],\"createTime\":\"2025-04-28 13:44:37\",\"icon\":\"#\",\"isCache\":\"0\",\"isFrame\":\"1\",\"menuId\":2065,\"menuName\":\"预订交货\",\"menuType\":\"F\",\"orderNum\":4,\"params\":{},\"parentId\":2009,\"path\":\"\",\"perms\":\"retail:outorder:deliver\",\"routeName\":\"\",\"status\":\"0\",\"updateBy\":\"admin\",\"visible\":\"0\"}','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,'2025-04-30 10:22:52',43),(353,'角色管理',2,'com.retail.project.system.controller.SysRoleController.edit()','PUT',1,'admin','总部','/system/role','127.0.0.1','内网IP','{\"admin\":false,\"createTime\":\"2025-04-25 08:57:46\",\"dataScope\":\"1\",\"delFlag\":\"0\",\"deptCheckStrictly\":true,\"flag\":false,\"menuCheckStrictly\":true,\"menuIds\":[1,100,1000,1001,1002,1003,1004,1005,1006,101,1007,1008,1009,1010,1011,102,1012,1013,1014,1015,103,1016,1017,1018,1019,104,1020,1021,1022,1023,1024,105,1025,1026,1027,1028,1029,106,1030,1031,1032,1033,1034,107,1035,1036,1037,1038,108,500,1039,1040,1041,501,1042,1043,1044,1045,2000,2001,2017,2014,2015,2016,2002,2018,2019,2020,2021,2003,2028,2022,2023,2024,2004,2029,2025,2026,2027,2036,2005,2030,2031,2032,2033,2037,2006,2034,2038,2007,2035,2039,2040,2064,2008,2041,2042,2043,2044,2045,2009,2046,2047,2048,2065,2066,2010,2050,2049,2051,2052,2053,2054,2057,2056,2058,2059,2055,2060,2061,2062,2063,2011,2012,2013],\"params\":{},\"roleId\":100,\"roleKey\":\"manager\",\"roleName\":\"管理员\",\"roleSort\":1,\"status\":\"0\",\"updateBy\":\"admin\"}','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,'2025-04-30 10:52:11',47),(354,'角色管理',2,'com.retail.project.system.controller.SysRoleController.edit()','PUT',1,'admin','总部','/system/role','127.0.0.1','内网IP','{\"admin\":false,\"createTime\":\"2025-04-25 08:58:13\",\"dataScope\":\"1\",\"delFlag\":\"0\",\"deptCheckStrictly\":true,\"flag\":false,\"menuCheckStrictly\":true,\"menuIds\":[2000,2001,2017,2014,2015,2016,2002,2018,2019,2020,2021,2003,2028,2022,2023,2024,2004,2029,2025,2026,2027,2036,2005,2030,2031,2032,2033,2037,2006,2034,2038,2007,2035,2039,2040,2064,2008,2041,2042,2043,2044,2045,2009,2046,2047,2048,2065,2066,2053,2054,2057,2056,2058,2059,2055,2060,2061,2062,2063],\"params\":{},\"roleId\":101,\"roleKey\":\"finance\",\"roleName\":\"财务\",\"roleSort\":3,\"status\":\"0\",\"updateBy\":\"admin\"}','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,'2025-04-30 10:52:24',40),(355,'角色管理',2,'com.retail.project.system.controller.SysRoleController.edit()','PUT',1,'admin','总部','/system/role','127.0.0.1','内网IP','{\"admin\":false,\"createTime\":\"2025-04-25 08:58:51\",\"dataScope\":\"1\",\"delFlag\":\"0\",\"deptCheckStrictly\":true,\"flag\":false,\"menuCheckStrictly\":true,\"menuIds\":[2000,2008,2053,2054,2055,2007,2035,2039,2040,2064,2041,2042,2045,2009,2046,2047,2048,2065,2066,2057,2056,2058,2060],\"params\":{},\"roleId\":102,\"roleKey\":\"store\",\"roleName\":\"门店\",\"roleSort\":4,\"status\":\"0\",\"updateBy\":\"admin\"}','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,'2025-04-30 10:52:45',45),(356,'商品预订',1,'com.retail.project.retail.controller.ReserveOrderController.reserve()','POST',1,'store1','门店','/retail/reserve/reserve','127.0.0.1','内网IP','{\"batchNumber\":\"R1745992899225\",\"depositAmount\":2000,\"depositType\":0,\"payType\":1,\"productId\":1,\"quantity\":1,\"remark\":\"\",\"salePrice\":4980,\"storeId\":1}','{\"msg\":\"会员ID不能为空\",\"code\":500}',0,NULL,'2025-04-30 14:01:39',15),(357,'商品预订',1,'com.retail.project.retail.controller.ReserveOrderController.reserve()','POST',1,'store1','门店','/retail/reserve/reserve','127.0.0.1','内网IP','{\"batchNumber\":\"R1745992935213\",\"depositAmount\":2000,\"depositType\":0,\"memberId\":2504277155,\"payType\":1,\"productId\":1,\"quantity\":1,\"remark\":\"\",\"salePrice\":4980,\"storeId\":1}','{\"msg\":\"商品预订成功\",\"code\":200}',0,NULL,'2025-04-30 14:02:15',78),(358,'撤销出库单',2,'com.retail.project.retail.controller.InventoryOutOrderController.cancel()','POST',1,'store1','门店','/retail/outorder/cancel','127.0.0.1','内网IP','{\"id\":27,\"cancelReason\":\"11\"}','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,'2025-04-30 14:02:46',54),(359,'商品预订',1,'com.retail.project.retail.controller.ReserveOrderController.reserve()','POST',1,'store1','门店','/retail/reserve/reserve','127.0.0.1','内网IP','{\"batchNumber\":\"R1745993004744\",\"depositAmount\":2000,\"depositType\":0,\"memberId\":2504277155,\"payType\":1,\"productId\":1,\"quantity\":1,\"remark\":\"11\",\"salePrice\":4980,\"storeId\":1}','{\"msg\":\"商品预订成功\",\"code\":200}',0,NULL,'2025-04-30 14:03:24',25),(360,'驳回出库单',2,'com.retail.project.retail.controller.InventoryOutOrderController.reject()','POST',1,'finance','总部','/retail/outorder/reject','127.0.0.1','内网IP','{\"id\":28,\"rejectReason\":\"11\"}','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,'2025-04-30 14:06:40',53),(361,'商品预订',1,'com.retail.project.retail.controller.ReserveOrderController.reserve()','POST',1,'store1','门店','/retail/reserve/reserve','127.0.0.1','内网IP','{\"batchNumber\":\"R1745993234304\",\"depositAmount\":2000,\"depositType\":0,\"memberId\":2504277155,\"payType\":1,\"productId\":1,\"quantity\":1,\"remark\":\"22\",\"salePrice\":4980,\"storeId\":1}','{\"msg\":\"商品预订成功\",\"code\":200}',0,NULL,'2025-04-30 14:07:14',32),(362,'审核出库单',2,'com.retail.project.retail.controller.InventoryOutOrderController.approve()','POST',1,'finance','总部','/retail/outorder/approve','127.0.0.1','内网IP','{\"id\":29,\"remark\":\"22\"}','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,'2025-04-30 14:07:33',78),(363,'取消预订单',2,'com.retail.project.retail.controller.ReserveOrderController.cancelReserveOrder()','POST',1,'store1','门店','/retail/reserve/cancel','127.0.0.1','内网IP','{\"id\":29,\"cancelReason\":\"22\"}','{\"msg\":\"只有待审核的交货业务才能取消\",\"code\":500}',0,NULL,'2025-04-30 14:07:57',46),(364,'取消预订单',2,'com.retail.project.retail.controller.ReserveOrderController.cancelReserveOrder()','POST',1,'store1','门店','/retail/reserve/cancel','127.0.0.1','内网IP','{\"id\":29,\"cancelReason\":\"22\"}','{\"msg\":\"只有待审核的交货业务才能取消\",\"code\":500}',0,NULL,'2025-04-30 14:08:01',23),(365,'取消预订单',2,'com.retail.project.retail.controller.ReserveOrderController.cancelReserveOrder()','POST',1,'store1','门店','/retail/reserve/cancel','127.0.0.1','内网IP','{\"id\":29,\"cancelReason\":\"22\"}','{\"msg\":\"只有待审核的交货业务才能取消\",\"code\":500}',0,NULL,'2025-04-30 14:08:06',32),(366,'取消预订单',2,'com.retail.project.retail.controller.ReserveOrderController.cancelReserveOrder()','POST',1,'store1','门店','/retail/reserve/cancel','127.0.0.1','内网IP','{\"id\":29,\"cancelReason\":\"22\"}','{\"msg\":\"只有待审核的交货业务才能取消\",\"code\":500}',0,NULL,'2025-04-30 14:09:22',41),(367,'取消预订单',2,'com.retail.project.retail.controller.ReserveOrderController.cancelReserveOrder()','POST',1,'store1','门店','/retail/reserve/cancel','127.0.0.1','内网IP','{\"id\":29,\"cancelReason\":\"22\"}','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,'2025-04-30 14:21:48',100),(368,'商品预订',1,'com.retail.project.retail.controller.ReserveOrderController.reserve()','POST',1,'store1','门店','/retail/reserve/reserve','127.0.0.1','内网IP','{\"batchNumber\":\"R1745996965050\",\"depositAmount\":2000,\"depositType\":0,\"memberId\":2504277155,\"payType\":1,\"productId\":1,\"quantity\":1,\"remark\":\"33\",\"salePrice\":4980,\"storeId\":1}','{\"msg\":\"商品预订成功\",\"code\":200}',0,NULL,'2025-04-30 15:09:25',63),(369,'审核出库单',2,'com.retail.project.retail.controller.InventoryOutOrderController.approve()','POST',1,'finance','总部','/retail/outorder/approve','127.0.0.1','内网IP','{\"id\":30,\"remark\":\"33\"}','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,'2025-04-30 15:10:28',68),(370,'取消预订单',2,'com.retail.project.retail.controller.ReserveOrderController.cancelReserveOrder()','POST',1,'store1','门店','/retail/reserve/cancel','127.0.0.1','内网IP','{\"id\":30,\"cancelReason\":\"error\"}','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,'2025-04-30 15:18:42',94),(371,'驳回出库单',2,'com.retail.project.retail.controller.InventoryOutOrderController.reject()','POST',1,'finance','总部','/retail/outorder/reject','127.0.0.1','内网IP','{\"id\":30,\"rejectReason\":\"no\"}','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,'2025-04-30 15:19:00',72),(372,'商品预订',1,'com.retail.project.retail.controller.ReserveOrderController.reserve()','POST',1,'store1','门店','/retail/reserve/reserve','127.0.0.1','内网IP','{\"batchNumber\":\"R1745997629832\",\"depositAmount\":1980,\"depositType\":0,\"memberId\":2504277155,\"payType\":1,\"productId\":1,\"quantity\":1,\"remark\":\"44\",\"salePrice\":4980,\"storeId\":1}','{\"msg\":\"商品预订成功\",\"code\":200}',0,NULL,'2025-04-30 15:20:29',45),(373,'审核出库单',2,'com.retail.project.retail.controller.InventoryOutOrderController.approve()','POST',1,'finance','总部','/retail/outorder/approve','127.0.0.1','内网IP','{\"id\":31,\"remark\":\"44\"}','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,'2025-04-30 15:20:46',75),(374,'取消预订单',2,'com.retail.project.retail.controller.ReserveOrderController.cancelReserveOrder()','POST',1,'store1','门店','/retail/reserve/cancel','127.0.0.1','内网IP','{\"id\":31,\"cancelReason\":\"44\"}','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,'2025-04-30 15:21:08',48),(375,'审核出库单',2,'com.retail.project.retail.controller.InventoryOutOrderController.approve()','POST',1,'finance','总部','/retail/outorder/approve','127.0.0.1','内网IP','{\"id\":31,\"remark\":\"44\"}','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,'2025-04-30 15:21:22',59),(376,'商品预订',1,'com.retail.project.retail.controller.ReserveOrderController.reserve()','POST',1,'store1','门店','/retail/reserve/reserve','127.0.0.1','内网IP','{\"batchNumber\":\"R1745998299431\",\"depositAmount\":1900,\"depositType\":0,\"memberId\":2504277155,\"payType\":1,\"productId\":1,\"quantity\":1,\"remark\":\"55\",\"salePrice\":4980,\"storeId\":1}','{\"msg\":\"商品预订成功\",\"code\":200}',0,NULL,'2025-04-30 15:31:39',32),(377,'审核出库单',2,'com.retail.project.retail.controller.InventoryOutOrderController.approve()','POST',1,'finance','总部','/retail/outorder/approve','127.0.0.1','内网IP','{\"id\":32,\"remark\":\"55\"}','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,'2025-04-30 15:33:41',62),(378,'取消预订单',2,'com.retail.project.retail.controller.ReserveOrderController.cancelReserveOrder()','POST',1,'finance','总部','/retail/reserve/cancel','127.0.0.1','内网IP','{\"id\":32,\"cancelReason\":\"55\"}','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,'2025-04-30 15:34:12',69),(379,'审核出库单',2,'com.retail.project.retail.controller.InventoryOutOrderController.approve()','POST',1,'finance','总部','/retail/outorder/approve','127.0.0.1','内网IP','{\"id\":32,\"remark\":\"55\"}','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,'2025-04-30 15:34:43',58),(380,'商品预订',1,'com.retail.project.retail.controller.ReserveOrderController.reserve()','POST',1,'store1','门店','/retail/reserve/reserve','127.0.0.1','内网IP','{\"batchNumber\":\"R1745999755691\",\"depositAmount\":2100,\"depositType\":0,\"memberId\":2504277155,\"payType\":1,\"productId\":1,\"quantity\":1,\"remark\":\"\",\"salePrice\":4980,\"storeId\":1}','{\"msg\":\"商品预订成功\",\"code\":200}',0,NULL,'2025-04-30 15:55:55',68),(381,'审核出库单',2,'com.retail.project.retail.controller.InventoryOutOrderController.approve()','POST',1,'finance','总部','/retail/outorder/approve','127.0.0.1','内网IP','{\"id\":33,\"remark\":\"66\"}','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,'2025-04-30 15:56:12',63),(382,'取消预订单',2,'com.retail.project.retail.controller.ReserveOrderController.cancelReserveOrder()','POST',1,'store1','门店','/retail/reserve/cancel','127.0.0.1','内网IP','{\"id\":33,\"cancelReason\":\"66\"}','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,'2025-04-30 15:56:36',56),(383,'审核取消预订单',2,'com.retail.project.retail.controller.InventoryOutOrderController.approveCancelReserve()','POST',1,'finance','总部','/retail/outorder/approve-cancel-reserve','127.0.0.1','内网IP','{\"id\":33,\"remark\":\"66\"}','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,'2025-04-30 15:56:50',64),(384,'商品预订',1,'com.retail.project.retail.controller.ReserveOrderController.reserve()','POST',1,'store1','门店','/retail/reserve/reserve','127.0.0.1','内网IP','{\"batchNumber\":\"R1745999845369\",\"depositAmount\":1980,\"depositType\":0,\"memberId\":2504277155,\"payType\":1,\"productId\":1,\"quantity\":1,\"remark\":\"\",\"salePrice\":4980,\"storeId\":1}','{\"msg\":\"商品预订成功\",\"code\":200}',0,NULL,'2025-04-30 15:57:25',35),(385,'审核出库单',2,'com.retail.project.retail.controller.InventoryOutOrderController.approve()','POST',1,'finance','总部','/retail/outorder/approve','127.0.0.1','内网IP','{\"id\":34,\"remark\":\"77\"}','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,'2025-04-30 15:57:35',36),(386,'预订单交货',2,'com.retail.project.retail.controller.ReserveOrderController.deliver()','POST',1,'store1','门店','/retail/reserve/deliver-order','127.0.0.1','内网IP','{\"balanceAmount\":3000.00,\"balancePayType\":2,\"remark\":\"77尾款已付\"}','{\"msg\":\"订单ID不能为空\",\"code\":500}',0,NULL,'2025-04-30 16:00:10',5),(387,'预订单交货',2,'com.retail.project.retail.controller.ReserveOrderController.deliver()','POST',1,'store1','门店','/retail/reserve/deliver-order','127.0.0.1','内网IP','{\"balanceAmount\":3000.00,\"balancePayType\":2,\"remark\":\"77尾款已付\"}','{\"msg\":\"订单ID不能为空\",\"code\":500}',0,NULL,'2025-04-30 16:00:20',1),(388,'预订单交货',2,'com.retail.project.retail.controller.ReserveOrderController.deliver()','POST',1,'store1','门店','/retail/reserve/deliver-order','127.0.0.1','内网IP','{\"balanceAmount\":3000.00,\"balancePayType\":2,\"orderId\":34,\"remark\":\"77交货\"}','{\"msg\":\"只有待交货状态的预订单才能进行交货操作\",\"code\":500}',0,NULL,'2025-04-30 16:10:33',30),(389,'预订单交货',2,'com.retail.project.retail.controller.ReserveOrderController.deliver()','POST',1,'store1','门店','/retail/reserve/deliver-order','127.0.0.1','内网IP','{\"balanceAmount\":3000.00,\"balancePayType\":2,\"orderId\":34,\"remark\":\"77交货\"}','{\"msg\":\"预订单交货成功\",\"code\":200}',0,NULL,'2025-04-30 16:50:27',121),(390,'商品预订',1,'com.retail.project.retail.controller.ReserveOrderController.reserve()','POST',1,'store1','门店','/retail/reserve/reserve','127.0.0.1','内网IP','{\"batchNumber\":\"R1746003611489\",\"depositAmount\":1999,\"depositType\":0,\"memberId\":2504277155,\"payType\":1,\"productId\":1,\"quantity\":1,\"remark\":\"\",\"salePrice\":4980,\"storeId\":1}','{\"msg\":\"商品预订成功\",\"code\":200}',0,NULL,'2025-04-30 17:00:11',66),(391,'审核出库单',2,'com.retail.project.retail.controller.InventoryOutOrderController.approve()','POST',1,'finance','总部','/retail/outorder/approve','127.0.0.1','内网IP','{\"id\":35,\"remark\":\"88\"}','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,'2025-04-30 17:00:23',57),(392,'预订单交货',2,'com.retail.project.retail.controller.ReserveOrderController.deliver()','POST',1,'store1','门店','/retail/reserve/deliver-order','127.0.0.1','内网IP','{\"balanceAmount\":2981.00,\"balancePayType\":3,\"orderId\":35,\"remark\":\"88交货\"}','{\"msg\":\"预订单交货成功\",\"code\":200}',0,NULL,'2025-04-30 17:00:52',91),(393,'驳回交货申请',2,'com.retail.project.retail.controller.InventoryOutOrderController.rejectDeliverReserve()','POST',1,'finance','总部','/retail/outorder/reject-deliver-reserve','127.0.0.1','内网IP','{\"id\":35,\"rejectReason\":\"88信息错误\"}','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,'2025-04-30 17:09:43',101),(394,'预订单交货',2,'com.retail.project.retail.controller.ReserveOrderController.deliver()','POST',1,'store1','门店','/retail/reserve/deliver-order','127.0.0.1','内网IP','{\"balanceAmount\":2981.00,\"balancePayType\":2,\"orderId\":35,\"remark\":\"88交货\"}','{\"msg\":\"预订单交货成功\",\"code\":200}',0,NULL,'2025-04-30 17:10:16',86),(395,'驳回交货申请',2,'com.retail.project.retail.controller.InventoryOutOrderController.rejectDeliverReserve()','POST',1,'finance','总部','/retail/outorder/reject-deliver-reserve','127.0.0.1','内网IP','{\"id\":35,\"rejectReason\":\"88信息错误1\"}','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,'2025-04-30 17:10:51',319),(396,'预订单交货',2,'com.retail.project.retail.controller.ReserveOrderController.deliver()','POST',1,'store1','门店','/retail/reserve/deliver-order','127.0.0.1','内网IP','{\"balanceAmount\":2981.00,\"balancePayType\":3,\"orderId\":35,\"remark\":\"88交货\"}','{\"msg\":\"预订单交货成功\",\"code\":200}',0,NULL,'2025-04-30 17:11:12',81),(397,'审核交货申请',2,'com.retail.project.retail.controller.InventoryOutOrderController.approveDeliverReserve()','POST',1,'finance','总部','/retail/outorder/approve-deliver-reserve','127.0.0.1','内网IP','{\"id\":35,\"remark\":\"88交货完成\"}','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,'2025-04-30 17:17:51',65),(398,'商品售出',1,'com.retail.project.retail.controller.OutOrderController.sale()','POST',1,'store1','门店','/retail/outorder/sale','127.0.0.1','内网IP','{\"memberId\":2504277155,\"payType\":1,\"quantity\":1,\"remark\":\"\",\"salePrice\":3300,\"stockId\":14}','{\"msg\":\"商品售出成功\",\"code\":200}',0,NULL,'2025-05-06 03:41:36',60),(399,'审核出库单',2,'com.retail.project.retail.controller.InventoryOutOrderController.approve()','POST',1,'finance','总部','/retail/outorder/approve','127.0.0.1','内网IP','{\"id\":36,\"remark\":\"1122\"}','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,'2025-05-06 03:42:03',69),(400,'申请退货',2,'com.retail.project.retail.controller.InventoryOutOrderController.applyReturn()','POST',1,'store1','门店','/retail/outorder/apply-return','127.0.0.1','内网IP','{\"id\":36,\"returnReason\":\"112233\"}','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,'2025-05-06 03:52:46',84),(401,'驳回退货申请',2,'com.retail.project.retail.controller.InventoryOutOrderController.rejectReturn()','POST',1,'finance','总部','/retail/outorder/reject-return','127.0.0.1','内网IP','{\"id\":36,\"rejectReason\":\"11223344\"}','{\"msg\":\"操作成功\",\"code\":200}',0,NULL,'2025-05-06 03:57:10',66);
/*!40000 ALTER TABLE `sys_oper_log` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `sys_post`
--

DROP TABLE IF EXISTS `sys_post`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `sys_post` (
  `post_id` bigint NOT NULL AUTO_INCREMENT COMMENT '岗位ID',
  `post_code` varchar(64) NOT NULL COMMENT '岗位编码',
  `post_name` varchar(50) NOT NULL COMMENT '岗位名称',
  `post_sort` int NOT NULL COMMENT '显示顺序',
  `status` char(1) NOT NULL COMMENT '状态（0正常 1停用）',
  `create_by` varchar(64) DEFAULT '' COMMENT '创建者',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(64) DEFAULT '' COMMENT '更新者',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  `remark` varchar(500) DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`post_id`)
) ENGINE=InnoDB AUTO_INCREMENT=5 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='岗位信息表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `sys_post`
--

LOCK TABLES `sys_post` WRITE;
/*!40000 ALTER TABLE `sys_post` DISABLE KEYS */;
INSERT INTO `sys_post` VALUES (1,'ceo','董事长',1,'0','admin','2025-04-25 08:01:00','',NULL,'');
/*!40000 ALTER TABLE `sys_post` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `sys_role`
--

DROP TABLE IF EXISTS `sys_role`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `sys_role` (
  `role_id` bigint NOT NULL AUTO_INCREMENT COMMENT '角色ID',
  `role_name` varchar(30) NOT NULL COMMENT '角色名称',
  `role_key` varchar(100) NOT NULL COMMENT '角色权限字符串',
  `role_sort` int NOT NULL COMMENT '显示顺序',
  `data_scope` char(1) DEFAULT '1' COMMENT '数据范围（1：全部数据权限 2：自定数据权限 3：本部门数据权限 4：本部门及以下数据权限）',
  `menu_check_strictly` tinyint(1) DEFAULT '1' COMMENT '菜单树选择项是否关联显示',
  `dept_check_strictly` tinyint(1) DEFAULT '1' COMMENT '部门树选择项是否关联显示',
  `status` char(1) NOT NULL COMMENT '角色状态（0正常 1停用）',
  `del_flag` char(1) DEFAULT '0' COMMENT '删除标志（0代表存在 2代表删除）',
  `create_by` varchar(64) DEFAULT '' COMMENT '创建者',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(64) DEFAULT '' COMMENT '更新者',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  `remark` varchar(500) DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`role_id`)
) ENGINE=InnoDB AUTO_INCREMENT=104 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='角色信息表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `sys_role`
--

LOCK TABLES `sys_role` WRITE;
/*!40000 ALTER TABLE `sys_role` DISABLE KEYS */;
INSERT INTO `sys_role` VALUES (1,'超级管理员','admin',1,'1',1,1,'0','0','admin','2025-04-25 08:01:00','',NULL,'超级管理员'),(2,'普通角色','common',2,'2',1,1,'0','2','admin','2025-04-25 08:01:00','admin','2025-04-25 08:31:06','普通角色'),(100,'管理员','manager',1,'1',1,1,'0','0','admin','2025-04-25 08:57:46','admin','2025-04-30 10:52:11',NULL),(101,'财务','finance',3,'1',1,1,'0','0','admin','2025-04-25 08:58:13','admin','2025-04-30 10:52:24',NULL),(102,'门店','store',4,'1',1,1,'0','0','admin','2025-04-25 08:58:51','admin','2025-04-30 10:52:45',NULL),(103,'供应商','channel',5,'1',1,1,'0','0','admin','2025-04-25 08:59:17','admin','2025-04-25 10:05:56',NULL);
/*!40000 ALTER TABLE `sys_role` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `sys_role_dept`
--

DROP TABLE IF EXISTS `sys_role_dept`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `sys_role_dept` (
  `role_id` bigint NOT NULL COMMENT '角色ID',
  `dept_id` bigint NOT NULL COMMENT '部门ID',
  PRIMARY KEY (`role_id`,`dept_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='角色和部门关联表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `sys_role_dept`
--

LOCK TABLES `sys_role_dept` WRITE;
/*!40000 ALTER TABLE `sys_role_dept` DISABLE KEYS */;
/*!40000 ALTER TABLE `sys_role_dept` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `sys_role_menu`
--

DROP TABLE IF EXISTS `sys_role_menu`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `sys_role_menu` (
  `role_id` bigint NOT NULL COMMENT '角色ID',
  `menu_id` bigint NOT NULL COMMENT '菜单ID',
  PRIMARY KEY (`role_id`,`menu_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='角色和菜单关联表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `sys_role_menu`
--

LOCK TABLES `sys_role_menu` WRITE;
/*!40000 ALTER TABLE `sys_role_menu` DISABLE KEYS */;
INSERT INTO `sys_role_menu` VALUES (100,1),(100,100),(100,101),(100,102),(100,103),(100,104),(100,105),(100,106),(100,107),(100,108),(100,500),(100,501),(100,1000),(100,1001),(100,1002),(100,1003),(100,1004),(100,1005),(100,1006),(100,1007),(100,1008),(100,1009),(100,1010),(100,1011),(100,1012),(100,1013),(100,1014),(100,1015),(100,1016),(100,1017),(100,1018),(100,1019),(100,1020),(100,1021),(100,1022),(100,1023),(100,1024),(100,1025),(100,1026),(100,1027),(100,1028),(100,1029),(100,1030),(100,1031),(100,1032),(100,1033),(100,1034),(100,1035),(100,1036),(100,1037),(100,1038),(100,1039),(100,1040),(100,1041),(100,1042),(100,1043),(100,1044),(100,1045),(100,2000),(100,2001),(100,2002),(100,2003),(100,2004),(100,2005),(100,2006),(100,2007),(100,2008),(100,2009),(100,2010),(100,2011),(100,2012),(100,2013),(100,2014),(100,2015),(100,2016),(100,2017),(100,2018),(100,2019),(100,2020),(100,2021),(100,2022),(100,2023),(100,2024),(100,2025),(100,2026),(100,2027),(100,2028),(100,2029),(100,2030),(100,2031),(100,2032),(100,2033),(100,2034),(100,2035),(100,2036),(100,2037),(100,2038),(100,2039),(100,2040),(100,2041),(100,2042),(100,2043),(100,2044),(100,2045),(100,2046),(100,2047),(100,2048),(100,2049),(100,2050),(100,2051),(100,2052),(100,2053),(100,2054),(100,2055),(100,2056),(100,2057),(100,2058),(100,2059),(100,2060),(100,2061),(100,2062),(100,2063),(100,2064),(100,2065),(100,2066),(101,2000),(101,2001),(101,2002),(101,2003),(101,2004),(101,2005),(101,2006),(101,2007),(101,2008),(101,2009),(101,2014),(101,2015),(101,2016),(101,2017),(101,2018),(101,2019),(101,2020),(101,2021),(101,2022),(101,2023),(101,2024),(101,2025),(101,2026),(101,2027),(101,2028),(101,2029),(101,2030),(101,2031),(101,2032),(101,2033),(101,2034),(101,2035),(101,2036),(101,2037),(101,2038),(101,2039),(101,2040),(101,2041),(101,2042),(101,2043),(101,2044),(101,2045),(101,2046),(101,2047),(101,2048),(101,2053),(101,2054),(101,2055),(101,2056),(101,2057),(101,2058),(101,2059),(101,2060),(101,2061),(101,2062),(101,2063),(101,2064),(101,2065),(101,2066),(102,2000),(102,2007),(102,2008),(102,2009),(102,2035),(102,2039),(102,2040),(102,2041),(102,2042),(102,2045),(102,2046),(102,2047),(102,2048),(102,2053),(102,2054),(102,2055),(102,2056),(102,2057),(102,2058),(102,2060),(102,2064),(102,2065),(102,2066),(103,2011),(103,2013);
/*!40000 ALTER TABLE `sys_role_menu` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `sys_user`
--

DROP TABLE IF EXISTS `sys_user`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `sys_user` (
  `user_id` bigint NOT NULL AUTO_INCREMENT COMMENT '用户ID',
  `dept_id` bigint DEFAULT NULL COMMENT '部门ID',
  `user_name` varchar(30) NOT NULL COMMENT '用户账号',
  `nick_name` varchar(30) NOT NULL COMMENT '用户昵称',
  `user_type` varchar(2) DEFAULT '00' COMMENT '用户类型（00系统用户）',
  `email` varchar(50) DEFAULT '' COMMENT '用户邮箱',
  `phonenumber` varchar(11) DEFAULT '' COMMENT '手机号码',
  `sex` char(1) DEFAULT '0' COMMENT '用户性别（0男 1女 2未知）',
  `avatar` varchar(100) DEFAULT '' COMMENT '头像地址',
  `password` varchar(100) DEFAULT '' COMMENT '密码',
  `status` char(1) DEFAULT '0' COMMENT '账号状态（0正常 1停用）',
  `del_flag` char(1) DEFAULT '0' COMMENT '删除标志（0代表存在 2代表删除）',
  `login_ip` varchar(128) DEFAULT '' COMMENT '最后登录IP',
  `login_date` datetime DEFAULT NULL COMMENT '最后登录时间',
  `create_by` varchar(64) DEFAULT '' COMMENT '创建者',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(64) DEFAULT '' COMMENT '更新者',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  `remark` varchar(500) DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`user_id`)
) ENGINE=InnoDB AUTO_INCREMENT=106 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='用户信息表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `sys_user`
--

LOCK TABLES `sys_user` WRITE;
/*!40000 ALTER TABLE `sys_user` DISABLE KEYS */;
INSERT INTO `sys_user` VALUES (1,101,'admin','Leo','00','<EMAIL>','15888888888','1','','$2a$10$7JB720yubVSZvUI0rEqK/.VqGOZTH.ulu33dHOiBE8ByOhJIrdAu2','0','0','127.0.0.1','2025-04-30 09:23:45','admin','2025-04-25 08:01:00','','2025-04-30 09:23:44','管理员'),(100,101,'boss','老板','00','','','0','','$2a$10$sD4XjkAyfSfKZZXzVYeV7.BTqzwIxQmfYbsj1ttoF9YZDUmQ5RnjC','0','0','127.0.0.1','2025-05-06 17:40:54','admin','2025-04-25 08:59:57','','2025-05-06 09:40:54',NULL),(101,101,'finance','财务经理','00','','','0','','$2a$10$gm7/qbCT/x8SmsdYjmYB/uEkSH6QApO6BV6URaZJYmwsabRgUT2na','0','0','127.0.0.1','2025-05-06 13:19:44','admin','2025-04-25 09:01:13','','2025-05-06 05:19:44',NULL),(102,200,'store1','门店1','00','','','0','','$2a$10$MsPtvrKtTe15pjWdmaSV1.jE3tH2C.u1FRg3xrVSHr4lymnN0omNq','0','0','127.0.0.1','2025-05-06 13:21:17','admin','2025-04-25 09:01:40','','2025-05-06 05:21:16',NULL),(103,200,'store2','门店2','00','','','0','','$2a$10$DPPnsBkdkTx5BQihzMMmwuMg9fCta9ltOzGwNtIaTtlCFocg7dG12','0','0','127.0.0.1','2025-04-28 14:26:33','admin','2025-04-25 09:02:11','','2025-04-28 14:26:32',NULL),(104,201,'channel1','供应商1','00','','','0','','$2a$10$38RkVq4Es8wMtDbsVNjeHuyF5NsfJiyhgeDHji1GWGRK.xNtEqeRe','0','0','',NULL,'admin','2025-04-25 09:02:44','',NULL,NULL),(105,201,'channel2','供应商2','00','','','0','','$2a$10$Cv1vwlYPtZOGsA4OEgh7I.KACH5gQOZBAAis2Y8WrfObpLUx7BZWC','0','0','',NULL,'admin','2025-04-25 09:03:27','',NULL,NULL);
/*!40000 ALTER TABLE `sys_user` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `sys_user_post`
--

DROP TABLE IF EXISTS `sys_user_post`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `sys_user_post` (
  `user_id` bigint NOT NULL COMMENT '用户ID',
  `post_id` bigint NOT NULL COMMENT '岗位ID',
  PRIMARY KEY (`user_id`,`post_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='用户与岗位关联表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `sys_user_post`
--

LOCK TABLES `sys_user_post` WRITE;
/*!40000 ALTER TABLE `sys_user_post` DISABLE KEYS */;
INSERT INTO `sys_user_post` VALUES (1,1);
/*!40000 ALTER TABLE `sys_user_post` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `sys_user_role`
--

DROP TABLE IF EXISTS `sys_user_role`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `sys_user_role` (
  `user_id` bigint NOT NULL COMMENT '用户ID',
  `role_id` bigint NOT NULL COMMENT '角色ID',
  PRIMARY KEY (`user_id`,`role_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='用户和角色关联表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `sys_user_role`
--

LOCK TABLES `sys_user_role` WRITE;
/*!40000 ALTER TABLE `sys_user_role` DISABLE KEYS */;
INSERT INTO `sys_user_role` VALUES (1,1),(100,100),(101,101),(102,102),(103,102),(104,103),(105,103);
/*!40000 ALTER TABLE `sys_user_role` ENABLE KEYS */;
UNLOCK TABLES;
/*!40103 SET TIME_ZONE=@OLD_TIME_ZONE */;

/*!40101 SET SQL_MODE=@OLD_SQL_MODE */;
/*!40014 SET FOREIGN_KEY_CHECKS=@OLD_FOREIGN_KEY_CHECKS */;
/*!40014 SET UNIQUE_CHECKS=@OLD_UNIQUE_CHECKS */;
/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40101 SET CHARACTER_SET_RESULTS=@OLD_CHARACTER_SET_RESULTS */;
/*!40101 SET COLLATION_CONNECTION=@OLD_COLLATION_CONNECTION */;
/*!40111 SET SQL_NOTES=@OLD_SQL_NOTES */;

-- Dump completed on 2025-05-19  2:21:34
